---
description: build
globs:
alwaysApply: false
---

# Chrome Extension Build & Obfuscation Guidelines

You are an expert in Chrome extension build systems and code obfuscation. When setting up build processes, follow these principles:

## Build System Architecture
- Use modern build tools: Webpack 5+, Vite, or Rollup for bundling
- Implement separate build configurations for development and production
- Use TypeScript for better type safety and development experience
- Set up proper source maps for debugging (dev only)
- Implement hot reload for development efficiency

## Webpack Configuration Template
```javascript
// webpack.config.js
const path = require('path');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const JavaScriptObfuscator = require('webpack-obfuscator');

module.exports = (env, argv) => {
  const isProduction = argv.mode === 'production';
  
  return {
    mode: isProduction ? 'production' : 'development',
    devtool: isProduction ? false : 'source-map',
    
    entry: {
      'background/service-worker': './src/background/service-worker.js',
      'popup/popup': './src/popup/popup.js',
      'content/content-script': './src/content/content-script.js',
      'options/options': './src/options/options.js'
    },
    
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: '[name].js',
      clean: true
    },
    
    optimization: {
      minimize: isProduction,
      minimizer: [
        new TerserPlugin({
          terserOptions: {
            compress: {
              drop_console: isProduction,
              drop_debugger: true,
              pure_funcs: ['console.log', 'console.info']
            },
            mangle: {
              reserved: ['chrome', 'browser'] // Preserve extension APIs
            }
          }
        })
      ]
    },
    
    plugins: [
      new CopyWebpackPlugin({
        patterns: [
          { from: 'manifest.json', to: 'manifest.json' },
          { from: 'src/popup/popup.html', to: 'popup/popup.html' },
          { from: 'src/options/options.html', to: 'options/options.html' },
          { from: 'src/assets', to: 'assets' }
        ]
      }),
      
      new MiniCssExtractPlugin({
        filename: '[name].css'
      }),
      
      // Apply obfuscation only in production
      ...(isProduction ? [
        new JavaScriptObfuscator({
          rotateStringArray: true,
          stringArray: true,
          stringArrayThreshold: 0.75,
          transformObjectKeys: true,
          unicodeEscapeSequence: false,
          // Preserve Chrome extension APIs
          reservedNames: [
            'chrome',
            'browser',
            'manifest',
            'runtime',
            'storage',
            'tabs',
            'scripting'
          ]
        }, ['background/service-worker.js']) // Don't obfuscate service worker too heavily
      ] : [])
    ]
  };
};
```

## Code Obfuscation Strategy
- **Level 1 (Light)**: For service workers and critical extension APIs
  - Variable name mangling only
  - Preserve chrome.* API calls
  - Keep debugging capabilities
  
- **Level 2 (Medium)**: For content scripts and utilities
  - String array obfuscation
  - Control flow flattening
  - Dead code elimination
  
- **Level 3 (Heavy)**: For business logic and proprietary algorithms
  - Full obfuscation with string encryption
  - Control flow obfuscation
  - Anti-debugging measures

## Build Scripts (package.json)
```json
{
  "scripts": {
    "dev": "webpack --mode=development --watch",
    "build": "webpack --mode=production",
    "build:analyze": "webpack-bundle-analyzer dist/stats.json",
    "clean": "rimraf dist",
    "lint": "eslint src/**/*.js",
    "test": "jest",
    "pack": "npm run build && web-ext build --source-dir=dist",
    "sign": "web-ext sign --source-dir=dist --api-key=$AMO_JWT_ISSUER --api-secret=$AMO_JWT_SECRET"
  }
}
```

## Environment-Specific Configurations
```javascript
// config/webpack.dev.js - Development configuration
module.exports = {
  devtool: 'eval-source-map',
  optimization: {
    minimize: false
  },
  plugins: [
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify('development'),
      'DEBUG': true
    })
  ]
};

// config/webpack.prod.js - Production configuration
module.exports = {
  devtool: false,
  optimization: {
    minimize: true,
    sideEffects: false,
    usedExports: true
  },
  plugins: [
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify('production'),
      'DEBUG': false
    })
  ]
};
```

## Advanced Obfuscation Techniques
```javascript
// Custom obfuscation for sensitive functions
const obfuscationConfig = {
  // API endpoint obfuscation
  stringArrayEncoding: ['base64'],
  stringArrayThreshold: 1,
  
  // Function name obfuscation
  identifierNamesGenerator: 'mangled-shuffled',
  
  // Control flow obfuscation
  controlFlowFlattening: true,
  controlFlowFlatteningThreshold: 0.75,
  
  // Anti-debugging
  debugProtection: true,
  debugProtectionInterval: true,
  
  // Performance vs Security balance
  compact: true,
  simplify: true
};
```

## Manifest Processing
```javascript
// Build-time manifest modification
const manifestProcessor = {
  development: {
    content_security_policy: {
      extension_pages: "script-src 'self' 'unsafe-eval'; object-src 'self'"
    }
  },
  production: {
    content_security_policy: {
      extension_pages: "script-src 'self'; object-src 'self'"
    }
  }
};
```

## Asset Optimization
- Compress images using imagemin-webpack-plugin
- Minify CSS with cssnano
- Tree-shake unused code
- Bundle splitting for better caching
- Implement proper chunk naming for cache busting

## Security Considerations
- Never obfuscate chrome API calls completely
- Preserve error handling for debugging
- Keep manifest.json readable for store review
- Maintain CSP compliance after obfuscation
- Test extension functionality after obfuscation

## Build Validation
```javascript
// Post-build validation script
const validateBuild = {
  checkManifest: () => {
    // Validate manifest.json structure
  },
  testAPICalls: () => {
    // Ensure chrome APIs still work
  },
  checkCSP: () => {
    // Validate Content Security Policy
  },
  verifyObfuscation: () => {
    // Check obfuscation effectiveness
  }
};
```

## Deployment Pipeline
1. **Pre-build**: Lint, test, security scan
2. **Build**: Webpack production build with obfuscation
3. **Post-build**: Validation, size analysis
4. **Package**: Create .zip for Chrome Web Store
5. **Deploy**: Automated upload to store (optional)

When setting up build processes, prioritize:
- Maintainability over extreme obfuscation
- Extension functionality preservation
- Store compliance requirements
- Development experience optimization
