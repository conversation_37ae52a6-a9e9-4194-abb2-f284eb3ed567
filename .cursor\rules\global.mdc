---
alwaysApply: true
---
# Chrome Extension Development Guidelines

You are an expert Chrome extension developer. When writing Chrome extension code, follow these principles:

## Architecture & Structure
- Use modular architecture with clear separation of concerns
- Organize code into logical directories: `/src`, `/popup`, `/content`, `/background`, `/options`
- Implement proper MVC/MVP patterns where applicable
- Use dependency injection for better testability

## Code Quality Standards
- Write clean, readable, and self-documenting code
- Use meaningful variable and function names in English
- Add comprehensive JSDoc comments for all functions and classes
- Follow consistent indentation (2 spaces) and formatting
- Implement proper error handling with try-catch blocks
- Use async/await instead of callbacks when possible

## Chrome Extension Best Practices
- Follow Manifest V3 specifications and best practices
- Use service workers instead of background pages
- Implement proper content security policy (CSP)
- Handle permissions gracefully with optional permissions when possible
- Use chrome.storage.local/sync for data persistence
- Implement proper message passing between contexts (popup ↔ content ↔ background)
- Handle tab lifecycle events properly

## Security & Performance
- Sanitize all user inputs and external data
- Use content scripts judiciously to minimize performance impact
- Implement proper CORS handling for external API calls
- Avoid eval() and inline scripts
- Use declarativeNetRequest for network modifications when possible

## Code Structure Template
```javascript
// Use this structure for main files:
/**
 * @fileoverview Brief description of the file's purpose
 * <AUTHOR> Name
 */

// Imports at top
import { ModuleName } from './path/to/module.js';

// Constants
const CONSTANTS = {
  API_BASE_URL: 'https://api.example.com',
  STORAGE_KEYS: {
    USER_SETTINGS: 'userSettings',
    CACHE: 'cache'
  }
};

// Main class/module
class ExtensionManager {
  constructor() {
    this.init();
  }

  /**
   * Initialize the extension
   * @returns {Promise<void>}
   */
  async init() {
    try {
      // Initialization logic
    } catch (error) {
      console.error('Initialization failed:', error);
    }
  }
}

// Export for modules
export { ExtensionManager };
```

## File Organization
```
chrome-extension/
├── manifest.json
├── src/
│   ├── background/
│   │   └── service-worker.js
│   ├── content/
│   │   ├── content-script.js
│   │   └── content-styles.css
│   ├── popup/
│   │   ├── popup.html
│   │   ├── popup.js
│   │   └── popup.css
│   ├── options/
│   │   ├── options.html
│   │   ├── options.js
│   │   └── options.css
│   ├── utils/
│   │   ├── storage.js
│   │   ├── messaging.js
│   │   └── dom-utils.js
│   └── assets/
│       └── icons/
└── README.md
```

## Common Patterns to Use
- Use factory patterns for creating different types of handlers
- Implement observer pattern for event management
- Use strategy pattern for different content script behaviors
- Apply singleton pattern for storage and configuration managers

## Error Handling
- Always wrap chrome API calls in try-catch blocks
- Provide meaningful error messages to users
- Log errors appropriately for debugging
- Implement graceful degradation for optional features

When generating code, ensure it follows these guidelines and provide explanations for architectural decisions.