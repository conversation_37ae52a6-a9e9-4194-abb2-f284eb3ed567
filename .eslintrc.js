module.exports = {
  env: {
    browser: true,
    es2021: true,
    webextensions: true,
    node: true
  },
  extends: [
    'eslint:recommended'
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  globals: {
    chrome: 'readonly'
  },
  rules: {
    // 代码风格
    'indent': ['error', 2],
    'linebreak-style': ['error', 'unix'],
    'quotes': ['error', 'single'],
    'semi': ['error', 'always'],
    
    // 最佳实践
    'no-console': 'warn',
    'no-unused-vars': 'warn',
    'no-undef': 'error',
    'no-debugger': 'warn',
    
    // ES6+
    'prefer-const': 'error',
    'arrow-spacing': 'error',
    'template-curly-spacing': 'error',
    
    // Chrome Extension特定
    'no-eval': 'error',
    'no-implied-eval': 'error'
  },
  overrides: [
    {
      files: ['src/background/**/*.js'],
      env: {
        'webextensions': true,
        'serviceworker': true
      }
    },
    {
      files: ['src/content/**/*.js'],
      env: {
        'browser': true,
        'webextensions': true
      }
    }
  ]
}; 