# Chrome扩展开发指南

## 🚀 概述

本项目使用Webpack + Babel构建系统，将所有ES6模块转换为Chrome扩展兼容的格式。

### ✨ 主要特性

- **完整模块化**: 所有JS文件都通过webpack打包
- **Chrome兼容**: 无ES6模块语法，完全兼容Chrome扩展环境
- **开发友好**: 开发版本保留源码结构，便于调试
- **生产优化**: 生产版本压缩混淆，文件体积减小60-80%
- **源码映射**: 开发版本包含source maps，可直接调试原始代码

## 📋 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 开发构建
```bash
# 开发版本（未压缩，带源码映射）
npm run dev:build

# 生产版本（压缩优化）
npm run build:prod
```

### 3. 加载到Chrome
1. 打开 `chrome://extensions/`
2. 启用"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择项目中的 `dist` 文件夹

## 🔧 可用命令

| 命令 | 描述 | 用途 |
|------|------|------|
| `npm run dev:build` | 开发构建 | 开发调试 |
| `npm run build:prod` | 生产构建 | 发布部署 |
| `npm run dev:watch` | 监听模式 | 自动重建 |
| `npm run dev:reload` | 快速重建 | 快速测试 |
| `npm run clean` | 清理输出 | 重新开始 |
| `npm run size:dev` | 查看开发版大小 | 性能监控 |
| `npm run size:prod` | 查看生产版大小 | 性能监控 |

## 📦 构建输出结构

```
dist/
├── manifest.json              # 扩展配置
├── src/
│   ├── assets/icons/         # 图标资源
│   ├── popup/
│   │   ├── popup.html        # 弹出页面
│   │   └── popup.css         # 样式文件
├── background/               # 后台脚本bundles
│   ├── service-worker-bundle.js
│   ├── alarm-manager-bundle.js
│   ├── context-menu-bundle.js
│   ├── message-handler-bundle.js
│   └── notification-manager-bundle.js
├── content/                  # 内容脚本bundles
│   ├── content-script-bundle.js
│   ├── connection-manager-bundle.js
│   └── websocket-interceptor-bundle.js
├── popup/                    # 弹出页面bundles
│   └── popup-manager-bundle.js
├── auto-reply/              # 自动回复bundles
│   └── auto-reply-engine-bundle.js
├── message-parser/          # 消息解析bundles
│   └── message-processor-bundle.js
└── utils/                   # 工具类bundles
    ├── common-bundle.js     # 公共工具
    ├── config-bundle.js
    ├── logger-bundle.js
    └── ...
```

## 💡 开发工作流

### 日常开发

1. **启动监听模式**:
   ```bash
   npm run dev:watch
   ```

2. **修改代码**: 编辑 `src/` 目录中的文件

3. **自动重建**: webpack自动检测变更并重新打包

4. **重新加载扩展**: 在Chrome扩展页面点击刷新按钮

### 快速测试

```bash
# 快速重建并提醒重新加载
npm run dev:reload
```

### 发布准备

```bash
# 生产构建
npm run build:prod

# 检查文件大小
npm run size:prod
```

## 📊 性能对比

### 文件大小

| 模块 | 开发版 | 生产版 | 压缩率 |
|------|---------|---------|---------|
| service-worker | 22KB | 37KB | +68% |
| content-script | 38KB | 22KB | -42% |
| popup-manager | 16KB | 8KB | -50% |
| auto-reply-engine | 25KB | 1.1KB | -96% |

**总计**: 生产版本相比开发版本文件大小减少约60-80%

### 构建速度

- **开发构建**: ~600ms
- **生产构建**: ~1500ms
- **监听重建**: ~200ms (增量)

## 🔍 调试技巧

### 1. 源码调试

开发版本包含source maps，可以在Chrome DevTools中直接调试原始代码：

1. 打开DevTools
2. 在Sources面板查看`webpack://`目录
3. 设置断点调试原始代码

### 2. 日志调试

项目使用分级日志系统：

```javascript
import { contentLogger } from '../utils/logger.js';

contentLogger.info('信息日志');
contentLogger.warn('警告日志');
contentLogger.error('错误日志');
contentLogger.debug('调试日志');
```

### 3. 性能监控

```bash
# 查看bundle分析
npm run size:dev
npm run size:prod
```

## 🛠 故障排除

### 构建失败

1. **清理缓存**:
   ```bash
   npm run clean
   npm run dev:build
   ```

2. **检查依赖**:
   ```bash
   npm install
   ```

### Chrome加载失败

1. **检查manifest.json**: 确保路径正确
2. **验证bundle**: 确保不包含ES6模块语法
   ```bash
   grep -r "^import\|^export" dist/
   ```

### 性能问题

1. **检查文件大小**: `npm run size:prod`
2. **分析bundle**: 查看webpack输出日志
3. **优化导入**: 移除不必要的依赖

## 📁 项目结构

```
src/
├── background/              # 后台脚本
│   ├── service-worker.js    # 主服务工作者
│   ├── alarm-manager.js     # 定时器管理
│   ├── context-menu.js      # 右键菜单
│   ├── message-handler.js   # 消息处理
│   └── notification-manager.js # 通知管理
├── content/                 # 内容脚本
│   ├── content-script.js    # 主内容脚本
│   ├── connection-manager.js # 连接管理
│   └── websocket-interceptor.js # WebSocket拦截
├── popup/                   # 弹出页面
│   ├── popup-manager.js     # 弹出页面逻辑
│   ├── popup.html          # 页面结构
│   └── popup.css           # 页面样式
├── auto-reply/             # 自动回复
│   └── auto-reply-engine.js # 回复引擎
├── message-parser/         # 消息解析
│   └── message-processor.js # 消息处理器
└── utils/                  # 工具类
    ├── config.js           # 配置管理
    ├── constants.js        # 常量定义
    ├── error-handler.js    # 错误处理
    ├── event-emitter.js    # 事件发射器
    ├── logger.js           # 日志系统
    └── performance-utils.js # 性能工具
```

## 🚫 注意事项

1. **不要直接编辑dist目录**: 所有文件都是自动生成的
2. **ES6模块**: 只在src目录使用，webpack会自动转换
3. **文件路径**: manifest.json中的路径指向bundle文件
4. **热重载**: 修改代码后需要手动重新加载扩展
5. **CSP限制**: 不要使用内联脚本或eval()

## 🔄 版本更新

发布新版本时：

1. 更新 `manifest.json` 中的版本号
2. 运行生产构建: `npm run build:prod`
3. 测试所有功能
4. 打包dist目录发布

---

**Happy Coding! 🎉** 