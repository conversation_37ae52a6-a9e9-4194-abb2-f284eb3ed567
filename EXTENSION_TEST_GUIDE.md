# 🔧 携程IM拦截器 - 扩展测试指南

## 📋 问题修复说明

本次修复解决了以下问题：
1. **Content Script注入范围过窄** - 扩展了manifest.json中的域名匹配范围
2. **消息通道过早关闭** - 改进了popup与content script之间的通信机制
3. **错误处理不完善** - 增强了异常处理和调试信息

## 🚀 测试步骤

### 1. 重新加载扩展
```bash
# 在项目根目录执行构建
npm run dev:build

# 然后在Chrome扩展管理页面重新加载扩展
```

### 2. 测试网站列表
扩展现在支持以下域名：
- `*.ctrip.com` (携程主站)
- `*.trip.com` (Trip.com国际站)
- `ctrip.com` (根域名)
- `trip.com` (根域名)
- `*.ctripbiz.com` (携程商旅)
- `*.tripbiz.com` (Trip商旅)

### 3. 验证步骤

#### 步骤1: 检查扩展状态
1. 打开任意携程相关网站
2. 点击扩展图标打开popup
3. 检查是否还显示"扩展未完全加载"错误

#### 步骤2: 测试Tab切换
1. 在popup中点击不同的tab（状态、连接、消息、调试、设置）
2. 确认tab能正常切换显示内容

#### 步骤3: 检查连接状态
1. 点击"状态"tab
2. 查看连接状态是否正常显示
3. 不应再出现"通信错误: The message port closed before a response was received"

## 🔍 调试信息

### 浏览器控制台日志
打开开发者工具(F12)，在Console中查看：
- `[CtripIM Content]` - Content Script日志
- `[CtripIM Popup]` - Popup日志
- `[CtripIM Background]` - Background Script日志

### 常见日志信息
```
✅ 正常情况:
[CtripIM Content] ✅ 检测到目标网站，开始初始化...
[CtripIM Content] 📨 收到消息: {source: "ctrip-im-popup", action: "get-status"}
[CtripIM Content] 📤 发送响应: {success: true, data: {...}}
[CtripIM Popup] ✅ 收到有效响应: {success: true, data: {...}}

❌ 异常情况:
[CtripIM Popup] ❌ Chrome runtime error: Could not establish connection
[CtripIM Popup] ⏰ 消息超时: get-status (10000ms)
```

## 🛠️ 故障排除

### 如果仍然显示"扩展未完全加载"
1. **检查网站域名**: 确保在支持的域名下使用
2. **刷新页面**: 按F5刷新当前页面
3. **重新加载扩展**: 在chrome://extensions/页面重新加载扩展
4. **检查权限**: 确保扩展有访问当前网站的权限

### 如果Tab切换不工作
1. **检查CSS**: 确保popup.css包含tab-content样式
2. **检查JS**: 确保popup-manager.js正确加载
3. **查看控制台**: 检查是否有JavaScript错误

### 如果连接状态异常
1. **检查Content Script**: 在控制台查看是否有content script错误
2. **检查网络**: 确保网站正常加载
3. **检查WebSocket**: 查看是否有WebSocket连接

## 📊 性能优化

本次修复还包含以下性能优化：
- 增加消息超时时间从5秒到10秒
- 改进错误处理，避免消息通道泄漏
- 增强调试日志，便于问题定位
- 优化异步操作处理

## 🔄 后续改进建议

1. **动态域名检测**: 考虑支持更多携程相关域名
2. **离线模式**: 增加离线状态下的基本功能
3. **性能监控**: 添加性能指标收集
4. **用户反馈**: 增加用户反馈收集机制

---

**如果问题仍然存在，请提供以下信息：**
- 浏览器版本
- 当前网站URL
- 控制台错误日志
- 扩展版本信息
