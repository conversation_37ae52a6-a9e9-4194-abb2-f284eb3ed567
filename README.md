# 🎯 携程IM拦截器 (Ctrip IM Interceptor)

[![Version](https://img.shields.io/badge/version-1.0.3-blue.svg)](https://github.com/your-username/ctrip-im-helper)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Chrome Extension](https://img.shields.io/badge/chrome-extension-yellow.svg)](https://developer.chrome.com/docs/extensions/)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](BUILD.md)
[![Webpack](https://img.shields.io/badge/webpack-5.99.9-blue.svg)](webpack.config.cjs)

> 🚀 **专业级Chrome扩展** - 智能拦截携程IM WebSocket通信，提供自动回复和消息分析功能

一个基于现代Web技术栈构建的Chrome扩展，专门用于拦截和分析携程网站的IM WebSocket通信。采用Webpack + Babel构建系统，支持ES6模块化开发，提供完整的消息处理和自动回复解决方案。

## � 目录

- [✨ 核心特性](#-核心特性)
- [🏗️ 技术架构](#️-技术架构)
- [🚀 快速开始](#-快速开始)
- [🎮 使用指南](#-使用指南)
- [🔧 开发指南](#-开发指南)
- [🐛 故障排除](#-故障排除)
- [🤝 贡献指南](#-贡献指南)
- [📄 开源许可](#-开源许可)
- [📞 联系与支持](#-联系与支持)

## ✨ 核心特性

### 🔍 **WebSocket智能拦截**
- **🎯 精准识别**: 自动检测携程IM的WebSocket连接 (`imvendor.ctrip.com`)
- **⚡ 实时监控**: 零延迟监控所有消息收发状态
- **🔗 连接管理**: 智能管理多个WebSocket连接实例
- **🚀 性能优化**: 低开销拦截机制，不影响原始通信性能

### 📝 **多格式消息解析**
- **📋 XMPP协议**: 完整支持XMPP消息格式解析和处理
- **📊 JSON数据**: 智能解析结构化JSON格式消息
- **💬 文本消息**: 高效处理纯文本通信内容
- **🔧 可扩展架构**: 模块化解析器设计，支持自定义格式

### 🤖 **智能自动回复系统**
- **🔍 关键词匹配**: 基于关键词的智能回复触发机制
- **📝 模板引擎**: 丰富的回复模板库和自定义模板支持
- **👥 用户管理**: 黑白名单机制，防止垃圾回复
- **⚙️ 策略配置**: 多种回复模式和延迟策略

### 📊 **数据分析与监控**
- **📈 实时统计**: 消息数量、回复率、连接状态等实时统计
- **📚 历史记录**: 完整的消息历史追踪和导出功能
- **🔧 性能监控**: 扩展性能指标和内存使用情况监控
- **🐛 错误追踪**: 全面的错误处理、报告和调试支持

## 🏗️ 技术架构

### �️ **技术栈**
- **构建系统**: Webpack 5 + Babel (ES6 → Chrome兼容)
- **开发语言**: ES6+ JavaScript (模块化开发)
- **扩展框架**: Chrome Extension Manifest V3
- **目标环境**: Chrome 88+ / Chromium内核浏览器

### �📁 **项目结构**
```
ctrip-im-helper/
├── 📄 manifest.json           # Chrome扩展配置
├── 📦 package.json            # 项目依赖和脚本
├── ⚙️ webpack.config.cjs      # Webpack构建配置
├── 📚 docs/                   # 项目文档
├── 🔨 scripts/                # 构建脚本
├── 📦 dist/                   # 构建输出目录
│   ├── manifest.json          # 扩展配置文件
│   ├── background/            # 后台脚本bundles
│   ├── content/               # 内容脚本bundles
│   ├── popup/                 # 弹窗页面bundles
│   ├── auto-reply/            # 自动回复bundles
│   ├── message-parser/        # 消息解析bundles
│   └── utils/                 # 工具类bundles
└── 📂 src/                    # 源代码目录
    ├── 🔧 background/         # 后台服务模块
    │   ├── service-worker.js      # 主服务工作者
    │   ├── message-handler.js     # 消息处理器
    │   ├── alarm-manager.js       # 定时任务管理
    │   ├── notification-manager.js # 通知管理
    │   └── context-menu.js        # 上下文菜单
    ├── 📱 content/            # 内容脚本模块
    │   ├── content-script.js      # 主内容脚本
    │   ├── websocket-interceptor.js # WebSocket拦截器
    │   └── connection-manager.js   # 连接管理器
    ├── 🎨 popup/              # 弹窗模块
    │   ├── popup-manager.js       # 弹窗管理器
    │   ├── popup.html             # 弹窗页面
    │   └── popup.css              # 弹窗样式
    ├── 📝 message-parser/     # 消息解析模块
    │   └── message-processor.js   # 消息处理器
    ├── 🤖 auto-reply/         # 自动回复模块
    │   └── auto-reply-engine.js   # 自动回复引擎
    └── 🛠️ utils/              # 工具模块
        ├── logger.js              # 分级日志系统
        ├── config.js              # 配置管理
        ├── constants.js           # 常量定义
        ├── event-emitter.js       # 事件发射器
        ├── performance-utils.js   # 性能优化工具
        └── error-handler.js       # 错误处理系统
```

### 🎨 **设计模式**
- **🔄 观察者模式**: 事件驱动的组件通信机制
- **🔧 策略模式**: 可插拔的消息解析器和回复策略
- **🏭 工厂模式**: 统一的对象创建和管理机制
- **🎯 单例模式**: 全局配置和状态管理

### ⚡ **性能优化策略**
- **💾 智能缓存**: 减少重复计算和网络请求
- **📦 批处理机制**: 消息批量处理提升吞吐量
- **🎛️ 节流防抖**: UI更新和事件处理优化
- **🧹 内存管理**: 自动清理和内存泄漏监控
- **📊 Bundle优化**: Webpack代码分割和压缩优化

## 🚀 快速开始

### 📋 **系统要求**
- **浏览器**: Chrome 88+ 或其他Chromium内核浏览器
- **扩展支持**: Chrome Extension Manifest V3
- **开发环境**: Node.js 14+ (仅开发时需要)
- **目标网站**: 携程IM系统 (`imvendor.ctrip.com`)

### 💻 **安装方式**

#### 🔧 开发者安装 (推荐)
```bash
# 1. 克隆项目
git clone https://github.com/your-username/ctrip-im-helper.git
cd ctrip-im-helper

# 2. 安装依赖
npm install

# 3. 构建扩展
npm run build:dev    # 开发版本
# 或
npm run build:prod   # 生产版本

# 4. 加载到Chrome
# 打开 chrome://extensions/
# 启用"开发者模式"
# 点击"加载已解压的扩展程序"
# 选择项目中的 dist 文件夹
```

#### 📦 直接安装 (预构建版本)
1. 下载最新的 [Release版本](https://github.com/your-username/ctrip-im-helper/releases)
2. 解压到本地目录
3. 在Chrome扩展页面加载 `dist` 文件夹

#### 🏪 商店安装 (计划中)
> 📅 Chrome Web Store版本正在审核中，敬请期待

### ⚙️ **配置说明**

#### 基本配置
```javascript
{
  "interceptor": {
    "enabled": true,                    // 🔛 启用WebSocket拦截
    "logLevel": "info",                // 📝 日志级别 (debug/info/warn/error)
    "maxHistorySize": 1000,            // 📚 最大历史记录数量
    "autoReconnect": true              // 🔄 自动重连机制
  },
  "autoReply": {
    "enabled": false,                  // 🤖 启用自动回复
    "mode": "keywords",                // 📋 回复模式 (keywords/template/ai)
    "delay": { "min": 1000, "max": 3000 }, // ⏱️ 回复延迟范围(ms)
    "maxRepliesPerUser": 5             // 👥 每用户最大回复次数
  },
  "ui": {
    "theme": "auto",                   // 🎨 界面主题 (light/dark/auto)
    "notifications": true,             // 🔔 桌面通知
    "soundAlerts": false               // 🔊 声音提醒
  }
}
```

## 🎮 使用指南

### � **快速上手**

#### 第一步：激活扩展
1. 访问携程IM页面 (`imvendor.ctrip.com`)
2. 点击浏览器工具栏中的扩展图标
3. 确认WebSocket连接状态为"已连接"

#### 第二步：基础配置
1. 在弹窗中点击"设置"按钮
2. 根据需要启用/禁用各项功能
3. 保存配置并刷新页面

### 📋 **核心功能详解**

#### 🔍 **1. WebSocket监控**
- **🔗 连接检测**: 自动识别并连接携程IM的WebSocket
- **📊 实时状态**: 显示连接状态、消息计数、延迟等信息
- **📈 流量监控**: 实时监控上行/下行消息流量
- **🔄 重连机制**: 连接断开时自动重连

#### 📝 **2. 消息分析**
- **🔍 内容解析**: 智能解析XMPP、JSON等多种消息格式
- **📚 历史记录**: 完整保存消息历史，支持搜索和过滤
- **📤 数据导出**: 支持导出为JSON、CSV等格式
- **🏷️ 消息分类**: 按类型、用户、时间等维度分类显示

#### 🤖 **3. 智能自动回复**
- **🎯 关键词匹配**: 基于关键词触发自动回复
- **📝 模板管理**: 创建和管理回复模板库
- **⏱️ 延迟控制**: 设置回复延迟，模拟人工回复
- **👥 用户管理**: 黑白名单机制，精确控制回复对象
- **📊 回复统计**: 统计回复成功率和用户反馈

#### 📊 **4. 性能监控**
- **💾 内存使用**: 实时监控扩展内存占用
- **⚡ 性能指标**: CPU使用率、响应时间等关键指标
- **🐛 错误追踪**: 自动捕获和记录运行时错误
- **📈 趋势分析**: 性能数据的历史趋势分析

### 🎨 **界面导览**

#### 🏠 主控制面板
```
┌─────────────────────────────────┐
│ 🔗 连接状态: ✅ 已连接           │
│ 📊 消息统计: ↑123 ↓456         │
│ ⚡ 延迟: 45ms                   │
│                                 │
│ [🔛 启用拦截] [🤖 自动回复]     │
│ [📊 统计面板] [⚙️ 设置]         │
└─────────────────────────────────┘
```

#### ⚙️ 设置面板
- **🔧 拦截配置**: 日志级别、历史记录限制、重连设置
- **🤖 回复设置**: 关键词规则、模板管理、延迟配置
- **🎨 界面设置**: 主题选择、通知设置、快捷键配置
- **🔒 隐私设置**: 数据保留策略、敏感信息过滤

#### 📊 统计面板
- **📈 实时图表**: 消息流量、回复率等实时图表
- **📋 详细日志**: 分级日志显示，支持过滤和搜索
- **🔍 调试信息**: 开发者调试信息和性能分析

## 🔧 开发指南

### 🛠️ **开发环境搭建**

#### 环境要求
- **Node.js**: 14.0+ (推荐使用LTS版本)
- **npm**: 6.0+ 或 **yarn**: 1.22+
- **Git**: 用于版本控制
- **Chrome**: 88+ 用于测试

#### 快速开始
```bash
# 1. 克隆项目
git clone https://github.com/your-username/ctrip-im-helper.git
cd ctrip-im-helper

# 2. 安装依赖
npm install

# 3. 开发构建 (带源码映射，便于调试)
npm run dev:build

# 4. 生产构建 (优化压缩)
npm run build:prod

# 5. 监听模式 (自动重建)
npm run dev:watch

# 6. 查看构建信息
npm run info
```

### 📋 **可用脚本**

| 脚本命令 | 功能描述 | 使用场景 |
|---------|----------|----------|
| `npm run dev:build` | 开发构建 | 日常开发调试 |
| `npm run build:prod` | 生产构建 | 发布部署 |
| `npm run dev:watch` | 监听重建 | 开发时自动构建 |
| `npm run dev:reload` | 快速重建 | 快速测试修改 |
| `npm run clean` | 清理输出 | 重新开始构建 |
| `npm run size:dev` | 开发版大小 | 性能分析 |
| `npm run size:prod` | 生产版大小 | 体积优化 |

### 📝 **代码规范**

#### ES6模块化开发
```javascript
// 📁 src/utils/my-module.js
import { Logger } from './logger.js';
import { EventEmitter } from './event-emitter.js';

/**
 * 示例模块类
 * @extends EventEmitter
 */
export class MyModule extends EventEmitter {
  constructor(options = {}) {
    super();
    this.logger = new Logger('MyModule');
    this.config = { ...this.defaultConfig, ...options };
  }

  get defaultConfig() {
    return {
      enabled: true,
      timeout: 5000
    };
  }

  async initialize() {
    try {
      this.logger.info('模块初始化开始');
      // 初始化逻辑
      this.emit('initialized');
    } catch (error) {
      this.logger.error('初始化失败', error);
      throw error;
    }
  }
}

export default MyModule;
```

#### 错误处理最佳实践
```javascript
import { globalErrorHandler, ErrorTypes, ErrorSeverity } from '../utils/error-handler.js';

class WebSocketManager {
  async connect(url) {
    try {
      this.ws = new WebSocket(url);
      this.setupEventHandlers();
    } catch (error) {
      // 统一错误处理
      globalErrorHandler.handleError(error, {
        type: ErrorTypes.WEBSOCKET,
        severity: ErrorSeverity.HIGH,
        context: {
          operation: 'connect',
          url: url,
          timestamp: Date.now()
        }
      });
      throw error;
    }
  }

  setupEventHandlers() {
    this.ws.onopen = () => this.logger.info('WebSocket连接成功');
    this.ws.onerror = (error) => {
      globalErrorHandler.handleError(error, {
        type: ErrorTypes.WEBSOCKET,
        severity: ErrorSeverity.MEDIUM,
        context: { operation: 'websocket_error' }
      });
    };
  }
}
```

#### 性能优化技巧
```javascript
import PerformanceUtils from '../utils/performance-utils.js';

class MessageProcessor {
  constructor() {
    // 🚀 使用缓存减少重复计算
    this.cache = new PerformanceUtils.Cache(100, 60000); // 100条记录，60秒过期

    // 🎛️ 节流优化UI更新
    this.throttledUpdateUI = PerformanceUtils.throttle(
      this.updateUI.bind(this),
      100 // 100ms内最多执行一次
    );

    // 📦 批处理消息
    this.batchProcessor = new PerformanceUtils.BatchProcessor({
      batchSize: 10,
      flushInterval: 1000,
      processor: this.processBatch.bind(this)
    });
  }

  processMessage(message) {
    // 检查缓存
    const cacheKey = `msg_${message.id}`;
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    // 处理消息
    const result = this.doProcessMessage(message);

    // 缓存结果
    this.cache.set(cacheKey, result);

    // 批量处理
    this.batchProcessor.add(result);

    return result;
  }
}
```

### 🧪 **测试与调试**

#### 单元测试示例
```javascript
// 📁 tests/websocket-interceptor.test.js
import { WebSocketInterceptor } from '../src/content/websocket-interceptor.js';

describe('WebSocketInterceptor', () => {
  let interceptor;

  beforeEach(() => {
    interceptor = new WebSocketInterceptor();
  });

  it('应该正确识别携程IM的WebSocket连接', () => {
    const ctripUrls = [
      'wss://imvendor.ctrip.com/ws-xmpp',
      'wss://im3.ctrip.com/websocket'
    ];

    ctripUrls.forEach(url => {
      expect(interceptor.isCtripIMWebSocket(url)).toBe(true);
    });
  });

  it('应该拒绝非携程的WebSocket连接', () => {
    const nonCtripUrls = [
      'wss://example.com/ws',
      'wss://other-site.com/socket'
    ];

    nonCtripUrls.forEach(url => {
      expect(interceptor.isCtripIMWebSocket(url)).toBe(false);
    });
  });
});
```

#### 集成测试示例
```javascript
// 📁 tests/message-flow.test.js
import { MessageProcessor } from '../src/message-parser/message-processor.js';

describe('消息处理流程', () => {
  let processor;

  beforeEach(() => {
    processor = new MessageProcessor();
  });

  it('应该完整处理XMPP消息', async () => {
    const mockXMPPMessage = {
      type: 'xmpp',
      data: '<message from="user1" to="user2"><body>Hello</body></message>'
    };

    const result = await processor.processMessage(mockXMPPMessage);

    expect(result).toBeDefined();
    expect(result.from).toBe('user1');
    expect(result.to).toBe('user2');
    expect(result.body).toBe('Hello');
  });
});
```

#### 性能测试
```javascript
// 📁 tests/performance.test.js
import PerformanceUtils from '../src/utils/performance-utils.js';

describe('性能测试', () => {
  it('缓存性能应该满足要求', () => {
    const cache = new PerformanceUtils.Cache(1000, 60000);

    // 测试写入性能
    const startTime = performance.now();
    for (let i = 0; i < 1000; i++) {
      cache.set(`key_${i}`, `value_${i}`);
    }
    const writeTime = performance.now() - startTime;

    expect(writeTime).toBeLessThan(100); // 应该在100ms内完成

    // 测试读取性能
    const readStartTime = performance.now();
    for (let i = 0; i < 1000; i++) {
      cache.get(`key_${i}`);
    }
    const readTime = performance.now() - readStartTime;

    expect(readTime).toBeLessThan(50); // 应该在50ms内完成
  });
});
```

## 🐛 故障排除

### ⚠️ **常见问题**

#### 🔗 WebSocket拦截问题
| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 拦截器未激活 | 未访问目标网站 | 确保在 `imvendor.ctrip.com` 使用 |
| 连接状态异常 | 页面未完全加载 | 刷新页面重新激活拦截器 |
| 权限不足 | 扩展权限设置错误 | 检查manifest.json权限配置 |

#### 🤖 自动回复问题
| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 回复不触发 | 功能未启用 | 在设置中启用自动回复功能 |
| 关键词不匹配 | 规则配置错误 | 检查关键词匹配规则设置 |
| 用户被屏蔽 | 在黑名单中 | 检查用户黑白名单配置 |

#### ⚡ 性能问题
| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 内存占用过高 | 历史记录过多 | 定期清理消息历史 |
| 响应速度慢 | 日志级别过高 | 调整为ERROR或WARN级别 |
| 扩展卡顿 | 资源冲突 | 禁用后重新启用扩展 |

### 🔍 **调试技巧**

#### Chrome开发者工具
```javascript
// 1. 打开开发者工具 (F12)
// 2. 在Console中执行以下命令进行调试

// 查看扩展状态
console.log('扩展状态:', window.ctripIMHelper?.status);

// 查看WebSocket连接
console.log('WebSocket连接:', window.ctripIMHelper?.connections);

// 查看消息历史
console.log('消息历史:', window.ctripIMHelper?.messageHistory);

// 查看性能指标
console.log('性能指标:', window.ctripIMHelper?.performance);
```

#### 日志分析工具
```javascript
// 📁 调试工具函数
class DebugUtils {
  // 导出完整日志
  static exportLogs() {
    const logs = logger.getHistory();
    const blob = new Blob([JSON.stringify(logs, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ctrip-im-helper-logs-${Date.now()}.json`;
    a.click();
  }

  // 性能报告
  static generatePerformanceReport() {
    return {
      memory: PerformanceUtils.MemoryMonitor.getCurrentUsage(),
      timing: PerformanceUtils.TimingMonitor.getMetrics(),
      errors: globalErrorHandler.getErrorSummary(),
      cache: PerformanceUtils.Cache.getStats()
    };
  }

  // 健康检查
  static healthCheck() {
    const report = {
      websocket: window.ctripIMHelper?.websocket?.readyState === 1,
      autoReply: window.ctripIMHelper?.autoReply?.enabled,
      memory: PerformanceUtils.MemoryMonitor.getCurrentUsage() < 50 * 1024 * 1024, // 50MB
      errors: globalErrorHandler.getErrorCount() < 10
    };

    console.table(report);
    return report;
  }
}

// 在Console中使用
// DebugUtils.healthCheck();
// DebugUtils.exportLogs();
```

## 🤝 贡献指南

### 💡 **如何参与贡献**

我们欢迎所有形式的贡献！无论您是开发者、设计师还是用户，都可以为项目做出贡献。

#### 🚀 快速贡献流程
```bash
# 1. Fork项目到您的GitHub账户
# 2. 克隆您的Fork
git clone https://github.com/YOUR_USERNAME/ctrip-im-helper.git
cd ctrip-im-helper

# 3. 创建功能分支
git checkout -b feature/your-amazing-feature

# 4. 进行开发
npm install
npm run dev:build

# 5. 提交更改
git add .
git commit -m "feat: 添加您的新功能"

# 6. 推送到您的Fork
git push origin feature/your-amazing-feature

# 7. 创建Pull Request
```

### 📋 **贡献类型**

| 类型 | 描述 | 标签 |
|------|------|------|
| 🐛 **Bug修复** | 修复现有功能的问题 | `bug` |
| ✨ **新功能** | 添加新的功能特性 | `enhancement` |
| 📚 **文档改进** | 改进README、注释、文档 | `documentation` |
| 🎨 **UI/UX优化** | 界面设计和用户体验改进 | `ui/ux` |
| ⚡ **性能优化** | 提升性能和效率 | `performance` |
| 🧪 **测试完善** | 添加或改进测试用例 | `testing` |
| 🔧 **工具改进** | 构建工具、开发工具优化 | `tooling` |

### 📝 **代码规范**

#### 提交信息规范
```bash
# 格式: <type>(<scope>): <description>
feat(websocket): 添加自动重连机制
fix(popup): 修复设置保存问题
docs(readme): 更新安装说明
style(ui): 优化弹窗界面布局
perf(cache): 优化消息缓存性能
test(unit): 添加WebSocket拦截器测试
```

#### 代码质量要求
- ✅ **ES6+语法**: 使用现代JavaScript特性
- ✅ **模块化**: 遵循ES6模块化规范
- ✅ **注释完整**: 关键逻辑添加中文注释
- ✅ **错误处理**: 完善的异常处理机制
- ✅ **性能考虑**: 避免内存泄漏和性能问题
- ✅ **测试覆盖**: 为新功能编写测试用例

#### Pull Request检查清单
- [ ] 代码通过本地构建测试
- [ ] 添加了必要的注释和文档
- [ ] 更新了相关的README部分
- [ ] 测试了在Chrome中的实际运行效果
- [ ] 遵循了项目的代码风格
- [ ] 没有引入不必要的依赖

### 🎯 **开发优先级**

当前我们特别欢迎以下方面的贡献：

1. **🔥 高优先级**
   - 消息解析器的扩展和优化
   - 自动回复策略的智能化改进
   - 性能监控和内存优化

2. **📈 中优先级**
   - 用户界面的美化和交互优化
   - 更多的消息格式支持
   - 国际化和多语言支持

3. **💡 创新功能**
   - AI驱动的智能回复
   - 消息数据的可视化分析
   - 与其他工具的集成

## 📄 开源许可

本项目采用 **MIT License** 开源许可证。

```
MIT License

Copyright (c) 2024 Robin Tse

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.
```

## 🙏 致谢

### 🌟 贡献者
感谢所有为项目做出贡献的开发者们！

### 🛠️ 技术栈致谢
- **Webpack** - 强大的模块打包工具
- **Babel** - JavaScript编译器
- **Chrome Extensions API** - 浏览器扩展平台
- **Node.js生态** - 丰富的开发工具链

### 💡 灵感来源
- Chrome扩展开发社区的最佳实践
- 现代Web开发的设计模式
- 开源项目的协作精神

## 📞 联系与支持

### 🔗 **项目链接**
- **GitHub仓库**: [ctrip-im-helper](https://github.com/your-username/ctrip-im-helper)
- **问题反馈**: [Issues](https://github.com/your-username/ctrip-im-helper/issues)
- **功能建议**: [Discussions](https://github.com/your-username/ctrip-im-helper/discussions)

### 📧 **联系方式**
- **项目维护者**: Robin Tse
- **技术支持**: 通过GitHub Issues获得最快响应
- **商务合作**: 请通过GitHub联系

### 📚 **相关资源**
- **完整文档**: [BUILD.md](BUILD.md) | [DEVELOPMENT_GUIDE.md](DEVELOPMENT_GUIDE.md)
- **更新日志**: [CHANGELOG.md](CHANGELOG.md)
- **构建指南**: [BUILD.md](BUILD.md)

---

<div align="center">

### 🌟 **如果这个项目对您有帮助，请给我们一个Star！** ⭐

**让我们一起构建更好的携程IM工具！** 🚀

[![Star History Chart](https://api.star-history.com/svg?repos=your-username/ctrip-im-helper&type=Date)](https://star-history.com/#your-username/ctrip-im-helper&Date)

</div>