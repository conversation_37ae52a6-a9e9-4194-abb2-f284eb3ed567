# 🎯 携程IM拦截器 (Ctrip IM Interceptor)

[![Version](https://img.shields.io/badge/version-1.0.3-blue.svg)](https://github.com/your-username/ctrip-helper)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Chrome Extension](https://img.shields.io/badge/chrome-extension-yellow.svg)](https://developer.chrome.com/docs/extensions/)

一个专业的Chrome扩展，用于拦截和分析携程网站的IM WebSocket通信，提供智能自动回复功能。

## ✨ 主要特性

### 🔍 **WebSocket拦截**
- **智能拦截**: 精准识别携程IM的WebSocket连接
- **实时监控**: 监控所有消息的收发状态
- **连接管理**: 自动管理多个WebSocket连接
- **性能优化**: 低开销的拦截机制

### 📝 **消息解析**
- **XMPP协议**: 完整支持XMPP消息格式
- **JSON数据**: 智能解析JSON格式消息
- **文本消息**: 处理纯文本通信
- **多格式支持**: 可扩展的解析器架构

### 🤖 **智能自动回复**
- **关键词匹配**: 基于关键词的智能回复
- **模板系统**: 丰富的回复模板库
- **用户限制**: 防止垃圾回复的用户管理
- **回复策略**: 多种回复模式选择

### 📊 **数据分析**
- **实时统计**: 消息数量、回复率等统计
- **历史记录**: 完整的消息历史追踪
- **性能监控**: 扩展性能和内存使用监控
- **错误追踪**: 全面的错误处理和报告

## 🏗️ 技术架构

### 📁 **项目结构**
```
ctrip-helper/
├── manifest.json              # 扩展配置文件
├── popup.html                 # 弹窗界面
├── popup.css                  # 界面样式
├── icons/                     # 图标资源
└── src/                       # 源代码目录
    ├── background/            # 后台服务模块
    │   ├── service-worker.js      # 主服务工作者
    │   ├── message-handler.js     # 消息处理器
    │   ├── alarm-manager.js       # 定时任务管理
    │   ├── notification-manager.js # 通知管理
    │   └── context-menu.js        # 上下文菜单
    ├── content/               # 内容脚本模块
    │   ├── content-script.js      # 主内容脚本
    │   ├── websocket-interceptor.js # WebSocket拦截器
    │   └── connection-manager.js   # 连接管理器
    ├── popup/                 # 弹窗模块
    │   └── popup-manager.js       # 弹窗管理器
    ├── message-parser/        # 消息解析模块
    │   └── message-processor.js   # 消息处理器
    ├── auto-reply/            # 自动回复模块
    │   └── auto-reply-engine.js   # 自动回复引擎
    └── utils/                 # 工具模块
        ├── logger.js              # 日志系统
        ├── config.js              # 配置管理
        ├── constants.js           # 常量定义
        ├── event-emitter.js       # 事件发射器
        ├── performance-utils.js   # 性能优化工具
        └── error-handler.js       # 错误处理系统
```

### 🎨 **设计模式**
- **观察者模式**: 事件驱动的组件通信
- **策略模式**: 可插拔的消息解析器
- **工厂模式**: 统一的对象创建机制
- **单例模式**: 全局配置和状态管理

### ⚡ **性能优化**
- **缓存机制**: 智能缓存减少重复计算
- **批处理**: 消息批量处理提升性能
- **节流防抖**: UI更新优化
- **内存管理**: 自动清理和内存监控

## 🚀 安装和使用

### 📋 **系统要求**
- Chrome 88+ 或其他Chromium内核浏览器
- Manifest V3 支持
- 携程网站访问权限

### 💻 **安装步骤**

#### 开发者模式安装
1. 下载源代码并解压
2. 打开Chrome扩展管理页面 (`chrome://extensions/`)
3. 启用"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目根目录

#### 商店安装 (即将上线)
1. 访问Chrome网上应用店
2. 搜索"携程IM拦截器"
3. 点击"添加至Chrome"

### 🛠️ **配置设置**

#### 基本设置
```javascript
{
  "interceptor": {
    "enabled": true,                // 启用拦截
    "logLevel": "info",            // 日志级别
    "maxHistorySize": 1000         // 最大历史记录
  },
  "autoReply": {
    "enabled": false,              // 启用自动回复
    "mode": "keywords",            // 回复模式
    "delay": { "min": 1000, "max": 3000 } // 回复延迟
  }
}
```

## 🎮 使用指南

### 📋 **主要功能**

#### 1. WebSocket监控
- 自动检测携程IM连接
- 实时显示连接状态
- 监控消息收发

#### 2. 消息分析
- 查看详细消息内容
- 解析XMPP和JSON格式
- 导出消息历史

#### 3. 自动回复设置
- 配置关键词和回复模板
- 设置回复延迟和频率
- 管理用户黑白名单

#### 4. 性能监控
- 查看扩展性能指标
- 监控内存使用情况
- 分析错误和异常

### 📊 **界面介绍**

#### 主面板
- **连接状态**: 显示当前WebSocket连接
- **消息统计**: 实时消息收发统计
- **快速操作**: 一键开启/关闭功能

#### 设置面板
- **拦截设置**: 配置拦截参数
- **自动回复**: 设置回复规则
- **用户偏好**: 个人化设置

#### 调试面板
- **实时日志**: 查看运行日志
- **性能监控**: 性能指标显示
- **错误报告**: 错误统计和导出

## 🔧 开发指南

### 🛠️ **开发环境搭建**

```bash
# 克隆项目
git clone https://github.com/your-username/ctrip-helper.git
cd ctrip-helper

# 安装依赖 (如果有)
npm install

# 运行测试
npm test

# 构建扩展
npm run build
```

### 📝 **代码规范**

#### ES6模块化
```javascript
// 导入模块
import { Logger } from '../utils/logger.js';
import { EventEmitter } from '../utils/event-emitter.js';

// 导出类
export class MyClass extends EventEmitter {
  constructor() {
    super();
    this.logger = new Logger('MyClass');
  }
}

export default MyClass;
```

#### 错误处理
```javascript
import { globalErrorHandler, ErrorTypes, ErrorSeverity } from '../utils/error-handler.js';

try {
  // 业务逻辑
} catch (error) {
  globalErrorHandler.handleError(error, {
    type: ErrorTypes.WEBSOCKET,
    severity: ErrorSeverity.HIGH,
    context: { operation: 'connect' }
  });
}
```

#### 性能优化
```javascript
import PerformanceUtils from '../utils/performance-utils.js';

class MyComponent {
  constructor() {
    // 使用缓存
    this.cache = new PerformanceUtils.Cache(50, 30000);
    
    // 节流优化
    this.throttledUpdate = PerformanceUtils.throttle(
      this.updateUI.bind(this), 100
    );
  }
}
```

### 🧪 **测试**

#### 单元测试
```javascript
// 测试WebSocket拦截器
describe('WebSocketInterceptor', () => {
  it('should intercept Ctrip IM connections', () => {
    const interceptor = new WebSocketInterceptor();
    const isCtripIM = interceptor.isCtripIMWebSocket('wss://im3.ctrip.com/ws-xmpp');
    expect(isCtripIM).toBe(true);
  });
});
```

#### 集成测试
```javascript
// 测试完整消息流程
describe('Message Flow', () => {
  it('should process messages end-to-end', async () => {
    const processor = new MessageProcessor();
    const result = await processor.processMessage(mockMessage);
    expect(result).toBeDefined();
  });
});
```

## 🐛 故障排除

### 常见问题

#### 1. WebSocket拦截失败
- **检查网站**: 确保在携程网站上使用
- **刷新页面**: 重新加载页面激活拦截器
- **检查权限**: 确认扩展权限设置正确

#### 2. 自动回复不工作
- **检查配置**: 确认自动回复已启用
- **检查关键词**: 验证关键词配置正确
- **检查用户**: 确认用户不在黑名单中

#### 3. 性能问题
- **清理历史**: 定期清理消息历史
- **调整设置**: 降低日志级别
- **重启扩展**: 禁用后重新启用扩展

### 🔍 **调试技巧**

#### 开发者工具
1. 打开Chrome开发者工具
2. 切换到Console面板
3. 查看扩展相关日志
4. 使用`window.popupManager`调试

#### 日志分析
```javascript
// 获取日志历史
const logs = logger.getHistory();

// 导出错误报告
const errorReport = globalErrorHandler.exportErrorReport();

// 性能监控
PerformanceUtils.MemoryMonitor.logMemoryUsage();
```

## 🤝 贡献指南

### 💡 **参与贡献**

1. **Fork** 项目到您的GitHub账户
2. **创建** 新的功能分支 (`git checkout -b feature/amazing-feature`)
3. **提交** 您的更改 (`git commit -m 'Add amazing feature'`)
4. **推送** 到分支 (`git push origin feature/amazing-feature`)
5. **创建** Pull Request

### 📋 **贡献类型**
- 🐛 Bug修复
- ✨ 新功能开发
- 📚 文档改进
- 🎨 UI/UX优化
- ⚡ 性能优化
- 🧪 测试完善

### 📝 **代码规范**
- 遵循ESLint配置
- 添加适当的注释
- 编写单元测试
- 更新相关文档

## 📄 许可证

本项目采用 [MIT License](LICENSE) 许可证。

## 🙏 致谢

- 感谢所有贡献者的支持
- 感谢Chrome扩展开发社区
- 感谢开源项目的启发

## 📞 联系我们

- **Issues**: [GitHub Issues](https://github.com/your-username/ctrip-helper/issues)
- **Email**: <EMAIL>
- **文档**: [完整文档](https://github.com/your-username/ctrip-helper/docs)

---

**⭐ 如果这个项目对您有帮助，请给我们一个星标！** 