// 🔧 WebSocket调试脚本
// 在浏览器控制台中运行此脚本来诊断WebSocket问题

console.log('🔍 开始WebSocket环境诊断...');

// 1. 检查WebSocket基本可用性
console.log('\n=== 1. WebSocket基本检查 ===');
console.log('WebSocket是否存在:', typeof WebSocket !== 'undefined');
console.log('WebSocket构造函数:', WebSocket);
console.log('WebSocket.name:', WebSocket?.name);
console.log('WebSocket.prototype:', WebSocket?.prototype);

// 2. 检查WebSocket常量
console.log('\n=== 2. WebSocket常量检查 ===');
const constants = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];
constants.forEach(constant => {
  console.log(`WebSocket.${constant}:`, WebSocket?.[constant]);
  
  // 检查是否为只读属性
  try {
    const descriptor = Object.getOwnPropertyDescriptor(WebSocket, constant);
    console.log(`  - 属性描述符:`, descriptor);
  } catch (e) {
    console.log(`  - 获取属性描述符失败:`, e.message);
  }
});

// 3. 检查扩展相关标记
console.log('\n=== 3. 扩展状态检查 ===');
console.log('_ctripWebSocketInterceptorInstalled:', window._ctripWebSocketInterceptorInstalled);
console.log('_ctripWebSocketInterceptor:', window._ctripWebSocketInterceptor);

// 4. 测试WebSocket创建
console.log('\n=== 4. WebSocket创建测试 ===');
try {
  // 测试创建WebSocket（不实际连接）
  const testWS = new WebSocket('wss://echo.websocket.org/');
  console.log('✅ WebSocket创建成功:', testWS);
  console.log('  - readyState:', testWS.readyState);
  console.log('  - url:', testWS.url);
  
  // 立即关闭测试连接
  testWS.close();
  
} catch (e) {
  console.error('❌ WebSocket创建失败:', e);
}

// 5. 检查原型链
console.log('\n=== 5. 原型链检查 ===');
try {
  console.log('WebSocket.prototype.constructor:', WebSocket.prototype?.constructor);
  console.log('WebSocket.__proto__:', WebSocket.__proto__);
  console.log('Object.getPrototypeOf(WebSocket):', Object.getPrototypeOf(WebSocket));
} catch (e) {
  console.error('❌ 原型链检查失败:', e);
}

// 6. 尝试手动设置常量
console.log('\n=== 6. 常量设置测试 ===');
function testConstantSetting() {
  // 创建一个测试函数
  function TestWebSocket() {}
  
  console.log('测试直接赋值:');
  try {
    TestWebSocket.CONNECTING = 0;
    console.log('✅ 直接赋值成功');
  } catch (e) {
    console.error('❌ 直接赋值失败:', e.message);
  }
  
  console.log('测试Object.defineProperty:');
  try {
    Object.defineProperty(TestWebSocket, 'CONNECTING', {
      value: 0,
      writable: false,
      enumerable: true,
      configurable: false
    });
    console.log('✅ defineProperty成功');
  } catch (e) {
    console.error('❌ defineProperty失败:', e.message);
  }
}

testConstantSetting();

// 7. 检查当前页面环境
console.log('\n=== 7. 页面环境检查 ===');
console.log('当前URL:', window.location.href);
console.log('User Agent:', navigator.userAgent);
console.log('是否在iframe中:', window !== window.top);
console.log('document.readyState:', document.readyState);

// 8. 检查Chrome扩展环境
console.log('\n=== 8. Chrome扩展环境检查 ===');
console.log('chrome对象存在:', typeof chrome !== 'undefined');
console.log('chrome.runtime存在:', typeof chrome?.runtime !== 'undefined');
console.log('chrome.runtime.id:', chrome?.runtime?.id);

// 9. 生成诊断报告
console.log('\n=== 🎯 诊断报告 ===');

const report = {
  webSocketAvailable: typeof WebSocket !== 'undefined',
  webSocketName: WebSocket?.name,
  hasPrototype: !!WebSocket?.prototype,
  constants: {},
  extensionInstalled: !!window._ctripWebSocketInterceptorInstalled,
  pageUrl: window.location.href,
  userAgent: navigator.userAgent,
  chromeExtensionEnv: typeof chrome !== 'undefined' && typeof chrome?.runtime !== 'undefined'
};

constants.forEach(constant => {
  report.constants[constant] = {
    value: WebSocket?.[constant],
    exists: WebSocket?.hasOwnProperty(constant)
  };
});

console.log('📋 完整诊断报告:', JSON.stringify(report, null, 2));

// 10. 提供修复建议
console.log('\n=== 💡 修复建议 ===');

if (!report.webSocketAvailable) {
  console.error('❌ WebSocket不可用 - 可能在不支持的环境中');
} else if (!report.hasPrototype) {
  console.error('❌ WebSocket.prototype不存在 - 可能被其他脚本破坏');
} else if (report.extensionInstalled) {
  console.log('✅ 扩展已安装 - 检查是否有其他错误');
} else {
  console.log('⚠️ 扩展未安装 - 可能初始化失败');
}

console.log('\n🔍 WebSocket环境诊断完成！');
