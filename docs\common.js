"use strict";
(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([[351], {
    13485: function (e, t, n) {
        n.r(t);
        var o, r = n(73002), i = n(91880), a = n.n(i), c = n(62198), s = (o = (0,
            r.Z)(a().mark(function e() {
                return a().wrap(function (e) {
                    for (; ;)
                        switch (e.prev = e.next) {
                            case 0:
                            case "end":
                                return e.stop()
                        }
                }, e)
            })),
            function () {
                return o.apply(this, arguments)
            }
        ), u = s, d = s, l = s;
        function m(e) {
            var t = e.getStorageFn
                , n = (e.setStorageFn,
                    e.removeStorageFn)
                , o = e.fetchFn;
            u = t,
                d = n,
                l = o
        }
        window.disableAutoReport = !1,
            m({
                getStorageFn: function (e) {
                    var t = e.key;
                    return new Promise(function (e) {
                        try {
                            e(window.localStorage.getItem(t))
                        } catch (n) {
                            e("")
                        }
                    }
                    )
                },
                setStorageFn: function (e) {
                    var t = e.key
                        , n = e.value;
                    return new Promise(function (e) {
                        try {
                            e(window.localStorage.setItem(t, n))
                        } catch (o) {
                            e("")
                        }
                    }
                    )
                },
                removeStorageFn: function (e) {
                    var t = e.key;
                    return new Promise(function (e) {
                        try {
                            e(window.localStorage.removeItem(t))
                        } catch (n) {
                            e("")
                        }
                    }
                    )
                },
                fetchFn: function (e) {
                    var t = e.url
                        , n = e.body
                        , o = e.timeout;
                    return new c.ZP({}).fetch(t, {
                        noCookieOrigin: !0,
                        timeout: void 0 === o ? 3e4 : o,
                        body: n
                    })
                }
            });
        var y = "_100017720_nfes_20250703193107_0_6389"
            , h = "100017720_INJECT_FNLIST_CACHE"
            , _ = "100017720_BUILD_VERSION_CACHE"
            , f = {}
            , g = []
            , p = {}
            , w = null
            , v = !0
            , C = !1;
        function Z() {
            return (Z = (0,
                r.Z)(a().mark(function e() {
                    var t, n;
                    return a().wrap(function (e) {
                        for (; ;)
                            switch (e.prev = e.next) {
                                case 0:
                                    return C = !0,
                                        e.prev = 1,
                                        e.next = 4,
                                        u({
                                            key: _
                                        });
                                case 4:
                                    if (e.t0 = e.sent,
                                        e.t0) {
                                        e.next = 7;
                                        break
                                    }
                                    e.t0 = "";
                                case 7:
                                    if (!(!(t = e.t0) || t !== y)) {
                                        e.next = 14;
                                        break
                                    }
                                    return e.next = 11,
                                        d({
                                            key: h
                                        });
                                case 11:
                                    return e.next = 13,
                                        d({
                                            key: _
                                        });
                                case 13:
                                    return e.abrupt("return");
                                case 14:
                                    return e.next = 16,
                                        u({
                                            key: h
                                        });
                                case 16:
                                    if (e.t1 = e.sent,
                                        e.t1) {
                                        e.next = 19;
                                        break
                                    }
                                    e.t1 = "";
                                case 19:
                                    "string" == typeof (n = e.t1) && (n = n && JSON.parse(n) || []),
                                        (g = g.concat(n)).forEach(function (e) {
                                            f[e] = 1
                                        }),
                                        e.next = 28;
                                    break;
                                case 25:
                                    e.prev = 25,
                                        e.t2 = e.catch(1);
                                case 28:
                                case "end":
                                    return e.stop()
                            }
                    }, e, null, [[1, 25]])
                }))).apply(this, arguments)
        }
        function T() {
            w && (clearTimeout(w),
                w = null)
        }
        function E() {
            if (!window.disableAutoReport) {
                var e = window.reportInterval || 2e4;
                !w && Object.keys(p).length > 0 && (w = setTimeout(function () {
                    T(),
                        k()
                }, e))
            }
        }
        function k() {
            if (T(),
                v) {
                var e = Object.keys(p);
                y && 0 !== e.length && (l({
                    url: "https://m.ctrip.com/restapi/soa2/28967/reportInjectFnInfo",
                    method: "POST",
                    header: {
                        "Content-Type": "application/json"
                    },
                    body: {
                        buildVersion: y,
                        fnInfo: e.join("")
                    }
                }).then(function (e) {
                    (v = !!(e && e.data && e.data.injectSwitch)) && E()
                }).catch(function (e) {
                    E()
                }),
                    function () {
                        S.apply(this, arguments)
                    }(),
                    p = {})
            }
        }
        function S() {
            return (S = (0,
                r.Z)(a().mark(function e() {
                    return a().wrap(function (e) {
                        for (; ;)
                            switch (e.prev = e.next) {
                                case 0:
                                    Object.keys(p).forEach(function (e) {
                                        var t = g.indexOf(e);
                                        -1 !== t && (g.splice(t, 1),
                                            delete f[e]),
                                            g.push(e),
                                            f[e] = 1,
                                            g.length > 5e3 && delete f[g.shift()]
                                    });
                                case 2:
                                case "end":
                                    return e.stop()
                            }
                    }, e)
                }))).apply(this, arguments)
        }
        window._n = function (e) {
            if (C || function () {
                Z.apply(this, arguments)
            }(),
                !v) {
                T(),
                    p = {};
                return
            }
            p[e] || f[e] || (p[e] = 1,
                0) || (Object.keys(p).length >= 1e3 ? k() : E())
        }
            ,
            window.reportInjectFnInfo = k,
            window.registerFn = m,
            t.default = {
                registerFn: m,
                reportInjectFnInfo: k
            }
    },
    23380: function (e, t, n) {
        n.r(t),
            n.d(t, {
                AllowActionCodes: function () {
                    return w
                },
                BlackActionCodes: function () {
                    return v
                },
                ConvStatus: function () {
                    return h
                },
                ConvTabs: function () {
                    return l
                },
                ConvTabsXQH: function () {
                    return m
                },
                ConvType: function () {
                    return r
                },
                ConversationType: function () {
                    return _
                },
                CreateConvType: function () {
                    return y
                },
                EmojiMapCode: function () {
                    return C
                },
                EmojiSendCode: function () {
                    return Z
                },
                EmojiSendCode2: function () {
                    return T
                },
                InitChatType: function () {
                    return i
                },
                JobPosition: function () {
                    return u
                },
                JobPositionKeyMap: function () {
                    return d
                },
                NormalMessageType: function () {
                    return f
                },
                NotifyMessageType: function () {
                    return g
                },
                WorkStatus: function () {
                    return a
                },
                WorkStatusKeyMap: function () {
                    return s
                },
                starCls: function () {
                    return p
                },
                weekday: function () {
                    return c
                }
            });
        var o = n(60414);
        n(13485);
        var r = {
            B2B: "B2B",
            O2O: "O2O",
            C2C: "C2C",
            B2C: "B2C",
            B2O: "B2O",
            C2B: "C2B",
            C2O: "C2O",
            O2B: "O2B",
            O2C: "O2C",
            B2MC: "B2MC"
        }
            , i = {
                B2O: "1",
                B2C: "2",
                B2B: "5"
            }
            , a = {
                IDLE: {
                    icon: "ico_free",
                    iconInvite: "free",
                    name: "空闲",
                    tranKey: "idle",
                    value: "IDLE",
                    key: 0
                },
                HANGUP: {
                    icon: "ico_busy",
                    iconInvite: "busy",
                    name: "忙碌",
                    tranKey: "busy",
                    value: "HANGUP",
                    key: 1
                },
                LEAVING: {
                    icon: "ico_leave",
                    iconInvite: "leave",
                    name: "离开",
                    tranKey: "leaving",
                    value: "LEAVING",
                    key: 2
                },
                GETOFF: {
                    icon: "ico_exit",
                    iconInvite: "exit",
                    name: "下班",
                    tranKey: "logout",
                    value: "GETOFF",
                    key: 3
                }
            }
            , c = [{
                en: "Sunday",
                cn: "星期日"
            }, {
                en: "Monday",
                cn: "星期一"
            }, {
                en: "Tuesday",
                cn: "星期二"
            }, {
                en: "Wednesday",
                cn: "星期三"
            }, {
                en: "Thursday",
                cn: "星期四"
            }, {
                en: "Friday",
                cn: "星期五"
            }, {
                en: "Saturday",
                cn: "星期六"
            }]
            , s = {
                k_0: "IDLE",
                k_1: "HANGUP",
                k_2: "LEAVING",
                k_3: "GETOFF"
            }
            , u = {
                LEADER: {
                    key: 1,
                    code: "LEADER",
                    name: o.Z.get("principal", "", ""),
                    tranKey: "principal"
                },
                FINANCE: {
                    key: 2,
                    code: "FINANCE",
                    tranKey: "finance",
                    name: o.Z.get("finance", "", "")
                },
                OPERATE: {
                    key: 3,
                    code: "OPERATE",
                    tranKey: "operation",
                    name: o.Z.get("oprator", "", "")
                },
                AGENT: {
                    key: 4,
                    code: "AGENT",
                    tranKey: "agent",
                    name: o.Z.get("agent", "", "")
                }
            }
            , d = {
                k_1: o.Z.get("principal", "", ""),
                k_2: o.Z.get("finance", "", ""),
                k_3: o.Z.get("oprator", "", ""),
                k_4: o.Z.get("agent", "", "")
            }
            , l = {
                BC: {
                    name: "客人",
                    type: "BC",
                    tranKey: "customer",
                    convTypes: [r.B2C, r.C2B, r.B2MC]
                },
                BOP: {
                    name: "企业",
                    type: "BOP",
                    tranKey: "ctripAgent",
                    convTypes: [r.B2O, r.O2B, r.B2B]
                },
                XQH: {
                    name: "星球号",
                    type: "xingqiuhao",
                    tranKey: "chat",
                    convTypes: []
                }
            }
            , m = {
                XQH: {
                    name: "聊天",
                    type: "xingqiuhao",
                    tranKey: "chat",
                    convTypes: []
                },
                BC: {
                    name: "咨询",
                    type: "BC",
                    tranKey: "customer",
                    convTypes: [r.B2C, r.C2B, r.B2MC]
                }
            }
            , y = {
                BC: [r.B2C, r.C2B],
                BOP: [r.B2O, r.O2B],
                BB: [r.B2B]
            }
            , h = {
                ACTIVE: {
                    name: "处理中",
                    type: "ACTIVE",
                    tranKey: "proccessing",
                    value: 2,
                    values: [1, 2]
                },
                FINISH: {
                    name: "已完成",
                    type: "FINISH",
                    tranKey: "finished",
                    value: 4,
                    values: [4]
                }
            }
            , _ = {
                GroupChat: "groupchat",
                Notice: "headline",
                SingleChat: "chat",
                XingqiuChat: "xingqiuhao",
                WaitChat: "waitlist"
            }
            , f = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
            , g = [1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008, 1009, 1012, 1021, 1022, 1023]
            , p = ["chosen", "orange", "green", "blue", "purple", "pink"]
            , w = ["NBZ11", "NBZ12", "NBZ13", "NBZ14", "NBZ16", "NBZ78", "NBZ17", "NBZ18", "NBZ48", "NBZ20", "NBZ22", "NBZ23", "NBZ24", "NBZ28", "NBZ52", "NBZ27", "NBZ29", "NBZ30", "NBZ31", "NBZ32", "NBZ33", "NBZ34", "NBZ35", "NBZ36", "NBZ42", "NBZ43", "NBZ44", "NHT09", "NHT01", "NHT02", "NHT03", "NBZ74", "NHT04", "NHT05", "NHT06", "NHT07", "NHT08", "CBZ10", "NBZ37", "NBZ38", "NBZ39", "NBZ99", "NBZ68", "NBZ71", "CBZ50", "NBZ75", "NBZ76", "NBZ77", "NBZ81", "NBZ80", "NBZ90", "CBZ66", "NBZ89", "NBZ91", "NBZ92", "NBZ84", "NBZ95"]
            , v = ["NBZ050", "NBZ50", "NBZ94"]
            , C = [{
                code: "im_emoji_grin",
                en: "[Grin]",
                cn: "[龇牙]"
            }, {
                code: "im_emoji_joyful",
                en: "[Joyful]",
                cn: "[愉快]"
            }, {
                code: "im_emoji_smile",
                en: "[Smile]",
                cn: "[微笑]"
            }, {
                code: "im_emoji_trick",
                en: "[Trick]",
                cn: "[坏笑]"
            }, {
                code: "im_emoji_askance",
                en: "[Askance]",
                cn: "[斜眼看]"
            }, {
                code: "im_emoji_tongue",
                en: "[Tongue]",
                cn: "[调皮]"
            }, {
                code: "im_emoji_chukle",
                en: "[Chukle]",
                cn: "[偷笑]"
            }, {
                code: "im_emoji_bald",
                en: "[Bald]",
                cn: "[秃了]"
            }, {
                code: "im_emoji_happy",
                en: "[Happy]",
                cn: "[开心]"
            }, {
                code: "im_emoji_tension",
                en: "[Tension]",
                cn: "[紧张]"
            }, {
                code: "im_emoji_think",
                en: "[Think]",
                cn: "[思考]"
            }, {
                code: "im_emoji_grimace",
                en: "[Grimace]",
                cn: "[鬼脸]"
            }, {
                code: "im_emoji_facepalm",
                en: "[Facepalm]",
                cn: "[捂脸]"
            }, {
                code: "im_emoji_joy_tears",
                en: "[JoyTears]",
                cn: "[笑哭]"
            }, {
                code: "im_emoji_like",
                en: "[Like]",
                cn: "[喜欢]"
            }, {
                code: "im_emoji_cry",
                en: "[Cry]",
                cn: "[大哭]"
            }, {
                code: "im_emoji_awkward",
                en: "[Awkward]",
                cn: "[尴尬]"
            }, {
                code: "im_emoji_tiptop",
                en: "[Tiptop]",
                cn: "[强]"
            }, {
                code: "im_emoji_sob",
                en: "[Sob]",
                cn: "[哭]"
            }, {
                code: "im_emoji_kiss",
                en: "[Kiss]",
                cn: "[亲亲]"
            }, {
                code: "im_emoji_clap",
                en: "[Clap]",
                cn: "[鼓掌]"
            }, {
                code: "im_emoji_smart",
                en: "[Smart]",
                cn: "[机智]"
            }, {
                code: "im_emoji_congratulate",
                en: "[Congratulate]",
                cn: "[庆祝]"
            }, {
                code: "im_emoji_onlooker",
                en: "[Onlooker]",
                cn: "[吃瓜]"
            }, {
                code: "im_emoji_sneer",
                en: "[Sneer]",
                cn: "[冷笑]"
            }, {
                code: "im_emoji_shy",
                en: "[Shy]",
                cn: "[害羞]"
            }, {
                code: "im_emoji_afraid",
                en: "[Afraid]",
                cn: "[害怕]"
            }, {
                code: "im_emoji_worry",
                en: "[worry]",
                cn: "[担心]"
            }, {
                code: "im_emoji_panic",
                en: "[Panic]",
                cn: "[惊悚]"
            }, {
                code: "im_emoji_surprise",
                en: "[Surprise]",
                cn: "[惊讶]"
            }, {
                code: "im_emoji_pick_eyebrows",
                en: "[PickEyebrows]",
                cn: "[挑眉]"
            }, {
                code: "im_emoji_scowl",
                en: "[Scowl]",
                cn: "[呆]"
            }, {
                code: "im_emoji_bye",
                en: "[Bye]",
                cn: "[再见]"
            }, {
                code: "im_emoji_go_fot_it",
                en: "[GoFotIt]",
                cn: "[加油]"
            }, {
                code: "im_emoji_yawn",
                en: "[Yawn]",
                cn: "[打哈欠]"
            }, {
                code: "im_emoji_salute",
                en: "[Salute]",
                cn: "[抱拳]"
            }, {
                code: "im_emoji_confusion",
                en: "[Confusion]",
                cn: "[疑惑]"
            }, {
                code: "im_emoji_eyepalm",
                en: "[Eyepalm]",
                cn: "[捂眼]"
            }, {
                code: "imkit_emoji_aixinlian",
                en: "[LoveFace]",
                cn: "[爱心脸]"
            }, {
                code: "imkit_emoji_ye",
                en: "[Yeah!]",
                cn: "[耶]"
            }, {
                code: "imkit_emoji_em",
                en: "[Sweat]",
                cn: "[em]"
            }, {
                code: "imkit_emoji_baozha",
                en: "[Explode]",
                cn: "[爆炸]"
            }, {
                code: "imkit_emoji_xingxingyan",
                en: "[StarEyes]",
                cn: "[星星眼]"
            }, {
                code: "imkit_emoji_yes_sir",
                en: "[YesSir]",
                cn: "[yesSir]"
            }, {
                code: "imkit_emoji_baiyan",
                en: "[Slight]",
                cn: "[白眼]"
            }, {
                code: "imkit_emoji_liekai",
                en: "[Split]",
                cn: "[裂开]"
            }, {
                code: "imkit_emoji_yun",
                en: "[Giddy]",
                cn: "[晕]"
            }, {
                code: "imkit_emoji_tianshi",
                en: "[Angel]",
                cn: "[天使]"
            }, {
                code: "imkit_emoji_xu",
                en: "[Shhh]",
                cn: "[嘘]"
            }, {
                code: "imkit_emoji_wabikong",
                en: "[NosePick]",
                cn: "[挖鼻孔]"
            }, {
                code: "imkit_emoji_shengqi",
                en: "[Angry]",
                cn: "[生气]"
            }, {
                code: "imkit_emoji_nanguo",
                en: "[Sad]",
                cn: "[难过]"
            }, {
                code: "imkit_emoji_linggan",
                en: "[Idea]",
                cn: "[灵感]"
            }, {
                code: "imkit_emoji_yanjing",
                en: "[Glasses]",
                cn: "[眼镜]"
            }, {
                code: "imkit_emoji_weiqu",
                en: "[Wronged]",
                cn: "[委屈]"
            }, {
                code: "imkit_emoji_jingxi",
                en: "[Surprising]",
                cn: "[惊喜]"
            }, {
                code: "imkit_emoji_tuhuo",
                en: "[SpitFire]",
                cn: "[吐火]"
            }, {
                code: "imkit_emoji_ruhua",
                en: "[Ruhua]",
                cn: "[如花]"
            }, {
                code: "imkit_emoji_touteng",
                en: "[Headache]",
                cn: "[头疼]"
            }, {
                code: "imkit_emoji_outu",
                en: "[Vomit]",
                cn: "[呕吐]"
            }, {
                code: "imkit_emoji_666",
                en: "[Awesome]",
                cn: "[666]"
            }, {
                code: "imkit_emoji_leile",
                en: "[Tired]",
                cn: "[累了]"
            }, {
                code: "imkit_emoji_kun",
                en: "[Sleepy]",
                cn: "[困]"
            }, {
                code: "imkit_emoji_shuai",
                en: "[Toasted]",
                cn: "[衰]"
            }, {
                code: "imkit_emoji_lalianzui",
                en: "[Secret]",
                cn: "[拉链嘴]"
            }, {
                code: "imkit_emoji_keai",
                en: "[Cute]",
                cn: "[可爱]"
            }, {
                code: "imkit_emoji_tanqi",
                en: "[Sigh]",
                cn: "[叹气]"
            }, {
                code: "imkit_emoji_jianshen",
                en: "[Fitness]",
                cn: "[健身]"
            }, {
                code: "imkit_emoji_tuxue",
                en: "[Hematemesis]",
                cn: "[吐血]"
            }, {
                code: "imkit_emoji_tanshou",
                en: "[Helpless]",
                cn: "[摊手]"
            }, {
                code: "imkit_emoji_gongzuo",
                en: "[Work]",
                cn: "[工作]"
            }, {
                code: "imkit_emoji_hekele",
                en: "[Coke]",
                cn: "[喝可乐]"
            }, {
                code: "imkit_emoji_fuetou",
                en: "[Incapable]",
                cn: "[扶额头]"
            }, {
                code: "imkit_emoji_kouzhao",
                en: "[Mask]",
                cn: "[口罩]"
            }, {
                code: "imkit_emoji_haipa",
                en: "[Fear]",
                cn: "[恐惧]"
            }, {
                code: "imkit_emoji_ganben",
                en: "[Cheers]",
                cn: "[干杯]"
            }, {
                code: "imkit_emoji_hecha",
                en: "[Tea]",
                cn: "[喝茶]"
            }, {
                code: "imkit_emoji_jiaban",
                en: "[Overtime]",
                cn: "[加班]"
            }, {
                code: "imkit_emoji_exin",
                en: "[Nausea]",
                cn: "[恶心]"
            }, {
                code: "imkit_emoji_dapenti",
                en: "[Sneeze]",
                cn: "[打喷嚏]"
            }, {
                code: "imkit_emoji_mojing",
                en: "[CoolGuy]",
                cn: "[墨镜]"
            }, {
                code: "imkit_emoji_bucuowo",
                en: "[GoodJob]",
                cn: "[不错哦]"
            }, {
                code: "imkit_emoji_fashou",
                en: "[Fever]",
                cn: "[发烧]"
            }, {
                code: "imkit_emoji_fangdajing",
                en: "[Loupe]",
                cn: "[放大镜]"
            }, {
                code: "imkit_emoji_xinsui",
                en: "[HeartBroken]",
                cn: "[心碎]"
            }, {
                code: "imkit_emoji_huo",
                en: "[Fire]",
                cn: "[火]"
            }, {
                code: "imkit_emoji_huashu",
                en: "[Flower]",
                cn: "[花束]"
            }, {
                code: "imkit_emoji_shui",
                en: "[Drops]",
                cn: "[水]"
            }, {
                code: "imkit_emoji_xingxing",
                en: "[Star]",
                cn: "[星星]"
            }, {
                code: "imkit_emoji_taiyang",
                en: "[Sun]",
                cn: "[太阳]"
            }, {
                code: "imkit_emoji_yibaifen",
                en: "[100]",
                cn: "[一百分]"
            }, {
                code: "imkit_emoji_wenhao",
                en: "[?]",
                cn: "[问号]"
            }, {
                code: "imkit_emoji_gantanhao",
                en: "[!]",
                cn: "[感叹号]"
            }, {
                code: "imkit_emoji_woshou",
                en: "[ShakeHands]",
                cn: "[握手]"
            }, {
                code: "imkit_emoji_dadianhua",
                en: "[Call]",
                cn: "[打电话]"
            }, {
                code: "imkit_emoji_jiandaoshou",
                en: "[Victory]",
                cn: "[剪刀手]"
            }, {
                code: "imkit_emoji_paishou",
                en: "[Clapping]",
                cn: "[拍手]"
            }, {
                code: "imkit_emoji_bixin",
                en: "[Heart]",
                cn: "[比心]"
            }, {
                code: "im_emoji_ok",
                en: "[Ok]",
                cn: "[ok]"
            }, {
                code: "im_emoji_thumbs_up",
                en: "[ThumbsUp]",
                cn: "[点赞]"
            }, {
                code: "im_emoji_thumbs_down",
                en: "[ThumbsDown]",
                cn: "[踩]"
            }, {
                code: "im_emoji_beg",
                en: "[Beg]",
                cn: "[合掌]"
            }, {
                code: "im_emoji_hands_up",
                en: "[HandsUp]",
                cn: "[举手]"
            }, {
                code: "im_emoji_doge",
                en: "[Doge]",
                cn: "[狗]"
            }, {
                code: "im_emoji_husky",
                en: "[Husky]",
                cn: "[二哈]"
            }, {
                code: "im_emoji_cat",
                en: "[Cat]",
                cn: "[猫]"
            }, {
                code: "im_emoji_shit",
                en: "[Shit]",
                cn: "[便便]"
            }, {
                code: "im_emoji_pig",
                en: "[Pig]",
                cn: "[猪]"
            }, {
                code: "im_emoji_demon",
                en: "[Demon]",
                cn: "[恶魔]"
            }, {
                code: "im_emoji_love",
                en: "[Love]",
                cn: "[爱心]"
            }, {
                code: "im_emoji_stars_eye",
                en: "[StarsEye]",
                cn: "[星星眼uu]"
            }, {
                code: "im_emoji_blink",
                en: "[Blink]",
                cn: "[眨眼]"
            }, {
                code: "im_emoji_dizzy",
                en: "[Dizzy]",
                cn: "[晕uu]"
            }, {
                code: "im_emoji_frighten",
                en: "[Frighten]",
                cn: "[吓]"
            }, {
                code: "im_emoji_like_uu",
                en: "[LikeUu]",
                cn: "[喜欢uu]"
            }, {
                code: "im_emoji_speechless",
                en: "[Speechless]",
                cn: "[无语]"
            }, {
                code: "im_emoji_why",
                en: "[Why]",
                cn: "[问号uu]"
            }, {
                code: "im_emoji_push_glasses",
                en: "[PushGlasses]",
                cn: "[托眼镜]"
            }, {
                code: "im_emoji_angry_uu",
                en: "[AngryUu]",
                cn: "[生气uu]"
            }, {
                code: "im_emoji_shy_uu",
                en: "[ShyUu]",
                cn: "[害羞uu]"
            }, {
                code: "im_emoji_gift",
                en: "[Gift]",
                cn: "[礼物]"
            }, {
                code: "im_emoji_birthday",
                en: "[Birthday]",
                cn: "[生日]"
            }]
            , Z = [[{
                code: "im_emoji_grin",
                en: "[Grin]",
                cn: "[龇牙]"
            }, {
                code: "im_emoji_joyful",
                en: "[Joyful]",
                cn: "[愉快]"
            }, {
                code: "im_emoji_smile",
                en: "[Smile]",
                cn: "[微笑]"
            }, {
                code: "im_emoji_trick",
                en: "[Trick]",
                cn: "[坏笑]"
            }, {
                code: "im_emoji_askance",
                en: "[Askance]",
                cn: "[斜眼看]"
            }, {
                code: "im_emoji_chukle",
                en: "[Chukle]",
                cn: "[偷笑]"
            }, {
                code: "imkit_emoji_jingxi",
                en: "[Surprising]",
                cn: "[惊喜]"
            }, {
                code: "imkit_emoji_ye",
                en: "[Yeah!]",
                cn: "[耶]"
            }, {
                code: "imkit_emoji_yanjing",
                en: "[Glasses]",
                cn: "[眼镜]"
            }, {
                code: "im_emoji_tiptop",
                en: "[Tiptop]",
                cn: "[强]"
            }], [{
                code: "im_emoji_clap",
                en: "[Clap]",
                cn: "[鼓掌]"
            }, {
                code: "im_emoji_kiss",
                en: "[Kiss]",
                cn: "[亲亲]"
            }, {
                code: "im_emoji_congratulate",
                en: "[Congratulate]",
                cn: "[庆祝]"
            }, {
                code: "imkit_emoji_xingxingyan",
                en: "[StarEyes]",
                cn: "[星星眼]"
            }, {
                code: "imkit_emoji_aixinlian",
                en: "[LoveFace]",
                cn: "[爱心脸]"
            }, {
                code: "imkit_emoji_666",
                en: "[Awesome]",
                cn: "[666]"
            }, {
                code: "im_emoji_go_fot_it",
                en: "[GoFotIt]",
                cn: "[加油]"
            }, {
                code: "im_emoji_like",
                en: "[Like]",
                cn: "[喜欢]"
            }, {
                code: "im_emoji_happy",
                en: "[Happy]",
                cn: "[开心]"
            }, {
                code: "im_emoji_think",
                en: "[Think]",
                cn: "[思考]"
            }], [{
                code: "im_emoji_salute",
                en: "[Salute]",
                cn: "[抱拳]"
            }, {
                code: "imkit_emoji_ganben",
                en: "[Cheers]",
                cn: "[干杯]"
            }, {
                code: "imkit_emoji_jianshen",
                en: "[Fitness]",
                cn: "[健身]"
            }, {
                code: "im_emoji_sob",
                en: "[Sob]",
                cn: "[哭]"
            }, {
                code: "imkit_emoji_bucuowo",
                en: "[GoodJob]",
                cn: "[不错哦]"
            }, {
                code: "imkit_emoji_linggan",
                en: "[Idea]",
                cn: "[灵感]"
            }, {
                code: "im_emoji_smart",
                en: "[Smart]",
                cn: "[机智]"
            }, {
                code: "im_emoji_shy",
                en: "[Shy]",
                cn: "[害羞]"
            }, {
                code: "imkit_emoji_hekele",
                en: "[Coke]",
                cn: "[喝可乐]"
            }, {
                code: "imkit_emoji_tianshi",
                en: "[Angel]",
                cn: "[天使]"
            }], [{
                code: "imkit_emoji_mojing",
                en: "[CoolGuy]",
                cn: "[墨镜]"
            }, {
                code: "imkit_emoji_gongzuo",
                en: "[Work]",
                cn: "[工作]"
            }, {
                code: "im_emoji_scowl",
                en: "[Scowl]",
                cn: "[呆]"
            }, {
                code: "im_emoji_onlooker",
                en: "[Onlooker]",
                cn: "[吃瓜]"
            }, {
                code: "imkit_emoji_tanshou",
                en: "[Helpless]",
                cn: "[摊手]"
            }, {
                code: "im_emoji_confusion",
                en: "[Confusion]",
                cn: "[疑惑]"
            }, {
                code: "imkit_emoji_yes_sir",
                en: "[YesSir]",
                cn: "[yesSir]"
            }, {
                code: "im_emoji_eyepalm",
                en: "[Eyepalm]",
                cn: "[捂眼]"
            }, {
                code: "im_emoji_surprise",
                en: "[Surprise]",
                cn: "[惊讶]"
            }, {
                code: "im_emoji_afraid",
                en: "[Afraid]",
                cn: "[害怕]"
            }], [{
                code: "im_emoji_love",
                en: "[Love]",
                cn: "[爱心]"
            }, {
                code: "im_emoji_thumbs_up",
                en: "[ThumbsUp]",
                cn: "[点赞]"
            }, {
                code: "imkit_emoji_dadianhua",
                en: "[Call]",
                cn: "[打电话]"
            }, {
                code: "imkit_emoji_jiandaoshou",
                en: "[Victory]",
                cn: "[剪刀手]"
            }, {
                code: "imkit_emoji_bixin",
                en: "[Heart]",
                cn: "[比心]"
            }, {
                code: "imkit_emoji_paishou",
                en: "[Clapping]",
                cn: "[拍手]"
            }, {
                code: "imkit_emoji_woshou",
                en: "[ShakeHands]",
                cn: "[握手]"
            }, {
                code: "im_emoji_hands_up",
                en: "[HandsUp]",
                cn: "[举手]"
            }, {
                code: "im_emoji_ok",
                en: "[Ok]",
                cn: "[ok]"
            }, {
                code: "im_emoji_beg",
                en: "[Beg]",
                cn: "[合掌]"
            }], [{
                code: "imkit_emoji_huo",
                en: "[Fire]",
                cn: "[火]"
            }, {
                code: "imkit_emoji_huashu",
                en: "[Flower]",
                cn: "[花束]"
            }, {
                code: "imkit_emoji_shui",
                en: "[Drops]",
                cn: "[水]"
            }, {
                code: "imkit_emoji_gantanhao",
                en: "[!]",
                cn: "[感叹号]"
            }, {
                code: "imkit_emoji_wenhao",
                en: "[?]",
                cn: "[问号]"
            }, {
                code: "imkit_emoji_yibaifen",
                en: "[100]",
                cn: "[一百分]"
            }, {
                code: "imkit_emoji_xingxing",
                en: "[Star]",
                cn: "[星星]"
            }, {
                code: "imkit_emoji_taiyang",
                en: "[Sun]",
                cn: "[太阳]"
            }, {
                code: "im_emoji_cat",
                en: "[Cat]",
                cn: "[猫]"
            }, {
                code: "im_emoji_husky",
                en: "[Husky]",
                cn: "[二哈]"
            }], [{
                code: "im_emoji_doge",
                en: "[Doge]",
                cn: "[狗]"
            }, {
                code: "im_emoji_gift",
                en: "[Gift]",
                cn: "[礼物]"
            }, {
                code: "im_emoji_birthday",
                en: "[Birthday]",
                cn: "[生日]"
            }, {
                code: "im_emoji_stars_eye",
                en: "[StarsEye]",
                cn: "[星星眼uu]"
            }, {
                code: "im_emoji_blink",
                en: "[Blink]",
                cn: "[眨眼]"
            }, {
                code: "im_emoji_like_uu",
                en: "[LikeUu]",
                cn: "[喜欢uu]"
            }, {
                code: "im_emoji_why",
                en: "[Why]",
                cn: "[问号uu]"
            }, {
                code: "im_emoji_frighten",
                en: "[Frighten]",
                cn: "[吓]"
            }, {
                code: "im_emoji_dizzy",
                en: "[Dizzy]",
                cn: "[晕uu]"
            }, {
                code: "im_emoji_shy_uu",
                en: "[ShyUu]",
                cn: "[害羞uu]"
            }, {
                code: "im_emoji_push_glasses",
                en: "[PushGlasses]",
                cn: "[托眼镜]"
            }]]
            , T = [[{
                code: "im_emoji_grin",
                en: "[Grin]",
                cn: "[龇牙]"
            }, {
                code: "im_emoji_joyful",
                en: "[Joyful]",
                cn: "[愉快]"
            }, {
                code: "im_emoji_smile",
                en: "[Smile]",
                cn: "[微笑]"
            }, {
                code: "im_emoji_trick",
                en: "[Trick]",
                cn: "[坏笑]"
            }, {
                code: "im_emoji_askance",
                en: "[Askance]",
                cn: "[斜眼看]"
            }, {
                code: "im_emoji_chukle",
                en: "[Chukle]",
                cn: "[偷笑]"
            }, {
                code: "imkit_emoji_jingxi",
                en: "[Surprising]",
                cn: "[惊喜]"
            }, {
                code: "imkit_emoji_ye",
                en: "[Yeah!]",
                cn: "[耶]"
            }, {
                code: "imkit_emoji_yanjing",
                en: "[Glasses]",
                cn: "[眼镜]"
            }, {
                code: "im_emoji_tiptop",
                en: "[Tiptop]",
                cn: "[强]"
            }, {
                code: "im_emoji_clap",
                en: "[Clap]",
                cn: "[鼓掌]"
            }, {
                code: "im_emoji_kiss",
                en: "[Kiss]",
                cn: "[亲亲]"
            }, {
                code: "im_emoji_congratulate",
                en: "[Congratulate]",
                cn: "[庆祝]"
            }, {
                code: "imkit_emoji_xingxingyan",
                en: "[StarEyes]",
                cn: "[星星眼]"
            }, {
                code: "imkit_emoji_aixinlian",
                en: "[LoveFace]",
                cn: "[爱心脸]"
            }, {
                code: "imkit_emoji_666",
                en: "[Awesome]",
                cn: "[666]"
            }, {
                code: "im_emoji_go_fot_it",
                en: "[GoFotIt]",
                cn: "[加油]"
            }, {
                code: "im_emoji_like",
                en: "[Like]",
                cn: "[喜欢]"
            }, {
                code: "im_emoji_happy",
                en: "[Happy]",
                cn: "[开心]"
            }, {
                code: "im_emoji_think",
                en: "[Think]",
                cn: "[思考]"
            }, {
                code: "im_emoji_salute",
                en: "[Salute]",
                cn: "[抱拳]"
            }], [{
                code: "imkit_emoji_ganben",
                en: "[Cheers]",
                cn: "[干杯]"
            }, {
                code: "imkit_emoji_jianshen",
                en: "[Fitness]",
                cn: "[健身]"
            }, {
                code: "im_emoji_sob",
                en: "[Sob]",
                cn: "[哭]"
            }, {
                code: "imkit_emoji_bucuowo",
                en: "[GoodJob]",
                cn: "[不错哦]"
            }, {
                code: "imkit_emoji_linggan",
                en: "[Idea]",
                cn: "[灵感]"
            }, {
                code: "im_emoji_smart",
                en: "[Smart]",
                cn: "[机智]"
            }, {
                code: "im_emoji_shy",
                en: "[Shy]",
                cn: "[害羞]"
            }, {
                code: "imkit_emoji_hekele",
                en: "[Coke]",
                cn: "[喝可乐]"
            }, {
                code: "imkit_emoji_tianshi",
                en: "[Angel]",
                cn: "[天使]"
            }, {
                code: "imkit_emoji_mojing",
                en: "[CoolGuy]",
                cn: "[墨镜]"
            }, {
                code: "imkit_emoji_gongzuo",
                en: "[Work]",
                cn: "[工作]"
            }, {
                code: "im_emoji_scowl",
                en: "[Scowl]",
                cn: "[呆]"
            }, {
                code: "im_emoji_onlooker",
                en: "[Onlooker]",
                cn: "[吃瓜]"
            }, {
                code: "imkit_emoji_tanshou",
                en: "[Helpless]",
                cn: "[摊手]"
            }, {
                code: "im_emoji_confusion",
                en: "[Confusion]",
                cn: "[疑惑]"
            }, {
                code: "imkit_emoji_yes_sir",
                en: "[YesSir]",
                cn: "[yesSir]"
            }, {
                code: "im_emoji_eyepalm",
                en: "[Eyepalm]",
                cn: "[捂眼]"
            }, {
                code: "im_emoji_surprise",
                en: "[Surprise]",
                cn: "[惊讶]"
            }, {
                code: "im_emoji_afraid",
                en: "[Afraid]",
                cn: "[害怕]"
            }, {
                code: "im_emoji_love",
                en: "[Love]",
                cn: "[爱心]"
            }, {
                code: "im_emoji_thumbs_up",
                en: "[ThumbsUp]",
                cn: "[点赞]"
            }], [{
                code: "imkit_emoji_dadianhua",
                en: "[Call]",
                cn: "[打电话]"
            }, {
                code: "imkit_emoji_jiandaoshou",
                en: "[Victory]",
                cn: "[剪刀手]"
            }, {
                code: "imkit_emoji_bixin",
                en: "[Heart]",
                cn: "[比心]"
            }, {
                code: "imkit_emoji_paishou",
                en: "[Clapping]",
                cn: "[拍手]"
            }, {
                code: "imkit_emoji_woshou",
                en: "[ShakeHands]",
                cn: "[握手]"
            }, {
                code: "im_emoji_hands_up",
                en: "[HandsUp]",
                cn: "[举手]"
            }, {
                code: "im_emoji_ok",
                en: "[Ok]",
                cn: "[ok]"
            }, {
                code: "im_emoji_beg",
                en: "[Beg]",
                cn: "[合掌]"
            }, {
                code: "imkit_emoji_huo",
                en: "[Fire]",
                cn: "[火]"
            }, {
                code: "imkit_emoji_huashu",
                en: "[Flower]",
                cn: "[花束]"
            }, {
                code: "imkit_emoji_shui",
                en: "[Drops]",
                cn: "[水]"
            }, {
                code: "imkit_emoji_gantanhao",
                en: "[!]",
                cn: "[感叹号]"
            }, {
                code: "imkit_emoji_wenhao",
                en: "[?]",
                cn: "[问号]"
            }, {
                code: "imkit_emoji_yibaifen",
                en: "[100]",
                cn: "[一百分]"
            }, {
                code: "imkit_emoji_xingxing",
                en: "[Star]",
                cn: "[星星]"
            }, {
                code: "imkit_emoji_taiyang",
                en: "[Sun]",
                cn: "[太阳]"
            }, {
                code: "im_emoji_cat",
                en: "[Cat]",
                cn: "[猫]"
            }, {
                code: "im_emoji_husky",
                en: "[Husky]",
                cn: "[二哈]"
            }, {
                code: "im_emoji_doge",
                en: "[Doge]",
                cn: "[狗]"
            }, {
                code: "im_emoji_gift",
                en: "[Gift]",
                cn: "[礼物]"
            }, {
                code: "im_emoji_birthday",
                en: "[Birthday]",
                cn: "[生日]"
            }, {
                code: "im_emoji_stars_eye",
                en: "[StarsEye]",
                cn: "[星星眼uu]"
            }, {
                code: "im_emoji_blink",
                en: "[Blink]",
                cn: "[眨眼]"
            }], [{
                code: "im_emoji_like_uu",
                en: "[LikeUu]",
                cn: "[喜欢uu]"
            }, {
                code: "im_emoji_why",
                en: "[Why]",
                cn: "[问号uu]"
            }, {
                code: "im_emoji_frighten",
                en: "[Frighten]",
                cn: "[吓]"
            }, {
                code: "im_emoji_dizzy",
                en: "[Dizzy]",
                cn: "[晕uu]"
            }, {
                code: "im_emoji_shy_uu",
                en: "[ShyUu]",
                cn: "[害羞uu]"
            }, {
                code: "im_emoji_push_glasses",
                en: "[PushGlasses]",
                cn: "[托眼镜]"
            }]]
    },
    61917: function (e, t, n) {
        n.r(t),
            n.d(t, {
                ADD_QUOTE: function () {
                    return W
                },
                CHANGE_FIRST_TAB_EVENT: function () {
                    return U
                },
                CHANGE_MAIN_LEFT_TAB_EVENT: function () {
                    return M
                },
                CHANGE_TAB_EVENT: function () {
                    return I
                },
                CHECK_SESSION_MODE: function () {
                    return x
                },
                CLEAR_FILTER_EVENT: function () {
                    return b
                },
                CLOSE_SHOPPING: function () {
                    return z
                },
                CURRENT_CONVERSATION_CHANGE: function () {
                    return d
                },
                CURRENT_USER_TOKEN_INVALID: function () {
                    return j
                },
                CUSTOMER_READ_MESSAGE_EVENT: function () {
                    return Z
                },
                CUSTOM_POP_HIDE: function () {
                    return et
                },
                CUSTOM_POP_SHOW: function () {
                    return ee
                },
                DRAG_MASK_HIDE: function () {
                    return G
                },
                FINISH_CONVERSATION_EVENT: function () {
                    return _
                },
                GLOBAL_CLICK_EVENT: function () {
                    return R
                },
                GLOBAL_MOUSEDOWN_EVENT: function () {
                    return L
                },
                GLOBAL_MOUSEUP_EVENT: function () {
                    return P
                },
                GLOBAL_TAP_EVENT: function () {
                    return B
                },
                IM_SDK_CONNECT_CHANGE_EVENT: function () {
                    return r
                },
                IM_STATUS_CHANGE_EVENT: function () {
                    return h
                },
                INVITE_NEW_MEMBER_EVENT: function () {
                    return f
                },
                MAIN_SEND_NEW_MESSAGE_EVENT: function () {
                    return E
                },
                NEDD_UPDATE_NOTICE_LIST: function () {
                    return A
                },
                NEED_UPDATE_SINGLE_LIST: function () {
                    return O
                },
                NEW_CONVERSATION_AND_CHANGE_EVENT: function () {
                    return m
                },
                NEW_CONVERSATION_EVENT: function () {
                    return i
                },
                NEW_MESSAGE_EVENT: function () {
                    return o
                },
                NEW_SINGLE_CHAT_EVENT: function () {
                    return c
                },
                NEW_SYS_NOTICE_EVENT: function () {
                    return u
                },
                NEW_XINGQIU_CHAT_EVENT: function () {
                    return s
                },
                PREVIEW_IMAGE_MESSAGE_EVENT: function () {
                    return v
                },
                PREVIEW_SHOPPING: function () {
                    return q
                },
                PREVIEW_VIDEO_MESSAGE_EVENT: function () {
                    return C
                },
                PULL_MORE_HISTORY_MESSAGE_EVENT: function () {
                    return p
                },
                QUEUE_SESSION_LIST_EVENT: function () {
                    return N
                },
                REFRESH_CURRENT_GROUP_MEMBER_EVENT: function () {
                    return g
                },
                REPOET_OPEN_EVENT: function () {
                    return H
                },
                RESEND_MESSAGE_EVENT: function () {
                    return w
                },
                RESET_SENDBOX_EVENT: function () {
                    return D
                },
                RE_EDIT_RECALL: function () {
                    return J
                },
                SENDBOX_BLUR: function () {
                    return l
                },
                SENDBOX_SEND_NEW_MESSAGE_EVENT: function () {
                    return T
                },
                SEND_CONTENT_CARD: function () {
                    return en
                },
                SEND_SHOPPING_CARD: function () {
                    return Q
                },
                SHOW_FEEDBACK: function () {
                    return er
                },
                SHOW_INVITE: function () {
                    return V
                },
                SHOW_STAR_POPUP_EVENT: function () {
                    return y
                },
                SHOW_UPDATE_GROUPNAME: function () {
                    return K
                },
                SHOW_UPDATE_GROUPTIP: function () {
                    return X
                },
                UPDATE_CONV_GROUPNAME: function () {
                    return Y
                },
                UPDATE_CONV_GROUPTIP: function () {
                    return $
                },
                UPDATE_CONV_MEMBER_EVENT: function () {
                    return S
                },
                UPDATE_CONV_SESSIONID_EVENT: function () {
                    return k
                },
                UPDATE_INVITEINFO: function () {
                    return F
                },
                UPDATE_NOTICE_FOLDER: function () {
                    return eo
                },
                UPDATE_WAIT_CONVERSATION_EVENT: function () {
                    return a
                }
            }),
            n(13485);
        var o = "NEW_MESSAGE_EVENT"
            , r = "IM_SDK_CONNECT_CHANGE_EVENT"
            , i = "NEW_CONVERSATION_EVENT"
            , a = "UPDATE_WAIT_CONVERSATION_EVENT"
            , c = "NEW_SINGLE_CHAT_EVENT"
            , s = "NEW_XINGQIU_CHAT_EVENT"
            , u = "NEW_SYS_NOTICE_EVENT"
            , d = "CURRENT_CONVERSATION_CHANGE"
            , l = "SENDBOX_BLUR"
            , m = "NEW_CONVERSATION_AND_CHANGE_EVENT"
            , y = "SHOW_STAR_POPUP_EVENT"
            , h = "IM_STATUS_CHANGE_EVENT"
            , _ = "FINISH_CONVERSATION_EVENT"
            , f = "INVITE_NEW_MEMBER_EVENT"
            , g = "REFRESH_CURRENT_GROUP_MEMBER_EVENT"
            , p = "PULL_MORE_HISTORY_MESSAGE_EVENT"
            , w = "RESEND_MESSAGE_EVENT"
            , v = "PREVIEW_IMAGE_MESSAGE_EVENT"
            , C = "PREVIEW_VIDEO_MESSAGE_EVENT"
            , Z = "CUSTOMER_READ_MESSAGE_EVENT"
            , T = "SENDBOX_SEND_NEW_MESSAGE_EVENT"
            , E = "MAIN_SEND_NEW_MESSAGE_EVENT"
            , k = "UPDATE_CONV_SESSIONID_EVENT"
            , S = "UPDATE_CONV_MEMBER_EVENT"
            , M = "CHANGE_MAIN_LEFT_TAB_EVENT"
            , I = "CHANGE_TAB_EVENT"
            , N = "QUEUE_SESSION_LIST_EVENT"
            , b = "CLEAR_FILTER_EVENT"
            , O = "NEED_UPDATE_SINGLE_LIST"
            , A = "NEDD_UPDATE_NOTICE_LIST"
            , j = "CURRENT_USER_TOKEN_INVALID"
            , L = "GLOBAL_MOUSEDOWN_EVENT"
            , P = "GLOBAL_MOUSEUP_EVENT"
            , R = "GLOBAL_CLICK_EVENT"
            , B = "GLOBAL_TAP_EVENT"
            , U = "CHANGE_FIRST_TAB_EVENT"
            , x = "CHECK_SESSION_MODE"
            , D = "RESET_SENDBOX_EVENT"
            , G = "DRAG_MASK_HIDE"
            , V = "SHOW_INVITE"
            , F = "UPDATE_INVITEINFO"
            , H = "REPOET_OPEN_EVENT"
            , W = "ADD_QUOTE"
            , J = "RE_EDIT_RECALL"
            , q = "PREVIEW_SHOPPING"
            , z = "CLOSE_SHOPPING"
            , Q = "SEND_SHOPPING_CARD"
            , K = "SHOW_UPDATE_GROUPNAME"
            , Y = "UPDATE_CONV_GROUPNAME"
            , X = "SHOW_UPDATE_GROUPTIP"
            , $ = "UPDATE_CONV_GROUPTIP"
            , ee = "CUSTOM_POP_SHOW"
            , et = "CUSTOM_POP_HIDE"
            , en = "SEND_CONTENT_CARD"
            , eo = "UPDATE_NOTICE_FOLDER"
            , er = "SHOW_FEEDBACK"
    },
    61023: function (e, t, n) {
        var o = n(61917)
            , r = n(23380)
            , i = n(60414);
        n(13485);
        var a = {
            1: i.Z.get("commentVeryBad", "", "很差"),
            2: i.Z.get("commentBad", "", "差"),
            3: i.Z.get("commentNormal", "", "一般"),
            4: i.Z.get("commentGood", "", "好"),
            5: i.Z.get("commentVeryGood", "", "很好"),
            6: i.Z.get("commentTxt1", "", "不满意"),
            7: i.Z.get("commentTxt2", "", "一般"),
            8: i.Z.get("commentTxt3", "", "满意")
        }
            , c = {
                1: i.Z.get("commentVeryBadLong", "", "非常不满意，各方面都很差"),
                2: i.Z.get("commentBadLong", "", "不满意，比较差"),
                3: i.Z.get("commentNormalLong", "", "一般，还需改善"),
                4: i.Z.get("commentGoodLong", "", "比较满意，仍可改善"),
                5: i.Z.get("commentVeryGoodLong", "", "非常满意，无可挑剔")
            };
        t.Z = {
            EventEnum: o,
            UBTTypeEnum: {
                CHANGE_IM_STATUS: "102851",
                IM_WS_STATUS_CHANGE: "102852",
                CONV_LIST_TAB_CHANGE: "102853",
                CONV_STATUS_TAB_CHANGE: "102854",
                CONV_MESSAGE_CLICK: "102855",
                SEND_MESSAGE: "102856",
                RECIEVE_MESSAGE: "102857",
                AJAX_ERROR: "103390",
                NEW_CONV: "103415",
                TRANSLATE_COUNT: "104101",
                CATCHED_ERR: "103631",
                TRANSLATE_CLICK: "112955",
                TRANSLATE_LIKE: "112956",
                TRANSLATE_NOLIKE: "112957",
                TRANSLATE_RESLUT: "112958",
                COLLECT: "171366",
                COLLECT_SELECT: "171367",
                COLLECT_SEND: "171368",
                REPEAT_COLLECT: "171369",
                AI_RES_SEND: "174026",
                AI_USE: "174048",
                MESSAGE_CARD: "186079",
                QUOTE: "188510",
                UPLOAD_VIDEO: "188505",
                XQH_SHOPPING: "190468",
                QUICKREPLY: "197647",
                QUICKREPLYASSO: "197648",
                RECALL_EXPOSURE: "265421",
                RECALL: "265422"
            },
            LogTypeEnum: {
                online_source: "online_source",
                ajax_status: "ajax_status",
                socket_status: "socket_status",
                socket_connect_status: "socket_connect_status",
                login_status: "login_status",
                query_message: "query_message",
                send_message: "send_message",
                upload_img: "upload_img",
                upload_file: "upload_file",
                upload_video: "upload_video",
                message_count_source: "message_count_source",
                received_message: "received_message",
                image_load_fail: "image_load_fail",
                sync_unread_count: "sync_unread_count",
                create_chat: "create_chat"
            },
            devUBTTypeEnum: {
                MSG_CARD_ERR: "b_msg_card_err_boundary",
                SOA_API: "o_im_b_soa_api"
            },
            CommonEnum: r,
            AcceptedFileType: "image/jpeg,image/png,image/gif,image/webp,application/pdf,application/msword,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.openxmlformats-officedocument.wordprocessingml.document,.csv",
            ImgAllowType: ["image/jpeg", "image/png", "image/gif", "image/webp"],
            VideoAllowType: ["video/mp4"],
            FileAllowObj: {
                "application/pdf": "pdf",
                "application/msword": "word",
                "application/vnd.ms-excel": "excel",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "excel",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "word"
            },
            MessageType: {
                AtMessage: 9,
                AudioMessage: 4,
                CardMessage: 2,
                CustomContentMessage: 7,
                EmoticonMessage: 8,
                FileMessage: 5,
                ImageMessage: 1,
                InputStateMessage: -4,
                LocationMessage: 6,
                NormalMessage: -1,
                StatusMessage: -3,
                SystemMessage: -2,
                TemplateMessage: 10,
                TextMessage: 0,
                VideoMessage: 3,
                AgentOnlyVisibleMessage: 1012
            },
            NormalActionCode: ["NBZ22"],
            NeedTranslateCode: ["CBZ06", "CBZ19", "CBZ12", "CBZ15", "CBZ21", "CBZ28", "CBZ42", "CBZ42", "CBZ04", "CBZ56", "CBZ48", "CBZ38", "NBZ11", "NBZ22", "NBZ23", "NBZ18", "NBZ16", "NBZ14", "NBZ12", "NBZ37", "CBZ10", "CBZ16", "CTL01", "CTL05", "NBZ73", "NBZ42", "NBZ11", "NBZ12", "NBZ13", "NBZ14", "NBZ16", "NBZ17", "NBZ18", "NBZ20"],
            CommentScoreTips: a,
            CommentScoreLongTips: c,
            DefaultHeadImg: "//pic.c-ctrip.com/h5/socialization/default.png",
            DefaultBrokenImg: "//dimg04.c-ctrip.com/images/Z00l0z000000mlnf18BBC.jpg",
            ActionCodesRefreshGroupMember: ["NBZ10", "NBZ30", "NBZ37", "NBZ12", "NBZ99", "CBZ19"],
            messageTypeRefreshGroupMember: ["1001", "1002", "1003"],
            HTMLNodeType: {
                ELEMENT_NODE: 1,
                TEXT_NODE: 3,
                CDATA_SECTION_NODE: 4,
                PROCESSING_INSTRUCTION_NODE: 7,
                COMMENT_NODE: 8,
                DOCUMENT_NODE: 9,
                DOCUMENT_TYPE_NODE: 10,
                DOCUMENT_FRAGMENT_NODE: 11
            },
            HostedEnvironments: {
                Browser: "Browser",
                CChat: "CChat"
            },
            QuickReplayType: {
                vagent: "VAGENT",
                vSkillGroup: "VSKILLGROUP"
            }
        }
    },
    92557: function (e, t, n) {
        var o = n(11025)
            , r = n(74213)
            , i = n(47813)
            , a = n(2037)
            , c = n(61917)
            , s = n(8797)
            , u = n(53122)
            , d = n(34719)
            , l = n(89312)
            , m = n(60414)
            , y = n(61023)
            , h = n(73383);
        n(32336),
            n(7315);
        var _ = n(48978)
            , f = n(46246);
        n(13485);
        var g = function () {
            function e() {
                (0,
                    o.Z)(this, e)
            }
            return (0,
                r.Z)(e, null, [{
                    key: "checkResponse",
                    value: function (t) {
                        try {
                            window._n("2ey")
                        } catch (n) { }
                        var o = t.conversationList || []
                            , r = t.agentTodoTaskList;
                        o.length > 0 && (o = s.Z.sortConversationList(o),
                            e.onlyUnreadCount || (u.Z.blinkBrowserTitle(m.Z.get("newConversationOrChange", "", "您有新会话或会话状态有变更")),
                                d.Z.playNewConversationTone()),
                            a.Z.publish(c.NEW_CONVERSATION_EVENT, [o]),
                            e.asqueue.enqueue(o)),
                            r && a.Z.publish(c.UPDATE_WAIT_CONVERSATION_EVENT, [r]);
                        var i = t.currentStatus
                            , l = "k_".concat(i);
                        i = y.Z.CommonEnum.WorkStatusKeyMap[l] ? y.Z.CommonEnum.WorkStatusKeyMap[l] : y.Z.CommonEnum.WorkStatusKeyMap.k_3,
                            a.Z.publish(c.IM_STATUS_CHANGE_EVENT, [i])
                    }
                }, {
                    key: "init",
                    value: function (t) {
                        try {
                            window._n("pLm")
                        } catch (n) { }
                        if (e.inited)
                            return Promise.resolve(!0);
                        h.Z.signleChatModel.addListener(function (e) {
                            try {
                                window._n("r2u")
                            } catch (t) { }
                            var n = e.messages || [];
                            n = n.map(function (e) {
                                try {
                                    window._n("PYP")
                                } catch (t) { }
                                return s.Z.singleMessageToConv(e, !1, y.Z.CommonEnum.ConversationType.SingleChat)
                            }),
                                a.Z.publish(y.Z.EventEnum.NEW_SINGLE_CHAT_EVENT, [n])
                        }),
                            h.Z.xingqiuChatModel.addListener(function (e) {
                                try {
                                    window._n("oUv")
                                } catch (t) { }
                                var n = e.conversationList || [];
                                n = n.map(function (e) {
                                    try {
                                        window._n("I2C")
                                    } catch (t) { }
                                    return s.Z.xingqiuMessageToConv(e, !1, y.Z.CommonEnum.ConversationType.XingqiuChat)
                                }),
                                    a.Z.publish(y.Z.EventEnum.NEW_XINGQIU_CHAT_EVENT, [n])
                            }),
                            e.onlyUnreadCount = !!t,
                            e.inited = !0
                    }
                }, {
                    key: "loop",
                    value: function () {
                        try {
                            window._n("jTl")
                        } catch (t) { }
                        return h.Z.conversationModel.loop({
                            interval: 5e3,
                            success: function (t) {
                                try {
                                    window._n("3u0")
                                } catch (n) { }
                                e.checkResponse(t)
                            },
                            fail: function (e) {
                                try {
                                    window._n("mZg")
                                } catch (t) { }
                                (0,
                                    f.y)(e) && _.Z.logout(e)
                            },
                            params: {
                                needTodoList: 1
                            }
                        })
                    }
                }, {
                    key: "ping",
                    value: function (t) {
                        try {
                            window._n("B6M")
                        } catch (n) { }
                        e.onlyUnreadCount = !!t,
                            h.Z.conversationModel.ping().then(function (t) {
                                try {
                                    window._n("oYL")
                                } catch (n) { }
                                e.checkResponse(t)
                            })
                    }
                }, {
                    key: "pingSingleChat",
                    value: function () {
                        try {
                            window._n("DMm")
                        } catch (e) { }
                        h.Z.signleChatModel.ping()
                    }
                }, {
                    key: "pingXingqiuChat",
                    value: function () {
                        try {
                            window._n("fjP")
                        } catch (e) { }
                        h.Z.xingqiuChatModel.ping()
                    }
                }, {
                    key: "pingSysNotice",
                    value: function () { }
                }]),
                e
        }();
        (0,
            i.Z)(g, "inited", !1),
            (0,
                i.Z)(g, "onlyUnreadCount", !1),
            (0,
                i.Z)(g, "asqueue", new l.Z(function (e) {
                    try {
                        window._n("Myn")
                    } catch (t) { }
                    return s.Z.mapConversationList(e, {
                        onlyUnreadCount: g.onlyUnreadCount
                    }).then(function (e) {
                        try {
                            window._n("vpy")
                        } catch (t) { }
                        a.Z.publish(c.NEW_CONVERSATION_EVENT, [e])
                    }).catch(function (e) {
                        try {
                            window._n("FpU")
                        } catch (t) { }
                    })
                }
                    , 10)),
            t.Z = g
    },
    61210: function (e, t, n) {
        n.d(t, {
            Z: function () {
                return _
            }
        });
        var o = n(47813)
            , r = n(11025)
            , i = n(74213)
            , a = n(73383)
            , c = n(61023)
            , s = n(85943)
            , u = n(60414)
            , d = n(80590);
        function l(e, t) {
            var n = Object.keys(e);
            if (Object.getOwnPropertySymbols) {
                var o = Object.getOwnPropertySymbols(e);
                t && (o = o.filter(function (t) {
                    return Object.getOwnPropertyDescriptor(e, t).enumerable
                })),
                    n.push.apply(n, o)
            }
            return n
        }
        function m(e) {
            for (var t = 1; t < arguments.length; t++) {
                var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? l(Object(n), !0).forEach(function (t) {
                    (0,
                        o.Z)(e, t, n[t])
                }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : l(Object(n)).forEach(function (t) {
                    Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                })
            }
            return e
        }
        n(13485);
        var y = {}
            , h = null
            , _ = function () {
                function e() {
                    (0,
                        r.Z)(this, e)
                }
                return (0,
                    i.Z)(e, null, [{
                        key: "setAgentStaus",
                        value: function (e) {
                            try {
                                window._n("uD8")
                            } catch (t) { }
                            if (c.Z.CommonEnum.WorkStatus.hasOwnProperty(e)) {
                                var n = {
                                    agentStatus: c.Z.CommonEnum.WorkStatus[e].key
                                };
                                return a.Z.setVenAgentStatus.post(n)
                            }
                        }
                    }, {
                        key: "getAgentInfo",
                        value: function () {
                            try {
                                window._n("2EY")
                            } catch (e) { }
                            return a.Z.userInfoModel.getCurrentUserInfo().then(function (e) {
                                try {
                                    window._n("R4M")
                                } catch (t) { }
                                if ((0,
                                    d.L)(window.page_id || ""),
                                    h = e,
                                    !e.ctripUid)
                                    throw s.Z.init({
                                        showSystemNoticeTab: !0,
                                        showSingleChatTab: !0,
                                        systemNoticeBizTypes: ["118"]
                                    }),
                                    "none user info";
                                if (1 == e.disable)
                                    throw "user is disabled";
                                var n = m(m({}, e), {}, {
                                    uid: e.ctripUid
                                });
                                return s.Z.init(n),
                                    y = n,
                                    n
                            })
                        }
                    }, {
                        key: "formatUid",
                        value: function (e) {
                            try {
                                window._n("Qo4")
                            } catch (t) { }
                            for (var n = Math.round(e.length / 3), o = e.substr(0, n), r = "", i = e.substr(2 * n + 1), a = 0; a++ < n + 1;)
                                r += "*";
                            return o + r + i
                        }
                    }, {
                        key: "getDisplayName",
                        value: function (t, n) {
                            try {
                                window._n("J8x")
                            } catch (o) { }
                            if (!t)
                                return "";
                            if (t.uid && t.uid.toLowerCase() == (y.uid || "").toLowerCase())
                                return e.getSharkName(y.nickname) || y.uid;
                            if (t) {
                                var r = e.getSharkName(t.remark || t.nick || "")
                                    , i = e.formatUid(t.uid || "");
                                return !n && r && i ? "".concat(r, "(").concat(i, ")") : r || i
                            }
                            return ""
                        }
                    }, {
                        key: "getQuoteName",
                        value: function (t) {
                            try {
                                window._n("B6Y")
                            } catch (n) { }
                            if (!t)
                                return "";
                            if (t.uid && t.uid.toLowerCase() == (y.uid || "").toLowerCase())
                                return e.getSharkName(y.nickname) || y.uid;
                            if (t) {
                                var o = e.getSharkName(t.nick || "")
                                    , r = e.formatUid(t.uid || "");
                                return o || r
                            }
                            return ""
                        }
                    }, {
                        key: "getSharkName",
                        value: function (e) {
                            try {
                                window._n("ckk")
                            } catch (t) { }
                            return "key.im.servicechat.robotnickname" == e ? u.Z.get("key.im.servicechat.robotnickname", "", "") : e
                        }
                    }, {
                        key: "cancelGetAgentWorkingSchedule",
                        value: function () {
                            try {
                                window._n("heT")
                            } catch (e) { }
                            a.Z.getAgentWorkingSchedule.cancel()
                        }
                    }, {
                        key: "getAgentWorkingSchedule",
                        value: function (e) {
                            try {
                                window._n("fNa")
                            } catch (t) { }
                            return a.Z.getAgentWorkingSchedule.cancel(),
                                a.Z.getAgentWorkingSchedule.post({
                                    venAgentUid: e.ctripUid
                                })
                        }
                    }, {
                        key: "editWorkTimeInfo",
                        value: function (e) {
                            try {
                                window._n("Osk")
                            } catch (t) { }
                            return a.Z.modifyAgentWorkingSchedule.post({
                                workingSchedule: e
                            })
                        }
                    }, {
                        key: "getTimeZoomList",
                        value: function (e) {
                            try {
                                window._n("OSM")
                            } catch (t) { }
                            return a.Z.getTimeZoomList.post({
                                locale: e || ""
                            }).then(function (e) {
                                try {
                                    window._n("g70")
                                } catch (t) { }
                                var n = e.timeZoneList;
                                return {
                                    val: n.map(function (e, t) {
                                        try {
                                            window._n("ssi")
                                        } catch (n) { }
                                        return e.defaultOffset
                                    }),
                                    name: n.map(function (e, t) {
                                        try {
                                            window._n("iMO")
                                        } catch (n) { }
                                        return e.timeZoneDisplayName
                                    })
                                }
                            })
                        }
                    }, {
                        key: "getCurrentUser",
                        value: function () {
                            try {
                                window._n("uCL")
                            } catch (e) { }
                            return h || {}
                        }
                    }, {
                        key: "getAutoReplayWords",
                        value: function () {
                            try {
                                window._n("1d3")
                            } catch (e) { }
                            return a.Z.getAutoReplayWords.post({})
                        }
                    }, {
                        key: "saveAutoReplayWords",
                        value: function (e) {
                            try {
                                window._n("hve")
                            } catch (t) { }
                            return a.Z.saveAutoReplayWords.post(e)
                        }
                    }, {
                        key: "getVenAgentLocaleList",
                        value: function (e) {
                            try {
                                window._n("ukp")
                            } catch (t) { }
                            return a.Z.getVenAgentLocaleList.post(e)
                        }
                    }]),
                    e
            }()
    },
    48978: function (e, t, n) {
        var o = n(11025)
            , r = n(74213)
            , i = n(73383)
            , a = n(32336)
            , c = n(61023);
        n(13485);
        var s = function () {
            function e() {
                (0,
                    o.Z)(this, e)
            }
            return (0,
                r.Z)(e, null, [{
                    key: "login",
                    value: function (e, t, n) {
                        try {
                            window._n("dfn")
                        } catch (o) { }
                        return i.Z.genTrippalLink.post(e).then(function (e) {
                            try {
                                window._n("vp3")
                            } catch (n) { }
                            t && t(e.linkUrl)
                        }).catch(function (e) {
                            try {
                                window._n("vHv")
                            } catch (t) { }
                            n && n()
                        })
                    }
                }, {
                    key: "logout",
                    value: function (e) {
                        try {
                            window._n("Xhb")
                        } catch (t) { }
                        var n = new a.Z
                            , o = e || {}
                            , r = o.errNo
                            , i = o.error
                            , s = ""
                            , u = "";
                        i ? (s = i,
                            u = e.errmsg) : (s = r,
                                u = e.errMsg),
                            n.send({
                                type: c.Z.LogTypeEnum.login_status,
                                status: "fail",
                                type_desc: e || "err is null",
                                errCode: s,
                                errMsg: u
                            }),
                            window.beforeunloadHandler && window.removeEventListener("beforeunload", window.beforeunloadHandler),
                            location.href = "authfail"
                    }
                }]),
                e
        }();
        t.Z = s
    },
    85943: function (e, t, n) {
        n.d(t, {
            Z: function () {
                return y
            }
        });
        var o = n(11025)
            , r = n(74213)
            , i = n(61023)
            , a = n(57770);
        n(13485);
        var c = !1
            , s = {}
            , u = {}
            , d = {}
            , l = {}
            , m = !1
            , y = function () {
                function e() {
                    (0,
                        o.Z)(this, e)
                }
                return (0,
                    r.Z)(e, null, [{
                        key: "init",
                        value: function (e) {
                            try {
                                window._n("vF9")
                            } catch (t) { }
                            c = !0;
                            for (var n = (s = e || {}).venAgentOption || {}, o = n.serviceTypeOptionList || [], r = (n.agentOption || {}).venOptionList || [], i = n.vendorOptionList || [], a = 0; a < o.length; a++) {
                                var m = o[a] || {}
                                    , y = m.venOptionList || [];
                                u[m.ownerCode] = {};
                                for (var h = 0; h < y.length; h++) {
                                    var _ = y[h].optionCode
                                        , f = y[h].optionValue;
                                    u[m.ownerCode][_] = f
                                }
                            }
                            for (var g = 0; g < r.length; g++) {
                                var p = r[g].optionCode
                                    , w = r[g].optionValue;
                                d[p] = w
                            }
                            for (var v = 0; v < i.length; v++) {
                                var C = i[v].ownerCode
                                    , Z = i[v].venOptionList;
                                l[C] = Z
                            }
                        }
                    }, {
                        key: "updateAgentOptions",
                        value: function (e, t) {
                            try {
                                window._n("wNB")
                            } catch (n) { }
                            d[e] = t
                        }
                    }, {
                        key: "canASKAI",
                        value: function (e) {
                            try {
                                window._n("aQp")
                            } catch (t) { }
                            return !e || !e.currentConversationRole || 2 != e.currentConversationRole
                        }
                    }, {
                        key: "canComment",
                        value: function (e) {
                            try {
                                window._n("rUh")
                            } catch (t) { }
                            var n = e.serviceType
                                , o = u[n] && "Y" == u[n].support_score;
                            return (e.conversationType != i.Z.CommonEnum.ConvType.B2B || 2 != e.currentConversationRole) && o && e.chatType !== i.Z.CommonEnum.ConversationType.SingleChat && e.chatType !== i.Z.CommonEnum.ConversationType.Notice && "xqhprivatechat" !== e.convtype && e.conversationType != i.Z.CommonEnum.ConvType.O2B && i.Z.CommonEnum.ConvTabs.BOP.convTypes.indexOf(e.conversationType) >= 0
                        }
                    }, {
                        key: "supportPullCtripAgent",
                        value: function (e) {
                            try {
                                window._n("XvS")
                            } catch (t) { }
                            var n = (l[e.vendorId] || []).find(function (e) {
                                try {
                                    window._n("RK8")
                                } catch (t) { }
                                return "supportPullCtripAgent" == e.optionCode
                            });
                            return !!n && "Y" == n.optionValue
                        }
                    }, {
                        key: "canPushComment",
                        value: function (e) {
                            try {
                                window._n("fZb")
                            } catch (t) { }
                            return (e.conversationType != i.Z.CommonEnum.ConvType.B2B || 2 != e.currentConversationRole) && e.chatType !== i.Z.CommonEnum.ConversationType.SingleChat && e.chatType !== i.Z.CommonEnum.ConversationType.Notice && i.Z.CommonEnum.ConvTabs.BOP.convTypes.indexOf(e.conversationType) >= 0
                        }
                    }, {
                        key: "canFinishConv",
                        value: function (e) {
                            try {
                                window._n("Dud")
                            } catch (t) { }
                            var n = e.serviceType
                                , o = (u[n] && u[n].agent_closeable_chat || "1|1").split("|");
                            return ({
                                consultant: 1 == o[0],
                                service: 1 == o[1]
                            })[1 == (e.currentConversationRole || 1) ? "consultant" : "service"] && e.chatType !== i.Z.CommonEnum.ConversationType.SingleChat && e.chatType !== i.Z.CommonEnum.ConversationType.Notice && "xqhprivatechat" !== e.convtype && i.Z.CommonEnum.ConvStatus.ACTIVE.values.indexOf(e.status) >= 0
                        }
                    }, {
                        key: "canChat",
                        value: function (e) {
                            try {
                                window._n("Ypc")
                            } catch (t) { }
                            return i.Z.CommonEnum.ConvStatus.ACTIVE.values.indexOf(e.status) >= 0 || this.canReversStartChat(e)
                        }
                    }, {
                        key: "canReversStartChat",
                        value: function (e) {
                            try {
                                window._n("RRv")
                            } catch (t) { }
                            return (s.bizTypesAllowActiveWhenClose || []).indexOf(e.serviceType) >= 0
                        }
                    }, {
                        key: "finishConvNeedCheckMember",
                        value: function (e) {
                            try {
                                window._n("Vfn")
                            } catch (t) { }
                            return (s.c2BCloseTransferMasterAgentBizTypes || []).indexOf(e.serviceType) >= 0 && i.Z.CommonEnum.ConvTabs.BC.convTypes.indexOf(e.conversationType) >= 0
                        }
                    }, {
                        key: "isShowSingleChat",
                        value: function () {
                            try {
                                window._n("JcK")
                            } catch (e) { }
                            return !!s.showSingleChatTab
                        }
                    }, {
                        key: "setAwaysShowXQTab",
                        value: function (e) {
                            try {
                                window._n("uru")
                            } catch (t) { }
                            m = e
                        }
                    }, {
                        key: "isAlwaysShowXQTab",
                        value: function () {
                            try {
                                window._n("UZh")
                            } catch (e) { }
                            return m
                        }
                    }, {
                        key: "isShowXingQiuAccount",
                        value: function () {
                            try {
                                window._n("bJ0")
                            } catch (e) { }
                            return !!d.xing_qiu_hao && "Y" == d.xing_qiu_hao
                        }
                    }, {
                        key: "isXingQiuConv",
                        value: function (e) {
                            try {
                                window._n("FiD")
                            } catch (t) { }
                            return "1201" == e || "1202" == e
                        }
                    }, {
                        key: "isShowSystemNotify",
                        value: function () {
                            try {
                                window._n("FYD")
                            } catch (e) { }
                            return !!s.showSystemNoticeTab
                        }
                    }, {
                        key: "isShowCustomerAera",
                        value: function (e) {
                            try {
                                window._n("vTY")
                            } catch (t) { }
                            return e.chatType !== i.Z.CommonEnum.ConversationType.SingleChat && e.chatType !== i.Z.CommonEnum.ConversationType.Notice && "xqhprivatechat" !== e.convtype && !0 != e.isHideCustomerArea
                        }
                    }, {
                        key: "isShowGroupMember",
                        value: function (e) {
                            try {
                                window._n("W1P")
                            } catch (t) { }
                            return e.chatType !== i.Z.CommonEnum.ConversationType.SingleChat && e.chatType !== i.Z.CommonEnum.ConversationType.Notice && "xqhprivatechat" !== e.convtype && i.Z.CommonEnum.ConvStatus.ACTIVE.values.indexOf(e.status) >= 0
                        }
                    }, {
                        key: "isCanInvite",
                        value: function (e) {
                            try {
                                window._n("UFP")
                            } catch (t) { }
                            var n = e.serviceType
                                , o = this.inviteAllowed(e);
                            return e.chatType !== i.Z.CommonEnum.ConversationType.SingleChat && e.chatType !== i.Z.CommonEnum.ConversationType.Notice && "xqhprivatechat" !== e.convtype && 2 === e.status && (o.OP || o.AGENT) && (!u[n] || "Y" == u[n].show_invite_page || !u[n].show_invite_page)
                        }
                    }, {
                        key: "needCheckSessionMode",
                        value: function (e) {
                            try {
                                window._n("IHS")
                            } catch (t) { }
                            return e.chatType !== i.Z.CommonEnum.ConversationType.SingleChat && e.chatType !== i.Z.CommonEnum.ConversationType.Notice && "xqhprivatechat" !== e.convtype && i.Z.CommonEnum.ConvStatus.ACTIVE.values.indexOf(e.status) >= 0
                        }
                    }, {
                        key: "isShowMessageReadStatus",
                        value: function (e) {
                            try {
                                window._n("tVe")
                            } catch (t) { }
                            if (4 == e.status)
                                return !1;
                            var n = e.serviceType;
                            return e.chatType !== i.Z.CommonEnum.ConversationType.SingleChat && e.chatType !== i.Z.CommonEnum.ConversationType.Notice && "xqhprivatechat" !== e.convtype && (!u[n] || "Y" == u[n].display_msg_read_status || !u[n].display_msg_read_status)
                        }
                    }, {
                        key: "isSysNoticeMessage",
                        value: function (e) {
                            try {
                                window._n("MKK")
                            } catch (t) { }
                            return (s.systemNoticeBizTypes || []).indexOf(e.bizType + "") >= 0
                        }
                    }, {
                        key: "needURLToCard",
                        value: function () {
                            try {
                                window._n("c95")
                            } catch (e) { }
                            return s.url2CardDomains && s.url2CardDomains.length > 0
                        }
                    }, {
                        key: "urlIsInTranList",
                        value: function (e) {
                            try {
                                window._n("9RQ")
                            } catch (t) { }
                            for (var n = s.url2CardDomains || [], o = 0; o < n.length; o++)
                                if (e.indexOf(n[o]) >= 0)
                                    return !0;
                            return !1
                        }
                    }, {
                        key: "isShowTipCard",
                        value: function (e) {
                            try {
                                window._n("1gl")
                            } catch (t) { }
                            var n = e.serviceType;
                            return u[n] && "Y" == u[n].show_prompt_message
                        }
                    }, {
                        key: "getTipCardMessage",
                        value: function (e) {
                            try {
                                window._n("UVc")
                            } catch (t) { }
                            var n = e.serviceType;
                            return u[n] && u[n].prompt_message_content || ""
                        }
                    }, {
                        key: "isShowTransation",
                        value: function (e, t) {
                            try {
                                window._n("nHQ")
                            } catch (n) { }
                            var o = e.serviceType
                                , r = u[o] && "Y" == u[o].translate_message
                                , a = this.translateActionCode() || i.Z.NeedTranslateCode
                                , c = !!(0 == t.messageType || a.indexOf(t.actionCode) >= 0);
                            return r && c
                        }
                    }, {
                        key: "isManualTransation",
                        value: function (e) {
                            try {
                                window._n("yIr")
                            } catch (t) { }
                            var n = this.translateActionCode() || i.Z.NeedTranslateCode;
                            return !!(0 == e.messageType || n.indexOf(e.actionCode) >= 0)
                        }
                    }, {
                        key: "isCanViewWorkTime",
                        value: function () {
                            try {
                                window._n("9uv")
                            } catch (e) { }
                            return d.view_work_time && "Y" == d.view_work_time || !d.view_work_time
                        }
                    }, {
                        key: "isCanEditAutoReplay",
                        value: function () {
                            try {
                                window._n("fDO")
                            } catch (e) { }
                            return d.update_auto_reply && "Y" == d.update_auto_reply || !d.update_auto_reply && c
                        }
                    }, {
                        key: "isCanEditWorkTime",
                        value: function () {
                            try {
                                window._n("F6O")
                            } catch (e) { }
                            return d.update_work_time && "Y" == d.update_work_time
                        }
                    }, {
                        key: "isCanEditAvator",
                        value: function () {
                            try {
                                window._n("syF")
                            } catch (e) { }
                            return d.update_agent_avatar && "Y" == d.update_agent_avatar || !d.update_agent_avatar && c
                        }
                    }, {
                        key: "isCanEditNickName",
                        value: function () {
                            try {
                                window._n("iJE")
                            } catch (e) { }
                            return d.update_agent_nickname && "Y" == d.update_agent_nickname || !d.update_agent_nickname && c
                        }
                    }, {
                        key: "isCanAutoTranslate",
                        value: function () {
                            try {
                                window._n("hv5")
                            } catch (e) { }
                            return d.automatic_translation && "Y" == d.automatic_translation || !d.automatic_translation && c
                        }
                    }, {
                        key: "translateActionCode",
                        value: function () {
                            try {
                                window._n("z0L")
                            } catch (e) { }
                            return ((window.__app_config__ || {}).qconfig || {}).translateActionCode
                        }
                    }, {
                        key: "isSettingEmail",
                        value: function () {
                            try {
                                window._n("VCz")
                            } catch (e) { }
                            return d.send_silence_email_notice_entry && "Y" == d.send_silence_email_notice_entry || !d.send_silence_email_notice_entry && c
                        }
                    }, {
                        key: "isCanEmailNotice",
                        value: function () {
                            try {
                                window._n("CkL")
                            } catch (e) { }
                            return d.send_silence_email_notice && "Y" == d.send_silence_email_notice || !d.send_silence_email_notice && c
                        }
                    }, {
                        key: "isShowWaitlist",
                        value: function () {
                            try {
                                window._n("ABi")
                            } catch (e) { }
                            return s.hasTodoList
                        }
                    }, {
                        key: "isIQMode",
                        value: function () {
                            try {
                                window._n("KH0")
                            } catch (e) { }
                            return "A" == s.abTestResult
                        }
                    }, {
                        key: "isCanYouYou",
                        value: function (e) {
                            try {
                                window._n("lCp")
                            } catch (t) { }
                            var n = e.serviceType;
                            return !u[n] || !u[n].support_youyou || u[n] && "Y" == u[n].support_youyou
                        }
                    }, {
                        key: "isCanShopping",
                        value: function (e) {
                            try {
                                window._n("Sw9")
                            } catch (t) { }
                            var n = e.serviceType;
                            return !!u[n] && !!u[n].xing_qiu_hao && u[n] && "Y" == u[n].xing_qiu_hao
                        }
                    }, {
                        key: "isCanUpdateGroupname",
                        value: function (e) {
                            try {
                                window._n("ZzD")
                            } catch (t) { }
                            var n = e.serviceType;
                            return !!u[n] && !!u[n].grouptitle_modifiable && u[n] && "Y" == u[n].grouptitle_modifiable
                        }
                    }, {
                        key: "isCanUpdateWelcome",
                        value: function (e) {
                            try {
                                window._n("TGC")
                            } catch (t) { }
                            var n = e.serviceType;
                            return !!u[n] && !!u[n].groupWelcome_modifiable && u[n] && "Y" == u[n].groupWelcome_modifiable
                        }
                    }, {
                        key: "isCanXQHContent",
                        value: function (e) {
                            try {
                                window._n("hJw")
                            } catch (t) { }
                            var n = e.serviceType;
                            return !!u[n] && !!u[n].xing_qiu_hao_content && u[n] && "Y" == u[n].xing_qiu_hao_content
                        }
                    }, {
                        key: "isCanQuickreply",
                        value: function (e) {
                            try {
                                window._n("C4o")
                            } catch (t) { }
                            var n = e.serviceType;
                            return !u[n] || !u[n].support_quick_reply || u[n] && "Y" == u[n].support_quick_reply
                        }
                    }, {
                        key: "isCanScore",
                        value: function (e) {
                            try {
                                window._n("wDc")
                            } catch (t) { }
                            var n = e.serviceType;
                            return u[n] && "Y" == u[n].support_score
                        }
                    }, {
                        key: "isCanAiTip",
                        value: function (e) {
                            try {
                                window._n("APK")
                            } catch (t) { }
                            var n = e.serviceType;
                            return u[n] && "Y" == u[n].relative_questions_switch
                        }
                    }, {
                        key: "aiTipResCount",
                        value: function (e) {
                            try {
                                window._n("Up4")
                            } catch (t) { }
                            var n = e.serviceType;
                            return u[n] && u[n].relative_questions_size || 20
                        }
                    }, {
                        key: "inviteAllowed",
                        value: function (e) {
                            try {
                                window._n("yZ8")
                            } catch (t) { }
                            var n = e.serviceType
                                , o = (u[n] && u[n].ebk_customer_allowed_to_invite || "0|1").split("|");
                            return {
                                OP: "1" == o[0],
                                AGENT: "1" == o[1]
                            }
                        }
                    }, {
                        key: "getConvTitleType",
                        value: function (e) {
                            try {
                                window._n("iLq")
                            } catch (t) { }
                            var n = e.serviceType
                                , o = e.currentConversationRole || 1;
                            return "1" == (u[n] && u[n][1 == o ? "contractor_msglist_sessionshow" : "server_msglist_sessionshow"] || "1") ? "user" : "group"
                        }
                    }, {
                        key: "isEndShowCommit",
                        value: function (e) {
                            try {
                                window._n("63s")
                            } catch (t) { }
                            var n = e.serviceType;
                            return !!u[n] && !!u[n].over_support_score && u[n] && "Y" == u[n].over_support_score
                        }
                    }, {
                        key: "roleDisplayName",
                        value: function (e) {
                            try {
                                window._n("I0b")
                            } catch (t) { }
                            var n = e.serviceType;
                            if (!u[n] || !u[n].role_display_name)
                                return {};
                            var o = {};
                            try {
                                o = u[n] && u[n].role_display_name && JSON.parse(u[n].role_display_name)
                            } catch (r) { }
                            return o
                        }
                    }, {
                        key: "isQuestion",
                        value: function (e) {
                            try {
                                window._n("dPb")
                            } catch (t) { }
                            var n = e.serviceType;
                            return !!u[n] && !!u[n].questionnaire && u[n] && "Y" == u[n].questionnaire
                        }
                    }, {
                        key: "isReport",
                        value: function (e) {
                            try {
                                window._n("sc4")
                            } catch (t) { }
                            var n = e.serviceType;
                            return !!u[n] && !!u[n].report_harassment && u[n] && "Y" == u[n].report_harassment
                        }
                    }, {
                        key: "isUseNewEmoji",
                        value: function () {
                            try {
                                window._n("Ql8")
                            } catch (e) { }
                            return d.emojiFlag && "Y" == d.emojiFlag
                        }
                    }, {
                        key: "isSendVidio",
                        value: function (e) {
                            try {
                                window._n("pKG")
                            } catch (t) { }
                            var n = e.serviceType;
                            return !!u[n] && !!u[n].ebk_send_video_check && u[n] && "Y" == u[n].ebk_send_video_check
                        }
                    }, {
                        key: "isHideOrderId",
                        value: function (e) {
                            try {
                                window._n("rCi")
                            } catch (t) { }
                            var n = e.serviceType;
                            return !!u[n] && !!u[n].hide_orderid && u[n] && "Y" == u[n].hide_orderid
                        }
                    }, {
                        key: "getAllowB2mc",
                        value: function (e) {
                            try {
                                window._n("JWu")
                            } catch (t) { }
                            var n = e.serviceType
                                , o = [];
                            return u[n] && u[n].allow_b2mc_biztypes_config && u[n].allow_b2mc_biztypes_config.split(";").forEach(function (e) {
                                try {
                                    window._n("PtF")
                                } catch (t) { }
                                var n = e.split("|");
                                o.push({
                                    key: n[0],
                                    name: n[1]
                                })
                            }),
                                o
                        }
                    }, {
                        key: "isIqNotice",
                        value: function () {
                            try {
                                window._n("u6E")
                            } catch (e) { }
                            var t = (0,
                                a.m)(location.search);
                            return "B" == s.abTestResult && t.accountsource && "vbk" == t.accountsource.toLowerCase()
                        }
                    }, {
                        key: "getActionCodeBlackList",
                        value: function () {
                            var e;
                            try {
                                window._n("MY1")
                            } catch (t) { }
                            return e = ((window.__app_config__ || {}).qconfig || {}).actionCodeBlackList,
                                "[object Array]" == Object.prototype.toString.call(e) ? e : i.Z.CommonEnum.BlackActionCodes
                        }
                    }, {
                        key: "compensateGroupConversationList",
                        value: function () {
                            try {
                                window._n("QLi")
                            } catch (e) { }
                            return ((window.__app_config__ || {}).qconfig || {}).compensateGroupConversationList
                        }
                    }, {
                        key: "isInNewMessageBlackList",
                        value: function (e) {
                            try {
                                window._n("Pos")
                            } catch (t) { }
                            return (((window.__app_config__ || {}).qconfig || {}).newMessageBlackList || []).indexOf(e + "") >= 0
                        }
                    }]),
                    e
            }()
    },
    8797: function (e, t, n) {
        var o = n(47813)
            , r = n(11025)
            , i = n(74213)
            , a = n(73383)
            , c = n(32336)
            , s = n(61023)
            , u = n(80830)
            , d = n(89485)
            , l = n(85943)
            , m = n(2037)
            , y = n(7315)
            , h = n(47015)
            , _ = n(57770);
        n(89539);
        var f = n(48978)
            , g = n(46246);
        function p(e, t) {
            var n = Object.keys(e);
            if (Object.getOwnPropertySymbols) {
                var o = Object.getOwnPropertySymbols(e);
                t && (o = o.filter(function (t) {
                    return Object.getOwnPropertyDescriptor(e, t).enumerable
                })),
                    n.push.apply(n, o)
            }
            return n
        }
        function w(e) {
            for (var t = 1; t < arguments.length; t++) {
                var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? p(Object(n), !0).forEach(function (t) {
                    (0,
                        o.Z)(e, t, n[t])
                }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : p(Object(n)).forEach(function (t) {
                    Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                })
            }
            return e
        }
        n(13485);
        var v = null
            , C = null
            , Z = function () {
                function e() {
                    (0,
                        r.Z)(this, e)
                }
                return (0,
                    i.Z)(e, [{
                        key: "mergeAndDistinctConversationList",
                        value: function () {
                            var e, t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [], n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : [];
                            try {
                                window._n("B5p")
                            } catch (o) { }
                            for (var r = {}, i = 0; i < t.length; i++)
                                r[t[i].gid] = t[i];
                            for (var a = 0; a < n.length; a++)
                                r[e = n[a].gid] ? r[e] = this.mergeConv(r[e], n[a]) : r[e] = n[a];
                            var c = []
                                , s = 0;
                            return Object.keys(r).map(function (e) {
                                try {
                                    window._n("vBd")
                                } catch (t) { }
                                s += r[e].unReadMessageCount || 0,
                                    c.push(r[e])
                            }),
                            {
                                totalUnreadCount: s,
                                convList: c = this.sortConversationList(c)
                            }
                        }
                    }, {
                        key: "mergeGroupConv",
                        value: function (e) {
                            var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : [];
                            try {
                                window._n("PLs")
                            } catch (n) { }
                            var o = this.groupConversationList(t);
                            return o.BC && o.BC.convList && o.BC.convList.length > 0 && (e.BC = this.mergeAndDistinctConversationList(e.BC.convList, o.BC.convList)),
                                o.BOP && o.BOP.convList && o.BOP.convList.length > 0 && (e.BOP = this.mergeAndDistinctConversationList(e.BOP.convList, o.BOP.convList)),
                                e.totalUnreadCount = (e.BC.totalUnreadCount || 0) + (e.BOP.totalUnreadCount || 0) + (e.xingqiuhao.totalUnreadCount || 0),
                                e
                        }
                    }, {
                        key: "mergeConv",
                        value: function (e, t) {
                            try {
                                window._n("a04")
                            } catch (n) { }
                            var o = (e || {}).ext || {}
                                , r = t.ext || {}
                                , i = (e || {}).memberHash || {}
                                , a = t.memberHash || {};
                            for (var c in a)
                                !a[c].roles && i && i[c] && i[c].roles && (a[c].roles = i[c].roles);
                            var s = t.ctripAgentId !== (e || {}).ctripAgentId;
                            return w(w(w({}, e), t), {}, {
                                ext: w(w({}, o), r),
                                memberHash: w(w({}, i), a),
                                isCtripAgentChange: s,
                                sessionId: t.sessionId || (e || {}).sessionId || ""
                            })
                        }
                    }, {
                        key: "updateConvObjMessageInfo",
                        value: function (e, t, n, o) {
                            var r, i, a, c, u = this;
                            try {
                                window._n("vqZ")
                            } catch (d) { }
                            var y = e.BC.convList || []
                                , h = e.BOP.convList || []
                                , _ = e[s.Z.CommonEnum.ConversationType.SingleChat].convList || []
                                , f = e[s.Z.CommonEnum.ConversationType.Notice].convList || []
                                , g = e[s.Z.CommonEnum.ConversationType.XingqiuChat].convList || []
                                , p = !1
                                , w = [];
                            return t.map(function (t) {
                                try {
                                    window._n("MMy")
                                } catch (d) { }
                                if (t.isIqNotice)
                                    f.forEach(function (n) {
                                        try {
                                            window._n("pPw")
                                        } catch (r) { }
                                        n.categorySubList.forEach(function (r) {
                                            try {
                                                window._n("o3r")
                                            } catch (i) { }
                                            if (t.categoryId == n.categoryId && t.categorySubId == r.categoryId) {
                                                if ("1021" == t.messageType || "1022" == t.messageType) {
                                                    var a = r.unreadCount || 0;
                                                    n.unreadCount = Math.max(0, n.unreadCount - a),
                                                        e[s.Z.CommonEnum.ConversationType.Notice].totalUnreadCount = Math.max(0, e[s.Z.CommonEnum.ConversationType.Notice].totalUnreadCount - a),
                                                        r.unreadCount = 0;
                                                    return
                                                }
                                                r.lastMsg = t.title || "",
                                                    r.lastMsgTime = r.lastMessageTime = t.createTime,
                                                    (o || {}).gid != n.categoryId + "_" + r.categoryId && (r.unreadCount++,
                                                        n.unreadCount++,
                                                        e[s.Z.CommonEnum.ConversationType.Notice].totalUnreadCount++)
                                            }
                                        }),
                                            n.categorySubList && n.categorySubList.length > 0 && (n.categorySubList = u.sortNoticeList(n.categorySubList))
                                    }),
                                        e[s.Z.CommonEnum.ConversationType.Notice].convList = f;
                                else if (t.conversationType === s.Z.CommonEnum.ConversationType.SingleChat) {
                                    var v = []
                                        , C = "";
                                    t.isXingqiu ? (v = g,
                                        C = s.Z.CommonEnum.ConversationType.XingqiuChat) : (v = l.Z.isSysNoticeMessage(t) ? f : _,
                                            C = l.Z.isSysNoticeMessage(t) ? s.Z.CommonEnum.ConversationType.Notice : s.Z.CommonEnum.ConversationType.SingleChat);
                                    var Z = !1;
                                    if (f.map(function (i) {
                                        try {
                                            window._n("Qeg")
                                        } catch (a) { }
                                        var c = i.unReadMessageCount;
                                        i.gid == t.fromGid && (Z = !0,
                                            r = u.updateConvMessageInfo(i, t, n, o) || r),
                                            e[s.Z.CommonEnum.ConversationType.Notice].totalUnreadCount += i.unReadMessageCount - c
                                    }),
                                        _.map(function (i) {
                                            try {
                                                window._n("ySi")
                                            } catch (a) { }
                                            var c = i.unReadMessageCount;
                                            i.gid == t.fromGid && (Z = !0,
                                                r = u.updateConvMessageInfo(i, t, n, o) || r),
                                                e[s.Z.CommonEnum.ConversationType.SingleChat].totalUnreadCount += i.unReadMessageCount - c
                                        }),
                                        !Z && t.fromUid.toLowerCase() != n.uid.toLowerCase()) {
                                        t.unreadCount = 1,
                                            e[C].totalUnreadCount += 1,
                                            ("1021" != t.messageType || "1022" != t.messageType || "1023" != t.messageType) && v.push(u.singleMessageToConv(t, !0));
                                        var T = l.Z.isSysNoticeMessage(t) ? s.Z.EventEnum.NEDD_UPDATE_NOTICE_LIST : s.Z.EventEnum.NEED_UPDATE_SINGLE_LIST;
                                        setTimeout(function () {
                                            try {
                                                window._n("J7j")
                                            } catch (e) { }
                                            m.Z.publish(T, [])
                                        }, 3e3)
                                    }
                                } else if (t.conversationType === s.Z.CommonEnum.ConversationType.GroupChat) {
                                    var E = !t.isXingqiu && !!t.fromGid;
                                    t.isXingqiu && (E = !1,
                                        g.map(function (r) {
                                            try {
                                                window._n("QUo")
                                            } catch (i) { }
                                            var a = r.unReadMessageCount;
                                            r && r.gid === t.fromGid && (c = u.updateConvMessageInfo(r, t, n, o) || c),
                                                e[s.Z.CommonEnum.ConversationType.XingqiuChat].totalUnreadCount += r.unReadMessageCount - a,
                                                e.totalUnreadCount += r.unReadMessageCount - a
                                        }),
                                        E || w.push(t)),
                                        y.map(function (r) {
                                            try {
                                                window._n("Ld5")
                                            } catch (a) { }
                                            if (!(s.Z.CommonEnum.ConvStatus.ACTIVE.values.indexOf(r.status) < 0)) {
                                                var c = r.unReadMessageCount;
                                                r && r.gid === t.fromGid && (E = !1,
                                                    i = u.updateConvMessageInfo(r, t, n, o) || i),
                                                    e.BC.totalUnreadCount += r.unReadMessageCount - c,
                                                    e.totalUnreadCount += r.unReadMessageCount - c
                                            }
                                        }),
                                        h.map(function (r) {
                                            try {
                                                window._n("t2M")
                                            } catch (i) { }
                                            if (!(s.Z.CommonEnum.ConvStatus.ACTIVE.values.indexOf(r.status) < 0)) {
                                                var c = r.unReadMessageCount;
                                                r && r.gid === t.fromGid && (E = !1,
                                                    a = u.updateConvMessageInfo(r, t, n, o) || a),
                                                    e.BOP.totalUnreadCount += r.unReadMessageCount - c,
                                                    e.totalUnreadCount += r.unReadMessageCount - c
                                            }
                                        }),
                                        E && (p = !0)
                                }
                            }),
                                a && (e.BOP.convList = this.sortConversationList(h)),
                                i && (e.BC.convList = this.sortConversationList(y)),
                                c && (e[s.Z.CommonEnum.ConversationType.XingqiuChat].convList = this.sortConversationList(g)),
                                r && (e[s.Z.CommonEnum.ConversationType.SingleChat].convList = this.sortConversationList(_),
                                    e[s.Z.CommonEnum.ConversationType.Notice].convList = this.sortConversationList(f)),
                                e.hasUnexpectedGroupMessage = p,
                                e
                        }
                    }, {
                        key: "getNoSheetConversation",
                        value: function (e) {
                            var t = this;
                            try {
                                window._n("f0N")
                            } catch (n) { }
                            return new Promise(function (n, o) {
                                try {
                                    window._n("pGF")
                                } catch (r) { }
                                a.Z.getNoSheetConversation.post({
                                    partnerJid: e.fromGid,
                                    chatType: e.conversationType,
                                    bizType: e.bizType
                                }).then(function (e) {
                                    try {
                                        window._n("Q14")
                                    } catch (o) { }
                                    var r = e.nosheetConversation || {};
                                    n(t.xingqiuMessageToConv(r, !1, s.Z.CommonEnum.ConversationType.XingqiuChat))
                                }).catch(function (e) {
                                    try {
                                        window._n("UsB")
                                    } catch (t) { }
                                    o(e)
                                })
                            }
                            )
                        }
                    }, {
                        key: "updateConvMessageInfo",
                        value: function (e, t, n, o) {
                            try {
                                window._n("L6N")
                            } catch (r) { }
                            var i = !0
                                , a = !1;
                            if (e && e.gid === t.fromGid) {
                                if (o && o.gid == e.gid && (e.unReadMessageCount = 0,
                                    i = !1),
                                    t.messageCategory == CommonEnum.MessageType.SystemMessage) {
                                    if (i = !1,
                                        "1023" == t.messageType)
                                        return;
                                    if ("1021" == t.messageType || "1022" == t.messageType) {
                                        e.unReadMessageCount = 0;
                                        return
                                    }
                                }
                                if (t.threadId !== e.threadId && (e.threadId = t.threadId),
                                    1007 == t.messageType || 7 == t.messageType) {
                                    if (t.extPropertys && "number" == typeof t.extPropertys.visibleRule) {
                                        if ((4 & t.extPropertys.visibleRule) == 0)
                                            return
                                    } else if (!1 === t.isPresent)
                                        return;
                                    if (1 == t.bSee && o.sessionId != t.sessionId)
                                        return !1
                                }
                                i && t.fromUid.toLowerCase() !== n.ctripUid.toLowerCase() && e.unReadMessageCount++,
                                    e.lastMessage = t,
                                    e.lastMessageTime = t.createTime,
                                    a = !0
                            }
                            return a
                        }
                    }, {
                        key: "groupConversationList",
                        value: function (e) {
                            try {
                                window._n("IJk")
                            } catch (t) { }
                            var n = {
                                totalConv: 0,
                                totalUnreadCount: 0
                            };
                            return (e || []).map(function (e) {
                                try {
                                    window._n("F4o")
                                } catch (t) { }
                                n.totalConv++,
                                    n.totalUnreadCount += e.unReadMessageCount || 0,
                                    Object.keys(s.Z.CommonEnum.ConvTabs).forEach(function (t) {
                                        try {
                                            window._n("uqY")
                                        } catch (o) { }
                                        n.hasOwnProperty(t) || (n[t] = {
                                            totalUnreadCount: 0,
                                            convList: []
                                        }),
                                            s.Z.CommonEnum.ConvTabs[t].convTypes.indexOf(e.conversationType) >= 0 && (n[t].convList = n[t].convList || [],
                                                e.tabType = t,
                                                n[t].convList.push(e))
                                    })
                            }),
                                n
                        }
                    }, {
                        key: "sortConversationList",
                        value: function (e) {
                            try {
                                window._n("bRa")
                            } catch (t) { }
                            return e[0] && ("xqhprivatechat" == e[0].convtype || "xqhfans" == e[0].convtype) && e.forEach(function (e) {
                                try {
                                    window._n("OBV")
                                } catch (t) { }
                                e.lastMessage && !e.lastMessage.createTime && (e.lastMessage.createTime = 0)
                            }),
                                (e || []).sort(function (e, t) {
                                    try {
                                        window._n("WvG")
                                    } catch (n) { }
                                    return 4 == e.status && 4 != t.status ? 1 : 4 == e.status && 4 == t.status ? e.lastMessage && t.lastMessage && e.lastMessage.createTime < t.lastMessage.createTime || !e.lastMessage && t.lastMessage ? 1 : -1 : (e.lastMessage ? e.lastMessage.createTime : e.lastUpdateTime) < (t.lastMessage ? t.lastMessage.createTime : t.lastUpdateTime) ? 1 : -1
                                })
                        }
                    }, {
                        key: "sortGroupChatList",
                        value: function (e) {
                            try {
                                window._n("2vX")
                            } catch (t) { }
                            return (e || []).sort(function (e, t) {
                                try {
                                    window._n("XVm")
                                } catch (n) { }
                                return e.lastMessage && t.lastMessage && e.lastMessage.createTime < t.lastMessage.createTime || !e.lastMessage && t.lastMessage ? 1 : -1
                            })
                        }
                    }, {
                        key: "mapConversationList",
                        value: function (e, t) {
                            var n = this;
                            try {
                                window._n("WoV")
                            } catch (o) { }
                            for (var r = [], i = 0; i < e.length; i++)
                                if (e[i].chatType = s.Z.CommonEnum.ConversationType.GroupChat,
                                    e[i].memberHash = e[i].memberHash || {},
                                    2 == e[i].status)
                                    r.push(e[i].gid);
                                else if (e[i].lastMessageJsonString && 4 == e[i].status)
                                    try {
                                        e[i].lastMessage = u.Z.serializeMessage(JSON.parse(e[i].lastMessageJsonString))
                                    } catch (t) { }
                            var a = this.getGroupDetailInfo(r)
                                , c = Promise.resolve({});
                            return t.onlyUnreadCount || (c = this.wrapPromise(d.Z.getChatTargetDetailInfoByConvList(e))),
                                Promise.all([a, c]).then(function (o) {
                                    try {
                                        window._n("gNy")
                                    } catch (r) { }
                                    for (var i = o[0] || {}, a = o[1] || {}, c = 0; c < e.length; c++) {
                                        var u = e[c].gid;
                                        i[u] && (e[c].unReadMessageCount = i[u].unreadCount);
                                        var d = a[u];
                                        d && (e[c].chatTarget = d.uid,
                                            e[c].memberHash[d.uid] = w(w({}, e[c].memberHash[d.uid] || {}), d))
                                    }
                                    var l = [];
                                    return (e || []).map(function (e) {
                                        try {
                                            window._n("Kk9")
                                        } catch (o) { }
                                        (s.Z.CommonEnum.ConvTabs.BC.convTypes.indexOf(e.conversationType) >= 0 || s.Z.CommonEnum.ConvTabs.BOP.convTypes.indexOf(e.conversationType) >= 0) && l.push(n.mapConversation(e, t))
                                    }),
                                        Promise.all(l)
                                })
                        }
                    }, {
                        key: "getGroupDetailInfo",
                        value: function () {
                            var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [];
                            try {
                                window._n("I8i")
                            } catch (t) { }
                            return 0 == e.length ? Promise.resolve({}) : this.wrapPromise(a.Z.getConversationInfoByGidList.post({
                                gidList: e
                            }).then(function (e) {
                                try {
                                    window._n("TpN")
                                } catch (t) { }
                                var n = {};
                                return (e.conversationInfoList || []).map(function (e) {
                                    try {
                                        window._n("h7R")
                                    } catch (t) { }
                                    n[e.gid] = {
                                        unreadCount: e.unreadCount,
                                        message: u.Z.serializeMessage(e.message)
                                    }
                                }),
                                    n
                            }))
                        }
                    }, {
                        key: "wrapPromise",
                        value: function (e) {
                            try {
                                window._n("62N")
                            } catch (t) { }
                            return new Promise(function (t, n) {
                                try {
                                    window._n("QCi")
                                } catch (o) { }
                                e.then(function (e) {
                                    try {
                                        window._n("a14")
                                    } catch (n) { }
                                    t(e)
                                }).catch(function (e) {
                                    try {
                                        window._n("8Yu")
                                    } catch (n) { }
                                    t(void 0)
                                })
                            }
                            )
                        }
                    }, {
                        key: "getLastMessagePromise",
                        value: function (e) {
                            var t = this;
                            try {
                                window._n("tkC")
                            } catch (n) { }
                            var o = null;
                            return this.wrapPromise(a.Z.userInfoModel.getCurrentUserInfo().then(function (n) {
                                try {
                                    window._n("mRt")
                                } catch (r) { }
                                var i = {
                                    showTargetIsRead: !0,
                                    groupId: e.gid,
                                    conversationType: e.conversationType,
                                    pageSize: 50
                                };
                                return t.getLastRangeMessage(i, n).then(function (e) {
                                    try {
                                        window._n("8v3")
                                    } catch (t) { }
                                    var n = e.messages;
                                    for (o = n.pop() || null; o && [1022, 1023, 1021].indexOf(o.messageType) >= 0 || 1007 == o.messageType & !1 === o.isPresent;)
                                        o = n.pop();
                                    return o
                                })
                            }))
                        }
                    }, {
                        key: "getUnreadInfo",
                        value: function (e) {
                            try {
                                window._n("r0N")
                            } catch (t) { }
                            return new Promise(function (t, n) {
                                try {
                                    window._n("HUU")
                                } catch (o) { }
                                var r = !1
                                    , i = null;
                                IMClient.getConversationUnreadInfo({
                                    partnerJid: e.gid,
                                    messageBaseLine: 0,
                                    conversationType: "groupchat"
                                }, function (e) {
                                    try {
                                        window._n("qWd")
                                    } catch (n) { }
                                    r = !0,
                                        clearTimeout(i),
                                        t(e)
                                }, function (e) {
                                    try {
                                        window._n("7Fv")
                                    } catch (t) { }
                                    r = !0,
                                        clearTimeout(i),
                                        n(e)
                                }),
                                    i = setTimeout(function () {
                                        try {
                                            window._n("Afb")
                                        } catch (e) { }
                                        r || n()
                                    }, 5e3)
                            }
                            )
                        }
                    }, {
                        key: "mapConversation",
                        value: function (e, t) {
                            try {
                                window._n("coM")
                            } catch (n) { }
                            if (e.chatTarget && e.lastMessage && void 0 !== e.unReadMessageCount)
                                return Promise.resolve(e);
                            var o = Promise.resolve({})
                                , r = Promise.resolve(0)
                                , i = Promise.resolve({});
                            return e.chatTarget || t.onlyUnreadCount || (o = this.wrapPromise(d.Z.getAllGroupMembers(e.gid))),
                                e.lastMessage || t.onlyUnreadCount || t.onlyMember || (i = this.getLastMessagePromise(e)),
                                void 0 !== e.unReadMessageCount || e.status != s.Z.CommonEnum.ConvStatus.ACTIVE.value || t.onlyMember || (r = this.wrapPromise(this.getUnreadInfo(e))),
                                new Promise(function (n, a) {
                                    try {
                                        window._n("bDD")
                                    } catch (c) { }
                                    Promise.all([o, i, r]).then(function (o) {
                                        try {
                                            window._n("Det")
                                        } catch (r) { }
                                        var i = o[0] || {};
                                        e.allMembers = i.members || [];
                                        var a = i.memberHash || {}
                                            , c = e.chatTarget || "";
                                        c || (c = d.Z.getChatTargetUid(e) || (i.robot || {}).uid),
                                            e.memberHash = w(w({}, e.memberHash || {}), a),
                                            e.chatTarget = c,
                                            e.lastMessage = e.lastMessage || o[1],
                                            e.unReadMessageCount = e.unReadMessageCount || (o[2] || {}).unreadCount || 0,
                                            e.chatType = s.Z.CommonEnum.ConversationType.GroupChat,
                                            c || t.onlyUnreadCount ? n(e) : d.Z.getChatTargetDetailInfo(e).then(function (t) {
                                                try {
                                                    window._n("Qy8")
                                                } catch (o) { }
                                                t.uid && (e.chatTarget = t.uid,
                                                    e.memberHash[t.uid] = t),
                                                    n(e)
                                            }).catch(function (t) {
                                                try {
                                                    window._n("1aP")
                                                } catch (o) { }
                                                n(e)
                                            })
                                    }).catch(function (t) {
                                        try {
                                            window._n("dgS")
                                        } catch (o) { }
                                        n(e)
                                    })
                                }
                                )
                        }
                    }, {
                        key: "batchTranslate",
                        value: function (e, t) {
                            try {
                                window._n("HOX")
                            } catch (n) { }
                            return new Promise(function (n, o) {
                                try {
                                    window._n("qGh")
                                } catch (r) { }
                                for (var i = e.messageIdList, c = []; i.length > 0;) {
                                    var d = i.splice(0, 30)
                                        , l = w(w({}, e), {}, {
                                            messageIdList: d
                                        });
                                    c.push(a.Z.batchTranslate.post(l))
                                }
                                Promise.all(c).then(function (o) {
                                    try {
                                        window._n("Ee7")
                                    } catch (r) { }
                                    var i = []
                                        , a = {}
                                        , c = "";
                                    o.map(function (e) {
                                        try {
                                            window._n("iQN")
                                        } catch (t) { }
                                        i = [].concat(i, e.translateMessageList || []),
                                            a = w(w({}, a), e.idWithTranslatedMap || {}),
                                            c = e.supplier
                                    });
                                    var d = u.Z.jsonSerialize(i, t.uid);
                                    n({
                                        messages: d = u.Z.filterMessage(d, t),
                                        supplier: c,
                                        idWithTranslatedMap: a
                                    }),
                                        (0,
                                            y.Z)(s.Z.UBTTypeEnum.TRANSLATE_RESLUT, {
                                                sessionId: e.sessionId,
                                                messageId: e.messageIdList,
                                                source: c,
                                                locale: e.target,
                                                issuccessful: !!d && !!d[0]
                                            })
                                }).catch(function (t) {
                                    try {
                                        window._n("Bz2")
                                    } catch (n) { }
                                    o(t),
                                        (0,
                                            y.Z)(s.Z.UBTTypeEnum.TRANSLATE_RESLUT, {
                                                sessionId: e.sessionId,
                                                messageId: e.messageIdList,
                                                locale: e.target,
                                                issuccessful: !1
                                            })
                                })
                            }
                            )
                        }
                    }, {
                        key: "getRangeMessage",
                        value: function (e, t) {
                            try {
                                window._n("H3V")
                            } catch (n) { }
                            var o = new c.Z;
                            return new Promise(function (n, r) {
                                try {
                                    window._n("igl")
                                } catch (i) { }
                                var c = new Date;
                                a.Z.messageListModelV2.cancel(),
                                    a.Z.messageListModelV2.post(e).then(function (e) {
                                        try {
                                            window._n("fvP")
                                        } catch (r) { }
                                        var i = new Date;
                                        o.send({
                                            type: s.Z.LogTypeEnum.query_message,
                                            status: "success",
                                            timecost: i.getTime() - c.getTime()
                                        });
                                        var a = e.rangeMessage
                                            , d = u.Z.jsonSerialize(a.messages, t.uid);
                                        d = u.Z.filterMessage(d, t),
                                            a.messages = d,
                                            n(a)
                                    }).catch(function (e) {
                                        try {
                                            window._n("oHK")
                                        } catch (t) { }
                                        var n = new Date;
                                        o.send({
                                            type: s.Z.LogTypeEnum.query_message,
                                            status: "fail",
                                            timecost: n.getTime() - c.getTime()
                                        }),
                                            (0,
                                                g.y)(e) && f.Z.logout(e),
                                            r(e)
                                    })
                            }
                            )
                        }
                    }, {
                        key: "getLastRangeMessage",
                        value: function (e, t) {
                            try {
                                window._n("n7X")
                            } catch (n) { }
                            var o = new c.Z;
                            return new Promise(function (n, r) {
                                try {
                                    window._n("AzV")
                                } catch (i) { }
                                var c = new Date;
                                a.Z.messageListModel.post(e).then(function (e) {
                                    try {
                                        window._n("opS")
                                    } catch (r) { }
                                    var i = new Date;
                                    o.send({
                                        type: s.Z.LogTypeEnum.query_message,
                                        status: "success",
                                        timecost: i.getTime() - c.getTime()
                                    });
                                    var a = e.rangeMessage
                                        , d = u.Z.jsonSerialize(a.messages, t.uid);
                                    d = u.Z.filterMessage(d, t),
                                        a.messages = d,
                                        n(a)
                                }).catch(function (e) {
                                    try {
                                        window._n("Isg")
                                    } catch (t) { }
                                    var n = new Date;
                                    o.send({
                                        type: s.Z.LogTypeEnum.query_message,
                                        status: "fail",
                                        timecost: n.getTime() - c.getTime()
                                    }),
                                        (0,
                                            g.y)(e) && f.Z.logout(e),
                                        r(e)
                                })
                            }
                            )
                        }
                    }, {
                        key: "getMessageList",
                        value: function (e, t, n, o) {
                            try {
                                window._n("3Au")
                            } catch (r) { }
                            if ("xqhprivatechat" != e.convtype && (e.chatType == s.Z.CommonEnum.ConversationType.SingleChat || e.chatType == s.Z.CommonEnum.ConversationType.Notice)) {
                                if (e.categoryId) {
                                    var i = {
                                        lastMsgTime: n || 0,
                                        pageSize: 100
                                    };
                                    return e.parentCategoryId ? (i.categoryId = e.parentCategoryId,
                                        i.categorySubId = e.categoryId) : (i.categoryId = e.categoryId,
                                            i.categorySubId = 0),
                                        this.getIQMessages(i, t).then(function (e) {
                                            try {
                                                window._n("nqw")
                                            } catch (t) { }
                                            var n = e.messages;
                                            return e.hasMoreHistory = n.length > 0,
                                                e
                                        })
                                }
                                var a = {
                                    targetUid: e.gid
                                };
                                return n && (a.beginTime = n),
                                    this.getSingleChatMessageList(a, t).then(function (e) {
                                        try {
                                            window._n("zZP")
                                        } catch (t) { }
                                        var n = e.messages;
                                        return e.hasMoreHistory = n.length > 0,
                                            e
                                    })
                            }
                            var c = {
                                showTargetIsRead: !0,
                                groupId: e.gid,
                                conversationType: e.conversationType,
                                pageSize: 50
                            };
                            return c.conversationType = e.convtype || e.conversationType,
                                "next" == o ? (c.beginTime = n,
                                    c.direction = o) : c.endTime = n,
                                this.getRangeMessage(c, t, !!n).then(function (e) {
                                    try {
                                        window._n("naP")
                                    } catch (t) { }
                                    return e.hasMoreHistory = !e.isDone,
                                        e
                                })
                        }
                    }, {
                        key: "getMessagesBySession",
                        value: function (e, t) {
                            try {
                                window._n("YgK")
                            } catch (n) { }
                            return a.Z.getMessagesBySession.post({
                                sessionId: e
                            }).then(function (e) {
                                try {
                                    window._n("kdt")
                                } catch (n) { }
                                var o = u.Z.jsonSerialize(e.messages || [], t.uid);
                                return o = u.Z.filterMessage(o, t),
                                    e.messages = o,
                                    e
                            })
                        }
                    }, {
                        key: "getMessageSender",
                        value: function (e, t) {
                            try {
                                window._n("a7j")
                            } catch (n) { }
                            if (e.conversationType == s.Z.CommonEnum.ConversationType.SingleChat) {
                                var o = t[s.Z.CommonEnum.ConversationType.SingleChat].convList
                                    , r = t[s.Z.CommonEnum.ConversationType.Notice].convList;
                                if (l.Z.isSysNoticeMessage(e)) {
                                    for (var i = 0; i < r.length; i++)
                                        if (e.fromGid == r[i].gid)
                                            return Promise.resolve(r[i].memberHash[e.fromUid] || null)
                                } else
                                    for (var a = 0; a < o.length; a++)
                                        if (e.fromGid == o[a].gid)
                                            return Promise.resolve(o[a].memberHash[e.fromUid] || null);
                                return Promise.resolve(null)
                            }
                            for (var c = e.fromGid, u = t.BC.convList.concat(t.BOP.convList), m = null, y = 0; y < u.length; y++)
                                e.fromGid == u[y].gid && (m = u[y]);
                            return d.Z.getAllGroupMembers(c).then(function (t) {
                                try {
                                    window._n("UBs")
                                } catch (n) { }
                                for (var o = t.members || [], r = null, i = 0; i < o.length; i++)
                                    o[i].username.toLowerCase() == e.fromUid.toLowerCase() && (r = o[i]);
                                return m && "group" == h.Z.Configs.getConvTitleType(m) && (r.remark = m.groupTitle,
                                    r.avatar = m.groupAvatar),
                                    r
                            })
                        }
                    }, {
                        key: "putAdviceOfReadByMsgId",
                        value: function (e) {
                            try {
                                window._n("4F6")
                            } catch (t) { }
                            e.messageCategory !== CommonEnum.MessageType.SystemMessage && IMClient.putAdviceOfReadByMsgId({
                                partnerJid: e.fromGid,
                                msgId: e.messageId || e.msgId,
                                conversationType: "chat" == (e.type || e.conversationType) ? "chat" : "groupchat"
                            }, function (e) { }, function (e) { })
                        }
                    }, {
                        key: "sendReadMessageWhenConvLoad",
                        value: function (e) {
                            try {
                                window._n("7xV")
                            } catch (t) { }
                            for (var n = e.length - 1; n >= 0;) {
                                var o = e[n--];
                                if (o.messageCategory !== CommonEnum.MessageType.SystemMessage) {
                                    this.putAdviceOfReadByMsgId(o);
                                    break
                                }
                            }
                        }
                    }, {
                        key: "getCustomAreaInfo",
                        value: function (e) {
                            try {
                                window._n("0J2")
                            } catch (t) { }
                            return new Promise(function (t, n) {
                                try {
                                    window._n("tbv")
                                } catch (o) { }
                                v && v.fetchAbort(),
                                    a.Z.customAreaModel.cancel(),
                                    e.serviceType && e.gid ? v = a.Z.customAreaModel.post({
                                        serviceType: "".concat(e.serviceType),
                                        groupId: e.gid
                                    }).then(function (e) {
                                        try {
                                            window._n("tFZ")
                                        } catch (n) { }
                                        t(e)
                                    }).catch(function (e) {
                                        try {
                                            window._n("H0G")
                                        } catch (n) { }
                                        t(null)
                                    }) : t(null)
                            }
                            )
                        }
                    }, {
                        key: "getCustomAreaConfig",
                        value: function (e) {
                            try {
                                window._n("bUA")
                            } catch (t) { }
                            return new Promise(function (t, n) {
                                try {
                                    window._n("3IL")
                                } catch (o) { }
                                C && C.fetchAbort(),
                                    a.Z.customAreaModel.cancel(),
                                    a.Z.customAreaConfig.cancel(),
                                    e.serviceType && e.gid ? C = a.Z.customAreaConfig.post({
                                        serviceType: "".concat(e.serviceType),
                                        groupId: e.gid
                                    }).then(function (e) {
                                        try {
                                            window._n("gTs")
                                        } catch (n) { }
                                        t(e)
                                    }).catch(function (e) {
                                        try {
                                            window._n("vVA")
                                        } catch (n) { }
                                        t(null)
                                    }) : t(null)
                            }
                            )
                        }
                    }, {
                        key: "createChat",
                        value: function (e) {
                            try {
                                window._n("uUO")
                            } catch (t) { }
                            var n, o, r, i = e.serviceType, u = e.vendorRefId, d = e.vendorRole, l = e.groupTitle, m = e.groupAvatar, y = e.threadId, h = e.orderNo, f = e.extParams, g = e.cuid, p = e.sessionId, v = e.preSessionId, C = e.groupId, Z = e.initChat, T = e.visitToken, E = e.lang, k = e.customerSource, S = e.terminalChannel, M = e.currentConversationRole, I = {
                                source: "h5",
                                pageCode: e.pageCode || "",
                                serviceType: i,
                                languageInfo: {}
                            };
                            if (g && (I.customerUid = g),
                                p && (I.sessionId = p),
                                v && (I.preSessionId = v),
                                C && (I.groupId = C),
                                u && (I.vendorRefId = u),
                                d && (I.role = d),
                                l && (I.groupTitle = l),
                                m && (I.groupAvatar = m),
                                y && (I.threadId = y),
                                h && (I.orderNo = h),
                                T && (I.token = T),
                                E && (I.languageInfo.lang = E),
                                k && (I.languageInfo.customerSource = k),
                                S && (I.languageInfo.terminalChannel = S),
                                f || h)
                                try {
                                    I.extParams = JSON.parse(f),
                                        h && (I.extParams.orderId = h)
                                } catch (N) {
                                    I.extParams = {},
                                        h && (I.extParams.orderId = h)
                                }
                            if (Z == s.Z.CommonEnum.InitChatType.B2C) {
                                var b = (0,
                                    _.m)(location.search);
                                b.extParams && JSON.parse(b.extParams).locale && (I.extParams = JSON.parse(b.extParams) || {},
                                    I.languageInfo.lang = JSON.parse(b.extParams).locale || null),
                                    r = 2,
                                    o = a.Z.createChartB2C,
                                    n = s.Z.CommonEnum.ConvType.B2C
                            } else if (Z == s.Z.CommonEnum.InitChatType.B2O)
                                r = 1,
                                    o = a.Z.createChatB2O,
                                    n = s.Z.CommonEnum.ConvType.B2O;
                            else if (Z == s.Z.CommonEnum.InitChatType.B2B)
                                r = 1,
                                    o = a.Z.createChatB2B,
                                    n = s.Z.CommonEnum.ConvType.B2B,
                                    M && 2 == M && (r = 2,
                                        o = a.Z.createChatB2BReverse);
                            else
                                throw "create conversation params error";
                            return new c.Z().send({
                                type: s.Z.LogTypeEnum.create_chat,
                                value: h ? 1 : 0,
                                value_desc: "withOrder",
                                jsonData: I
                            }),
                                o.post(I).then(function (e) {
                                    try {
                                        window._n("pQG")
                                    } catch (t) { }
                                    var o = e.workSheetId || e.sWorkSheetId;
                                    if (!o)
                                        throw "创建会话失败";
                                    return {
                                        conversationType: n,
                                        status: r,
                                        chatType: s.Z.CommonEnum.ConversationType.GroupChat,
                                        conversationKey: e.conversationKey,
                                        gid: e.groupId,
                                        serviceType: i,
                                        sessionId: e.sessionId,
                                        uid: e.customerUid || "",
                                        id: o,
                                        threadId: y,
                                        sidMod: e.sidMod,
                                        initMode: e.mode,
                                        ext: w(w({
                                            orderId: h
                                        }, I.extParams || {}), e.extParams || {})
                                    }
                                })
                        }
                    }, {
                        key: "getCurrentSessionMode",
                        value: function (e, t) {
                            try {
                                window._n("AYa")
                            } catch (n) { }
                            return a.Z.getCurrentSessionMode.post({
                                customerUid: t,
                                gid: e.gid,
                                sessionId: e.sessionId
                            })
                        }
                    }, {
                        key: "dropoutManual",
                        value: function (e) {
                            try {
                                window._n("Up1")
                            } catch (t) { }
                            return a.Z.dropoutManualModel.post({
                                groupId: e.gid,
                                sessionId: e.sessionId
                            })
                        }
                    }, {
                        key: "checkCommentStatus",
                        value: function (e) {
                            try {
                                window._n("Mdf")
                            } catch (t) { }
                            return new Promise(function (t, n) {
                                try {
                                    window._n("pFx")
                                } catch (o) { }
                                var r = e.gid
                                    , i = e.sessionId;
                                e.status,
                                    e.chatTarget,
                                    a.Z.checkScoreStatus.post({
                                        groupId: r,
                                        sessionId: i
                                    }).then(function (e) {
                                        try {
                                            window._n("fxI")
                                        } catch (o) { }
                                        var r = (e.scoreFlags || [])[0] || {};
                                        !1 == r.hasScored && !0 == r.needScored ? (e.type = r.type,
                                            t(e)) : n()
                                    }).catch(function (e) {
                                        try {
                                            window._n("2Tf")
                                        } catch (t) { }
                                        n(e)
                                    })
                            }
                            )
                        }
                    }, {
                        key: "checkCommentStatusBySession",
                        value: function (e) {
                            try {
                                window._n("x2E")
                            } catch (t) { }
                            return a.Z.checkScoreStatusBySession.post(e)
                        }
                    }, {
                        key: "commentConversation",
                        value: function (e) {
                            try {
                                window._n("Gsk")
                            } catch (t) { }
                            return a.Z.submitScore.post(w({}, e))
                        }
                    }, {
                        key: "setWorkSheetStar",
                        value: function (e, t) {
                            try {
                                window._n("9wT")
                            } catch (n) { }
                            return a.Z.setWorkSheetStar.post({
                                sessionId: e.sessionId,
                                groupId: e.gid,
                                worksheetId: e.id,
                                action: t
                            })
                        }
                    }, {
                        key: "getSingleChatList",
                        value: function () {
                            var e = this;
                            arguments.length > 0 && void 0 !== arguments[0] && arguments[0];
                            try {
                                window._n("XoT")
                            } catch (t) { }
                            return a.Z.getSingleChatList.post({}).then(function (t) {
                                try {
                                    window._n("yzD")
                                } catch (n) { }
                                var o = t.messages || []
                                    , r = 0;
                                return o = o.map(function (t) {
                                    try {
                                        window._n("Gyz")
                                    } catch (n) { }
                                    var o = e.singleMessageToConv(t, !1, s.Z.CommonEnum.ConversationType.SingleChat);
                                    return r += o.unReadMessageCount || 0,
                                        o
                                }),
                                    o = e.sortConversationList(o),
                                {
                                    totalUnreadCount: r,
                                    convList: o
                                }
                            })
                        }
                    }, {
                        key: "getSingleChatMessageList",
                        value: function (e, t) {
                            try {
                                window._n("n8N")
                            } catch (n) { }
                            return a.Z.getSingleChatMessageList.post(e).then(function (e) {
                                try {
                                    window._n("UFH")
                                } catch (n) { }
                                var o = e.messages || [];
                                return o = u.Z.jsonSerialize(o, t.uid),
                                {
                                    messages: o = u.Z.filterMessage(o, t),
                                    lastMessageTime: e.lastMessageTime
                                }
                            })
                        }
                    }, {
                        key: "getSystemNotifyList",
                        value: function () {
                            var e = this;
                            arguments.length > 0 && void 0 !== arguments[0] && arguments[0];
                            try {
                                window._n("Vnk")
                            } catch (t) { }
                            var n = (0,
                                _.m)(location.search)
                                , o = {};
                            return n.lang && (o.locale = n.lang),
                                a.Z.getSystemNotice.post(o).then(function (t) {
                                    try {
                                        window._n("LGR")
                                    } catch (n) { }
                                    var o = t.messages || []
                                        , r = 0;
                                    return o = o.map(function (t) {
                                        try {
                                            window._n("yax")
                                        } catch (n) { }
                                        var o = e.singleMessageToConv(t, !1, s.Z.CommonEnum.ConversationType.Notice);
                                        return r += o.unReadMessageCount || 0,
                                            o
                                    }),
                                        o = e.sortConversationList(o),
                                    {
                                        totalUnreadCount: r,
                                        convList: o
                                    }
                                })
                        }
                    }, {
                        key: "getIQConversations",
                        value: function () {
                            var e = this;
                            try {
                                window._n("UNQ")
                            } catch (t) { }
                            return a.Z.getIQConversations.post({
                                lastMsgTime: 0
                            }).then(function (t) {
                                try {
                                    window._n("h2n")
                                } catch (n) { }
                                var o = 0;
                                return t.categoryList.forEach(function (t, n) {
                                    try {
                                        window._n("R6y")
                                    } catch (r) { }
                                    t.isOpen = t.unreadCount > 0 && 0 == o,
                                        t.categorySubList && t.categorySubList.length > 0 || (t.chatType = s.Z.CommonEnum.ConversationType.Notice,
                                            t.parentCategoryId = "",
                                            t.id = "_" + t.categoryId,
                                            t.gid = "_" + t.categoryId,
                                            t.lastMessageTime = t.lastMsgTime || 0,
                                            t.status = 2),
                                        o += t.unreadCount,
                                        (t.categorySubList || []).forEach(function (e) {
                                            try {
                                                window._n("BJp")
                                            } catch (n) { }
                                            e.chatType = s.Z.CommonEnum.ConversationType.Notice,
                                                e.parentCategoryId = t.categoryId,
                                                e.categoryId = e.categoryId,
                                                e.id = t.categoryId + "_" + e.categoryId,
                                                e.gid = t.categoryId + "_" + e.categoryId,
                                                e.lastMessageTime = e.lastMsgTime,
                                                e.status = 2
                                        }),
                                        t.categorySubList && t.categorySubList.length > 0 && (t.categorySubList = e.sortNoticeList(t.categorySubList))
                                }),
                                    0 == o && t.categoryList && t.categoryList[0] && (t.categoryList[0].isOpen = !0),
                                {
                                    totalUnreadCount: o,
                                    convList: t.categoryList
                                }
                            })
                        }
                    }, {
                        key: "sortNoticeList",
                        value: function (e) {
                            try {
                                window._n("ngP")
                            } catch (t) { }
                            return e.forEach(function (e) {
                                try {
                                    window._n("BsY")
                                } catch (t) { }
                                e.time = e.lastMessageTime || e.categoryId
                            }),
                                (e || []).sort(function (e, t) {
                                    try {
                                        window._n("vd7")
                                    } catch (n) { }
                                    return e.time && t.time && e.time < t.time || !e.time && t.time ? 1 : -1
                                })
                        }
                    }, {
                        key: "getIQMessages",
                        value: function (e, t) {
                            try {
                                window._n("Q8Y")
                            } catch (n) { }
                            return a.Z.getIQMessages.post(e).then(function (e) {
                                try {
                                    window._n("Vtl")
                                } catch (n) { }
                                var o = e.iqMessages || [];
                                return o.reverse(),
                                    o.forEach(function (e) {
                                        try {
                                            window._n("F3h")
                                        } catch (t) { }
                                        e.isRead = !0,
                                            e.status = 0,
                                            e.subject = "",
                                            e.threadId = "",
                                            e.type = "chat"
                                    }),
                                    o = u.Z.jsonSerialize(o, t.uid),
                                {
                                    messages: o = u.Z.filterMessage(o, t),
                                    lastMessageTime: o.length > 0 ? o[0].createTime : 0
                                }
                            })
                        }
                    }, {
                        key: "singleMessageToConv",
                        value: function (e, t, n) {
                            try {
                                window._n("EJZ")
                            } catch (o) { }
                            var r = {}
                                , i = t ? e.fromGid : e.partnerJid;
                            r[e.partnerJid] = {
                                avatar: e.avatar,
                                nick: t ? e.fromGid : e.name,
                                uid: t ? e.fromGid : e.partnerJid,
                                remark: ""
                            };
                            var a = e;
                            return t || (a = u.Z.serializeMessage({
                                messageBody: e.lastMessage,
                                msgtype: e.msgType,
                                createTime: e.lastTimestamp,
                                fromJid: "",
                                toJid: ""
                            })),
                                w(w({}, e), {}, {
                                    chatTarget: i,
                                    memberHash: r,
                                    unReadMessageCount: e.unreadCount,
                                    lastMessage: a,
                                    lastMessageTime: e.lastTimestamp || e.createTime,
                                    chatType: n || (l.Z.isSysNoticeMessage(e) ? s.Z.CommonEnum.ConversationType.Notice : s.Z.CommonEnum.ConversationType.SingleChat),
                                    partnerJid: e.partnerJid || e.gid,
                                    gid: t ? e.fromGid : e.partnerJid,
                                    id: t ? e.fromGid : e.partnerJid,
                                    status: 2
                                })
                        }
                    }, {
                        key: "xingqiuMessageToConv",
                        value: function (e, t, n) {
                            try {
                                window._n("RKX")
                            } catch (o) { }
                            var r = {}
                                , i = t ? e.fromGid : e.partnerJid;
                            r[e.partnerJid] = {
                                avatar: e.avatar,
                                nick: t ? e.fromGid : e.nickName,
                                uid: t ? e.fromGid : e.partnerJid,
                                remark: ""
                            };
                            var a = {};
                            e.lastTimestamp = 1617980761438;
                            try {
                                a = u.Z.serializeMessage(JSON.parse(e.lastMessageJsonString))
                            } catch (c) { }
                            return w(w({}, e), {}, {
                                chatTarget: i,
                                memberHash: r,
                                unReadMessageCount: e.unreadCount,
                                lastMessage: a,
                                chatType: e.chatType,
                                partnerJid: e.partnerJid || e.gid,
                                gid: t ? e.fromGid : e.partnerJid,
                                id: t ? e.fromGid : e.partnerJid,
                                status: 2,
                                serviceType: e.bizType,
                                groupTitle: e.groupName,
                                lastUpdateTime: e.lastMessageTime
                            })
                        }
                    }, {
                        key: "agentApplyTask",
                        value: function (e, t) {
                            try {
                                window._n("W5x")
                            } catch (n) { }
                            return a.Z.agentApplyTask.post({
                                taskKey: e,
                                vendorId: t
                            }).then(function (e) {
                                try {
                                    window._n("sw3")
                                } catch (t) { }
                                return e
                            })
                        }
                    }, {
                        key: "startChatC2O",
                        value: function (e) {
                            try {
                                window._n("bGK")
                            } catch (t) { }
                            return a.Z.startChatC2O.post(e).then(function (e) {
                                try {
                                    window._n("qvN")
                                } catch (t) { }
                                return e
                            })
                        }
                    }, {
                        key: "isXQHFansConv",
                        value: function (e) {
                            try {
                                window._n("iFX")
                            } catch (t) { }
                            return !!e && "xqhfans" == e.convtype
                        }
                    }]),
                    e
            }();
        t.Z = new Z
    },
    89485: function (e, t, n) {
        n.d(t, {
            Z: function () {
                return l
            }
        });
        var o = n(47813)
            , r = n(11025)
            , i = n(74213)
            , a = n(73383)
            , c = n(61023)
            , s = n(47015);
        function u(e, t) {
            var n = Object.keys(e);
            if (Object.getOwnPropertySymbols) {
                var o = Object.getOwnPropertySymbols(e);
                t && (o = o.filter(function (t) {
                    return Object.getOwnPropertyDescriptor(e, t).enumerable
                })),
                    n.push.apply(n, o)
            }
            return n
        }
        function d(e) {
            for (var t = 1; t < arguments.length; t++) {
                var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? u(Object(n), !0).forEach(function (t) {
                    (0,
                        o.Z)(e, t, n[t])
                }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : u(Object(n)).forEach(function (t) {
                    Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                })
            }
            return e
        }
        n(13485);
        var l = function () {
            function e() {
                (0,
                    r.Z)(this, e)
            }
            return (0,
                i.Z)(e, null, [{
                    key: "getCurrentGroupMemeber",
                    value: function (e) {
                        try {
                            window._n("RLB")
                        } catch (t) { }
                        return new Promise("xqhfans" == e.convtype ? function (t, n) {
                            try {
                                window._n("Jy5")
                            } catch (o) { }
                            a.Z.GroupMemberModel2.ping(e, function (e) {
                                try {
                                    window._n("9N6")
                                } catch (n) { }
                                (e = e.sort(function (e, t) {
                                    try {
                                        window._n("iWN")
                                    } catch (n) { }
                                    var o = 5 == e.roles[0] || 1 == e.roles[1]
                                        , r = 5 == t.roles[0] || 1 == t.roles[1];
                                    return 4 == e.roles[1] || o && 4 != t.roles[1] || 2 == e.roles[1] && !r && 4 != t.roles[1] ? -1 : 0
                                })).forEach(function (e) {
                                    try {
                                        window._n("X9x")
                                    } catch (t) { }
                                    e.nick = e.nickName,
                                        e.uid = e.username
                                }),
                                    t(e)
                            })
                        }
                            : function (t, n) {
                                try {
                                    window._n("44D")
                                } catch (o) { }
                                a.Z.groupMemberModel.cancel(),
                                    a.Z.groupMemberModel.post({
                                        groupId: e.gid
                                    }).then(function (e) {
                                        try {
                                            window._n("Doi")
                                        } catch (n) { }
                                        var o = e.members;
                                        t(o = o.sort(function (e, t) {
                                            try {
                                                window._n("qq3")
                                            } catch (n) { }
                                            var o = 5 == e.roles[0] || 1 == e.roles[1]
                                                , r = 5 == t.roles[0] || 1 == t.roles[1];
                                            return 4 == e.roles[1] || o && 4 != t.roles[1] || 2 == e.roles[1] && !r && 4 != t.roles[1] ? -1 : 0
                                        }))
                                    }).catch(function (e) {
                                        try {
                                            window._n("UTx")
                                        } catch (n) { }
                                        t([])
                                    })
                            }
                        )
                    }
                }, {
                    key: "getAllGroupMembers",
                    value: function (e, t) {
                        try {
                            window._n("ZYC")
                        } catch (n) { }
                        var o = {};
                        return o.gid = e,
                            a.Z.getAllGroupMembersAndRemark.post(o).then(function (e) {
                                try {
                                    window._n("bnL")
                                } catch (t) { }
                                var n = {
                                    members: e.remarkList,
                                    memberHash: {},
                                    robot: {}
                                };
                                return n.members.map(function (e) {
                                    try {
                                        window._n("eAH")
                                    } catch (t) { }
                                    var o = {
                                        uid: e.username.toLowerCase(),
                                        nick: s.Z.Agent.getSharkName(e.nickName),
                                        avatar: e.avatar,
                                        roles: e.roles,
                                        status: e.status,
                                        remark: e.remark
                                    };
                                    return 2 == o.roles[1] && (n.robot = o),
                                        n.memberHash[o.uid] = o,
                                        o
                                }),
                                    n
                            })
                    }
                }, {
                    key: "getAllGroupMembersByQuery",
                    value: function (e) {
                        try {
                            window._n("sUZ")
                        } catch (t) { }
                        var n = {
                            mode: 1
                        };
                        return n.gid = e,
                            a.Z.getAllGroupMembersAndRemark.post(n).then(function (e) {
                                try {
                                    window._n("jtg")
                                } catch (t) { }
                                var n = {
                                    members: e.remarkList,
                                    memberHash: {},
                                    robot: {}
                                };
                                return n.members.map(function (e) {
                                    try {
                                        window._n("YWu")
                                    } catch (t) { }
                                    var o = {
                                        uid: e.username.toLowerCase(),
                                        nick: s.Z.Agent.getSharkName(e.nickName),
                                        avatar: e.avatar,
                                        roles: e.roles,
                                        status: e.status,
                                        remark: e.remark
                                    };
                                    return 2 == o.roles[1] && (n.robot = o),
                                        n.memberHash[o.uid] = o,
                                        o
                                }),
                                    n
                            })
                    }
                }, {
                    key: "getAllGroupMemberList",
                    value: function (e) {
                        try {
                            window._n("evm")
                        } catch (t) { }
                        var n = {
                            members: e,
                            memberHash: {},
                            robot: {}
                        };
                        return n.members.map(function (e) {
                            try {
                                window._n("pFM")
                            } catch (t) { }
                            var o = {
                                uid: e.username.toLowerCase(),
                                nick: s.Z.Agent.getSharkName(e.nickName),
                                avatar: e.avatar,
                                roles: e.roles,
                                status: e.status,
                                remark: e.remark
                            };
                            return 2 == o.roles[1] && (n.robot = o),
                                n.memberHash[o.uid] = o,
                                o
                        }),
                            n
                    }
                }, {
                    key: "inviteMember",
                    value: function (e, t) {
                        try {
                            window._n("jjF")
                        } catch (n) { }
                        var o = {
                            venAgentUidList: (e || []).map(function (e) {
                                try {
                                    window._n("2Dr")
                                } catch (t) { }
                                return e.agentUid
                            }),
                            serviceType: t.serviceType,
                            workSheetId: t.id + ""
                        };
                        return a.Z.inviteAgentModel.post(o)
                    }
                }, {
                    key: "getInviteMemberList",
                    value: function (e, t) {
                        try {
                            window._n("oup")
                        } catch (n) { }
                        return a.Z.inviteMemberListModel.post(e).then(function (e) {
                            try {
                                window._n("xHd")
                            } catch (n) { }
                            return e.agentInfos = (e.agentInfos || []).map(function (e, n) {
                                try {
                                    window._n("TQg")
                                } catch (o) { }
                                e.add = !0;
                                var r = e.agentUid.toLowerCase();
                                return (t || []).map(function (t, n) {
                                    try {
                                        window._n("6oY")
                                    } catch (o) { }
                                    r == t.uid.toLowerCase() && (e.add = !1)
                                }),
                                    e
                            }),
                                e
                        })
                    }
                }, {
                    key: "modifyRemark",
                    value: function (e, t) {
                        try {
                            window._n("kDM")
                        } catch (n) { }
                        return a.Z.modifyRemark.post({
                            targetUid: e,
                            remark: t
                        })
                    }
                }, {
                    key: "isMainOperatorInGroup",
                    value: function (e, t) {
                        try {
                            window._n("aSJ")
                        } catch (n) { }
                        var o = !1;
                        return e.map(function (e) {
                            try {
                                window._n("zi9")
                            } catch (n) { }
                            e.uid.toLowerCase() == (t || "").toLowerCase() && (o = 4 == e.roles[1])
                        }),
                            o
                    }
                }, {
                    key: "isMainCustomerInGroup",
                    value: function (e, t) {
                        try {
                            window._n("0Gc")
                        } catch (n) { }
                        var o = !1;
                        return e.map(function (e) {
                            try {
                                window._n("UkL")
                            } catch (n) { }
                            e.uid.toLowerCase() == (t || "").toLowerCase() && (o = 5 == e.roles[0] && 3 == e.roles[1])
                        }),
                            o
                    }
                }, {
                    key: "hasMainOperatorInGroup",
                    value: function (e) {
                        try {
                            window._n("dzd")
                        } catch (t) { }
                        var n = !1;
                        return e.map(function (e) {
                            try {
                                window._n("KWV")
                            } catch (t) { }
                            4 == e.roles[1] && 4 == e.roles[2] && (n = !0),
                                4 == e.roles[1] && 0 == e.roles[2] && (n = !0)
                        }),
                            n
                    }
                }, {
                    key: "isCustomer",
                    value: function (e) {
                        try {
                            window._n("9PT")
                        } catch (t) { }
                        return 1 == (e.roles || [])[1]
                    }
                }, {
                    key: "isVendorAgent",
                    value: function (e) {
                        try {
                            window._n("T92")
                        } catch (t) { }
                        return 4 == (e.roles || [])[2]
                    }
                }, {
                    key: "isCtripAgent",
                    value: function (e) {
                        try {
                            window._n("Abh")
                        } catch (t) { }
                        var n = e.roles || [];
                        return (3 == n[1] || 4 == n[1]) && 4 != n[2] && 5 != n[2]
                    }
                }, {
                    key: "getVendorAgent",
                    value: function (t) {
                        try {
                            window._n("oz2")
                        } catch (n) { }
                        var o = 0
                            , r = [];
                        return t.map(function (t) {
                            try {
                                window._n("ltO")
                            } catch (n) { }
                            e.isVendorAgent(t) && (o++,
                                r.push(t))
                        }),
                            r
                    }
                }, {
                    key: "transferVenMaster",
                    value: function (e, t) {
                        try {
                            window._n("0rJ")
                        } catch (n) { }
                        return a.Z.transferVenMaster.post({
                            groupId: e.gid,
                            sessionId: e.sessionId,
                            targetVenAgentUid: t.uid
                        })
                    }
                }, {
                    key: "deleteChatMember",
                    value: function (e, t) {
                        try {
                            window._n("asE")
                        } catch (n) { }
                        return a.Z.finishConversationModel.post({
                            conversationKey: e.conversationKey,
                            conversationType: e.conversationType,
                            sessionId: e.sessionId,
                            workSheetId: e.id,
                            venAgentUids: t
                        })
                    }
                }, {
                    key: "removeXQHMember",
                    value: function (e, t) {
                        try {
                            window._n("tHd")
                        } catch (n) { }
                        return a.Z.removeMember.post({
                            gid: e.gid,
                            removeMemberList: t
                        })
                    }
                }, {
                    key: "updateGroupTitle",
                    value: function (e, t) {
                        try {
                            window._n("obN")
                        } catch (n) { }
                        return a.Z.updateGroupTitle.post({
                            groupId: e.gid,
                            groupTitle: t,
                            bizType: e.sessionId
                        })
                    }
                }, {
                    key: "getChatTargetDetailInfo",
                    value: function (t) {
                        try {
                            window._n("lLu")
                        } catch (n) { }
                        var o = e.getChatTargetUid(t);
                        return a.Z.customAndAgentBasicInfoModel.post({
                            agentUidList: [o]
                        }).then(function (e) {
                            try {
                                window._n("bkZ")
                            } catch (t) { }
                            var n = (e.agentInfoList || [])[0] || {};
                            return {
                                uid: n.agentUid,
                                nick: n.agentNickname,
                                avatar: n.agentAvatar
                            }
                        })
                    }
                }, {
                    key: "getChatTargetUid",
                    value: function (e) {
                        try {
                            window._n("n5j")
                        } catch (t) { }
                        var n = (e.uid || "").toLowerCase()
                            , o = (e.ctripAgentId || "").toLowerCase()
                            , r = (e.b2bTargetUid || "").toLowerCase()
                            , i = "";
                        return c.Z.CommonEnum.ConvTabs.BC.convTypes.indexOf(e.conversationType) >= 0 ? i = n : e.conversationType == c.Z.CommonEnum.ConvType.B2B ? i = r : c.Z.CommonEnum.ConvTabs.BOP.convTypes.indexOf(e.conversationType) >= 0 && (i = o),
                            i
                    }
                }, {
                    key: "getChatTargetDetailInfoByConvList",
                    value: function () {
                        var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [];
                        try {
                            window._n("IPR")
                        } catch (n) { }
                        var o = {}
                            , r = [];
                        return t.map(function (t) {
                            try {
                                window._n("YE2")
                            } catch (n) { }
                            var i = e.getChatTargetUid(t);
                            i && (o[i] || (o[i] = []),
                                o[i].push(t.gid),
                                r.push(i))
                        }),
                            a.Z.getAllGroupMembersAndRemark.post({
                                uidList: r
                            }).then(function (e) {
                                try {
                                    window._n("jB6")
                                } catch (t) { }
                                var n = {};
                                return (e.remarkList || []).map(function (e) {
                                    try {
                                        window._n("Zkb")
                                    } catch (t) { }
                                    for (var r = e.username.toLowerCase(), i = {
                                        uid: r,
                                        nick: s.Z.Agent.getSharkName(e.nickName),
                                        avatar: e.avatar,
                                        roles: e.roles,
                                        status: e.status,
                                        remark: e.remark
                                    }, a = o[r] || [], c = 0; c < a.length; c++)
                                        n[a[c]] = i;
                                    return i
                                }),
                                    n
                            })
                    }
                }, {
                    key: "getInviteMemberListBySkillGroup",
                    value: function (e) {
                        try {
                            window._n("EDh")
                        } catch (t) { }
                        return a.Z.getInviteMemberListBySkillGroup.post(d({}, e))
                    }
                }, {
                    key: "searchInviteMemberListBySkillGroup",
                    value: function (e) {
                        try {
                            window._n("6qf")
                        } catch (t) { }
                        return a.Z.searchInviteMemberListBySkillGroup.post(d({}, e))
                    }
                }]),
                e
        }()
    },
    631: function (e, t, n) {
        var o = n(11025)
            , r = n(74213)
            , i = n(47813)
            , a = n(2037)
            , c = n(61917)
            , s = n(80830)
            , u = n(57770)
            , d = n(7315)
            , l = n(61023)
            , m = n(89312)
            , y = n(32336)
            , h = n(85943);
        n(13485);
        var _ = function () {
            function e() {
                (0,
                    o.Z)(this, e)
            }
            return (0,
                r.Z)(e, null, [{
                    key: "init",
                    value: function (t, n) {
                        try {
                            window._n("4BO")
                        } catch (o) { }
                        if (e.inited)
                            return Promise.resolve(!0);
                        var r = new y.Z;
                        return e.currentAgentInfo = t,
                            IMClient.setOnReceiveMessageListener({
                                onReceived: function (e) {
                                    try {
                                        window._n("Ymz")
                                    } catch (n) { }
                                    if (e && 0 == e.errorcode && e.messages && e.messages.length > 0) {
                                        var o = s.Z.parseMessagesFromSDK(e.messages, t.uid);
                                        o = o.filter(function (e) {
                                            try {
                                                window._n("VrG")
                                            } catch (t) { }
                                            return !h.Z.isInNewMessageBlackList(e.bizType) && (!h.Z.isIqNotice() || !h.Z.isSysNoticeMessage(e))
                                        }),
                                            a.Z.publish(c.NEW_MESSAGE_EVENT, [o]),
                                            "/main" == location.pathname && r.send({
                                                type: l.Z.LogTypeEnum.received_message,
                                                sub_type: "message",
                                                messageType: o.map(function (e) {
                                                    return e.messageType
                                                }).join(","),
                                                messageId: o.map(function (e) {
                                                    return e.messageId
                                                }).join(",")
                                            })
                                    }
                                }
                            }),
                            h.Z.isIqNotice() && IMClient.setOnReceiveNotificationListener({
                                onReceived: function (e) {
                                    try {
                                        window._n("Viv")
                                    } catch (n) { }
                                    var o = !1;
                                    window.__app_config__ && window.__app_config__.useNewIMSDK && (o = !0),
                                        o && (e = e.messages[0]),
                                        e.isIqNotice = !0,
                                        e.conversationType = "chat",
                                        e.createThread = null,
                                        e.fromJid = e.from,
                                        e.fromNickname = null,
                                        e.messageCategory = e.messageType,
                                        e.toJid = e.to,
                                        e.createTime = parseInt(e.createTime);
                                    var r = s.Z.parseMessagesFromSDK([e], t.uid);
                                    a.Z.publish(c.NEW_MESSAGE_EVENT, [r]);
                                    var i = new y.Z
                                        , u = r.map(function (e) {
                                            return e.messageId
                                        });
                                    0 !== r.length && i.send({
                                        type: l.Z.LogTypeEnum.received_message,
                                        sub_type: "iq_message",
                                        status: "success",
                                        messageId: u.join("")
                                    })
                                }
                            }),
                            IMClient.setConnectionStatusListener({
                                onChanged: function (t) {
                                    try {
                                        window._n("IBw")
                                    } catch (n) { }
                                    e.status = t,
                                        a.Z.publish(c.IM_SDK_CONNECT_CHANGE_EVENT, [t]),
                                        t !== CommonEnum.ConnectionStatus.CONNECTED && (0,
                                            d.Z)(l.Z.UBTTypeEnum.IM_WS_STATUS_CHANGE, {
                                                status: t
                                            })
                                }
                            }),
                            IMClient.setInnersSignalListener({
                                onData: function (e, t) {
                                    try {
                                        window._n("VKg")
                                    } catch (n) { }
                                    0 == e && r.send({
                                        type: l.Z.LogTypeEnum.socket_status,
                                        status: 0 == t.result ? "fail" : "success",
                                        value_desc: t.result
                                    })
                                }
                            }),
                            new Promise(function (t, o) {
                                try {
                                    window._n("bnw")
                                } catch (r) { }
                                var i = (0,
                                    u.m)(location.search)
                                    , a = {
                                        conversationType: "conversationList",
                                        initPageSize: 0,
                                        appId: "5130",
                                        language: i.lang || "",
                                        withoutExtra: !0,
                                        customHTTPExts: [{
                                            name: "amp-product-type",
                                            value: "IM"
                                        }, {
                                            name: "amp-account-source",
                                            value: i && i.accountsource || ""
                                        }, {
                                            name: "client-source",
                                            value: i && i["client-source"] || ""
                                        }]
                                    };
                                n && (a.disableUBT = !0),
                                    i.auth && (a.token = i.auth),
                                    "function" == typeof IMClient.setNetwork && IMClient.setNetwork("external"),
                                    IMClient.init(a, function (n) {
                                        try {
                                            window._n("f0E")
                                        } catch (r) { }
                                        e.inited = !0,
                                            IMClient.connect({}, function (e) {
                                                try {
                                                    window._n("WwG")
                                                } catch (o) { }
                                                t(n)
                                            }, function (e) {
                                                try {
                                                    window._n("Hm6")
                                                } catch (t) { }
                                                o(e)
                                            })
                                    }, function (e) {
                                        try {
                                            window._n("rBW")
                                        } catch (n) { }
                                        IMClient.connect({}, function (e) {
                                            try {
                                                window._n("2Ad")
                                            } catch (n) { }
                                            t(e)
                                        }, function (e) {
                                            try {
                                                window._n("7Lj")
                                            } catch (t) { }
                                            o(e)
                                        })
                                    })
                            }
                            )
                    }
                }, {
                    key: "reportSocketStatus",
                    value: function () {
                        try {
                            window._n("73c")
                        } catch (t) { }
                        var n = function () {
                            try {
                                window._n("gcA")
                            } catch (t) { }
                            var n = e.status;
                            new y.Z().send({
                                type: l.Z.LogTypeEnum.socket_connect_status,
                                status: n == CommonEnum.ConnectionStatus.CONNECTED ? "success" : "fail",
                                value: n
                            })
                        };
                        setInterval(function () {
                            try {
                                window._n("OqM")
                            } catch (e) { }
                            try {
                                n()
                            } catch (t) { }
                        }, 6e4)
                    }
                }]),
                e
        }();
        (0,
            i.Z)(_, "inited", !1),
            (0,
                i.Z)(_, "currentAgentInfo", {}),
            (0,
                i.Z)(_, "status", -1),
            (0,
                i.Z)(_, "asqueue", new m.Z(function (e) {
                    try {
                        window._n("dun")
                    } catch (t) { }
                    return new Promise(function (t, n) {
                        try {
                            window._n("4XJ")
                        } catch (o) { }
                        var r = s.Z.parseMessagesFromSDK(e, _.currentAgentInfo.uid);
                        a.Z.publish(c.NEW_MESSAGE_EVENT, [r]),
                            setTimeout(function () {
                                try {
                                    window._n("PC6")
                                } catch (e) { }
                                t()
                            }, 100)
                    }
                    )
                }
                    , 1e3)),
            t.Z = _
    },
    47015: function (e, t, n) {
        n.d(t, {
            Z: function () {
                return g
            }
        });
        var o = n(92557)
            , r = n(631)
            , i = n(8797)
            , a = n(80830)
            , c = n(89485)
            , s = n(85943)
            , u = n(61210)
            , d = n(11025)
            , l = n(74213)
            , m = n(73383);
        n(61023),
            n(99797),
            n(13485);
        var y = function () {
            function e() {
                (0,
                    d.Z)(this, e)
            }
            return (0,
                l.Z)(e, null, [{
                    key: "search",
                    value: function (e, t) {
                        try {
                            window._n("fBg")
                        } catch (n) { }
                        return m.Z.searchConversation.post(e).then(function (n) {
                            try {
                                window._n("LmY")
                            } catch (o) { }
                            var r = n.contentSearchList
                                , i = void 0 === r ? [] : r
                                , c = n.customerSearchList
                                , s = void 0 === c ? [] : c
                                , u = n.orderSearchList
                                , d = void 0 === u ? [] : u
                                , l = n.dateSearchList
                                , m = void 0 === l ? [] : l
                                , y = n.pageInfo;
                            return i = i.map(function (n) {
                                try {
                                    window._n("rAR")
                                } catch (o) { }
                                var r = n.message
                                    , i = n.conversation || {};
                                return i.searchKeyword = e.searchValue,
                                    r && (i.lastMessageTime = r.createTime,
                                        r = a.Z.serializeMessage(r, t)),
                                {
                                    Message: r,
                                    conversation: i
                                }
                            }),
                                s = s.map(function (n) {
                                    try {
                                        window._n("d0X")
                                    } catch (o) { }
                                    var r = n.message
                                        , i = n.conversation || {};
                                    return i.searchKeyword = e.searchValue,
                                        r && (i.lastMessageTime = r.createTime,
                                            r = a.Z.serializeMessage(r, t)),
                                    {
                                        Message: r,
                                        conversation: i
                                    }
                                }),
                                d = d.map(function (n) {
                                    try {
                                        window._n("Nwx")
                                    } catch (o) { }
                                    var r = n.message
                                        , i = n.conversation || {};
                                    return i.searchKeyword = e.searchValue,
                                        r && (i.lastMessageTime = r.createTime,
                                            r = a.Z.serializeMessage(r, t)),
                                    {
                                        Message: r,
                                        conversation: i
                                    }
                                }),
                            {
                                contentSearchList: i,
                                customerSearchList: s,
                                dateSearchList: m = m.map(function (n) {
                                    try {
                                        window._n("Dof")
                                    } catch (o) { }
                                    var r = n.message
                                        , i = n.conversation || {};
                                    return i.searchKeyword = e.searchValue,
                                        r && (i.lastMessageTime = r.createTime,
                                            r = a.Z.serializeMessage(r, t)),
                                    {
                                        Message: r,
                                        conversation: i
                                    }
                                }),
                                orderSearchList: d,
                                pageInfo: void 0 === y ? {} : y
                            }
                        })
                    }
                }]),
                e
        }()
            , h = n(47813);
        function _(e, t) {
            var n = Object.keys(e);
            if (Object.getOwnPropertySymbols) {
                var o = Object.getOwnPropertySymbols(e);
                t && (o = o.filter(function (t) {
                    return Object.getOwnPropertyDescriptor(e, t).enumerable
                })),
                    n.push.apply(n, o)
            }
            return n
        }
        n(13485);
        var f = function () {
            function e() {
                (0,
                    d.Z)(this, e)
            }
            return (0,
                l.Z)(e, null, [{
                    key: "addCategory",
                    value: function (e, t, n) {
                        try {
                            window._n("YgU")
                        } catch (o) { }
                        return m.Z.createCategory.post({
                            name: e,
                            type: "CUSTOMSCRIPT",
                            status: "ACTIVE",
                            ownerType: t,
                            vSkillGroupIds: n
                        })
                    }
                }, {
                    key: "updateCategory",
                    value: function (e, t, n) {
                        try {
                            window._n("eW3")
                        } catch (o) { }
                        return m.Z.updateCategory.post({
                            id: e,
                            name: t,
                            status: "ACTIVE",
                            vSkillGroupIds: n
                        })
                    }
                }, {
                    key: "deleteCategory",
                    value: function (e) {
                        try {
                            window._n("JMw")
                        } catch (t) { }
                        return m.Z.deleteCategory.post({
                            id: e
                        })
                    }
                }, {
                    key: "sortCategory",
                    value: function (e) {
                        try {
                            window._n("Nts")
                        } catch (t) { }
                        return m.Z.updateCategorySortNo.post({
                            categorySortList: e
                        })
                    }
                }, {
                    key: "getCategories",
                    value: function () {
                        var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "VAGENT";
                        try {
                            window._n("6wn")
                        } catch (t) { }
                        return m.Z.getCategories.post({
                            typeList: [e],
                            categoryType: "CUSTOMSCRIPT",
                            status: "ACTIVE"
                        })
                    }
                }, {
                    key: "addCustomerScript",
                    value: function (e, t, n) {
                        try {
                            window._n("ymI")
                        } catch (o) { }
                        return m.Z.addCustomerScript.post({
                            content: e,
                            categoryCode: t,
                            ownerType: n
                        })
                    }
                }, {
                    key: "sortCustomerScript",
                    value: function (e) {
                        try {
                            window._n("KpM")
                        } catch (t) { }
                        return m.Z.updateCustomerScriptSort.post({
                            sortInfos: e
                        })
                    }
                }, {
                    key: "updateCustomerScript",
                    value: function (e, t, n) {
                        try {
                            window._n("t9P")
                        } catch (o) { }
                        return m.Z.updateCustomerScript.post({
                            id: e + "",
                            content: t,
                            categoryCode: n
                        })
                    }
                }, {
                    key: "deleteCustomerScript",
                    value: function (e) {
                        try {
                            window._n("Ldp")
                        } catch (t) { }
                        return m.Z.deleteCustomerScript.post({
                            id: e
                        })
                    }
                }, {
                    key: "getCustomerScripts",
                    value: function () {
                        var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
                        try {
                            window._n("ZMS")
                        } catch (t) { }
                        var n = {};
                        return e.code && (n.categoryCode = e.code),
                            e.content && (n.content = e.content),
                            m.Z.getCustomerScripts.post(function (e) {
                                for (var t = 1; t < arguments.length; t++) {
                                    var n = null != arguments[t] ? arguments[t] : {};
                                    t % 2 ? _(Object(n), !0).forEach(function (t) {
                                        (0,
                                            h.Z)(e, t, n[t])
                                    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : _(Object(n)).forEach(function (t) {
                                        Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                                    })
                                }
                                return e
                            }({}, n))
                    }
                }, {
                    key: "getSkillGroupInfoByVenAgent",
                    value: function (e) {
                        try {
                            window._n("Yoj")
                        } catch (t) { }
                        return m.Z.getSkillGroupInfoByVenAgent.post({
                            agentUid: e
                        })
                    }
                }]),
                e
        }();
        n(13485);
        var g = {
            ACDSDK: o.Z,
            IMSDK: r.Z,
            Conversation: i.Z,
            Message: a.Z,
            GroupMember: c.Z,
            Configs: s.Z,
            Agent: u.Z,
            Search: y,
            QuickReplay: f
        }
    },
    80830: function (e, t, n) {
        var o = n(47813)
            , r = n(11025)
            , i = n(74213)
            , a = n(61023)
            , c = n(73383)
            , s = n(32336)
            , u = n(60414)
            , d = n(2037)
            , l = n(11549)
            , m = n(95677)
            , y = n(61210)
            , h = n(7315)
            , _ = n(17587)
            , f = n(47015)
            , g = n(30381)
            , p = n.n(g)
            , w = n(57770)
            , v = n(48978)
            , C = n(46246);
        function Z(e, t) {
            var n = Object.keys(e);
            if (Object.getOwnPropertySymbols) {
                var o = Object.getOwnPropertySymbols(e);
                t && (o = o.filter(function (t) {
                    return Object.getOwnPropertyDescriptor(e, t).enumerable
                })),
                    n.push.apply(n, o)
            }
            return n
        }
        function T(e) {
            for (var t = 1; t < arguments.length; t++) {
                var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? Z(Object(n), !0).forEach(function (t) {
                    (0,
                        o.Z)(e, t, n[t])
                }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Z(Object(n)).forEach(function (t) {
                    Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                })
            }
            return e
        }
        n(13485);
        var E = function () {
            function e() {
                (0,
                    r.Z)(this, e)
            }
            return (0,
                i.Z)(e, null, [{
                    key: "jsonSerialize",
                    value: function (t, n) {
                        try {
                            window._n("que")
                        } catch (o) { }
                        var r = [];
                        return t && ("[object Array]" !== Object.prototype.toString.call(t) && (t = [t]),
                            t.forEach(function (t) {
                                try {
                                    window._n("znO")
                                } catch (o) { }
                                var i = e.serializeMessage(t, n);
                                i && r.push(i)
                            })),
                            r
                    }
                }, {
                    key: "serializeMessage",
                    value: function (t, n) {
                        try {
                            window._n("a6g")
                        } catch (o) { }
                        var r = null
                            , i = {}
                            , a = void 0 !== t.msgtype ? t.msgtype : t.msgType;
                        if (n || (n = y.Z.getCurrentUser().ctripUid || ""),
                            t && void 0 !== a) {
                            try {
                                i = JSON.parse(t.messageBody)
                            } catch (o) {
                                try {
                                    i = JSON.parse(t.messageBody.replace(/\n/g, "\\n").replace(/\f/g, "\\f").replace(/\r/g, "\\r").replace(/\t/g, "\\t"))
                                } catch (e) {
                                    i = {
                                        title: "不支持的消息类型"
                                    }
                                }
                            }
                            if (null == i && (i = {}),
                                a == CommonEnum.MessageType.TextMessage)
                                (r = new MessageFactory.TextMessage).content = t.messageBody;
                            else if (a == CommonEnum.MessageType.ImageMessage)
                                (r = new MessageFactory.ImageMessage).imageUrl = i.url,
                                    r.thumbUrl = i.thumbUrl,
                                    r.width = i.width,
                                    r.height = i.height;
                            else if (a == CommonEnum.MessageType.CardMessage)
                                (r = new MessageFactory.CardMessage).avatar = i.avatar,
                                    r.title = i.title,
                                    r.desc = i.desc,
                                    r.linkUrl = i.url,
                                    r.type = i.type;
                            else if (a == CommonEnum.MessageType.VideoMessage)
                                (r = new MessageFactory.VideoMessage).title = i.title,
                                    r.extPropertys = i.ext || {},
                                    i.video && (r.filename = i.video.filename,
                                        r.cover = i.video.cover,
                                        r.url = i.video.url,
                                        r.duration = i.video.duration,
                                        r.size = i.video.size,
                                        r.secret = i.video.secret);
                            else if (a == CommonEnum.MessageType.AudioMessage)
                                (r = new MessageFactory.AudioMessage).title = i.title,
                                    r.extPropertys = i.ext,
                                    i.audio && (r.filename = i.audio.filename,
                                        r.url = i.audio.url,
                                        r.duration = i.audio.duration,
                                        r.size = i.audio.size,
                                        r.secret = i.audio.secret);
                            else if (a == CommonEnum.MessageType.FileMessage)
                                (r = new MessageFactory.FileMessage).title = i.title,
                                    r.extPropertys = i.ext,
                                    i.file && (r.filename = i.file.filename,
                                        r.url = i.file.url,
                                        r.size = i.file.size,
                                        r.secret = i.file.secret);
                            else if (a == CommonEnum.MessageType.LocationMessage)
                                (r = new MessageFactory.LocationMessage).title = i.title,
                                    r.extPropertys = i.ext,
                                    i.location && (r.cooType = i.location.cooType,
                                        r.lng = i.location.lng,
                                        r.lat = i.location.lat,
                                        r.thumburl = i.location.thumburl,
                                        r.address = i.location.address,
                                        r.poiName = i.location.poiname,
                                        r.city = i.location.city,
                                        r.country = i.location.country);
                            else if (a == CommonEnum.MessageType.CustomContentMessage)
                                (r = new MessageFactory.CustomContentMessage).title = i.title,
                                    r.messageBody = t.messageBody,
                                    r.extPropertys = i.ext || {},
                                    r.actionCode = i.action,
                                    r.see = i.see,
                                    r.bSee = i.bSee,
                                    r.sessionId = i.sid || i.sessionId,
                                    "number" == typeof r.extPropertys.visibleRule ? (4 & r.extPropertys.visibleRule) == 0 && (r.isPresent = !1) : r.isPresent = i.isPresent;
                            else if (a == CommonEnum.MessageType.EmoticonMessage)
                                (r = new MessageFactory.EmoticonMessage).title = i.title,
                                    r.extPropertys = i.ext || {},
                                    i.emoticon && (r.code = i.emoticon.code,
                                        r.label = i.emoticon.label);
                            else if (a == CommonEnum.MessageType.AtMessage)
                                (r = new MessageFactory.AtMessage).body = i.body,
                                    r.atUid = i.uid,
                                    r.atFromNickname = i.from;
                            else if (a == CommonEnum.MessageType.TemplateMessage)
                                (r = new MessageFactory.TemplateMessage).title = i.title,
                                    r.extPropertys = i.ext || {},
                                    i.templet && (r.code = i.templet.code,
                                        r.values = i.templet.values);
                            else if (a == CommonEnum.MessageType.CiteMessage)
                                (r = new MessageFactory.CiteMessage).title = i.title,
                                    r.threadMid = i.threadMid,
                                    r.cite = i.cite,
                                    r.reply = i.reply;
                            else {
                                if (!(a >= 1e3) || !(a <= 1999))
                                    return;
                                r = new MessageFactory.SystemMessage,
                                    1007 == a || 1012 == a ? (r.title = i.title,
                                        r.extPropertys = i.ext || {},
                                        r.actionCode = i.action,
                                        r.see = i.see,
                                        r.bSee = i.bSee,
                                        r.sessionId = i.sid,
                                        "number" == typeof r.extPropertys.visibleRule ? (4 & r.extPropertys.visibleRule) == 0 && (r.isPresent = !1) : r.isPresent = i.isPresent) : 1009 == a ? (r.isPresent = i.isPresent,
                                            r.title = i.title,
                                            r.operator = i.operator,
                                            r.operatorName = i.operatorName,
                                            r.extPropertys = i.ext || {},
                                            r.recallMessageId = i.recallMessageId,
                                            r.code = i.code,
                                            r.source = i.source) : (r.operator = i.operator,
                                                r.tip = i.tip,
                                                r.gtype = i.gtype,
                                                r.gname = i.gname,
                                                r.butype = i.butype,
                                                r.content = i.content)
                            }
                            r.messageType = a,
                                r.messageId = t.msgId,
                                r.threadId = t.threadId,
                                r.subject = t.subject,
                                r.createTime = t.createTime,
                                r.fromJid = t.fromJid,
                                r.fromNickname = t.fromNick,
                                r.bizType = t.bizType,
                                r.toJid = t.toJid,
                                r.isRead = t.isread,
                                r.isTargetRead = !!t.isTargetRead,
                                r.type = t.type,
                                r.status = t.status,
                                r.contentEncodeList = t.contentEncodeList || [],
                                r.source = t.source || r.source || "";
                            var c = e.parseFromJid(r.fromJid, t, n).fromUid
                                , s = e.parseFromJid(r.fromJid, t, n).fromGid
                                , u = e.parseToJid(r.toJid);
                            i && i.ext && i.ext.replaceSender && (c = i.ext.replaceSender),
                                r.fromUid = c,
                                r.fromGid = s,
                                r.toUid = u
                        }
                        return r
                    }
                }, {
                    key: "parseMessagesFromSDK",
                    value: function (t, n) {
                        try {
                            window._n("wKb")
                        } catch (o) { }
                        (0,
                            w.m)(window.location.href);
                        var r = [];
                        return t && ("[object Array]" !== Object.prototype.toString.call(t) && (t = [t]),
                            t.forEach(function (t) {
                                try {
                                    window._n("d1c")
                                } catch (o) { }
                                if (t && void 0 !== t.messageType) {
                                    var i = e.parseFromJid(t.fromJid, t, n).fromUid
                                        , a = e.parseFromJid(t.fromJid, t, n).fromGid
                                        , c = e.parseToJid(t.toJid);
                                    t.extPropertys && t.extPropertys.replaceSender && (i = t.extPropertys.replaceSender),
                                        t.fromUid = i,
                                        t.fromGid = a,
                                        t.toUid = c,
                                        t.messageType = parseInt(t.messageType),
                                        r.push(t)
                                }
                            })),
                            r
                    }
                }, {
                    key: "parseFromJid",
                    value: function (e, t, n) {
                        try {
                            window._n("obv")
                        } catch (o) { }
                        var r = ""
                            , i = "";
                        if (n || (n = y.Z.getCurrentUser().ctripUid || ""),
                            e) {
                            var a = e.split("/");
                            i = a.length > 1 ? a[1] : null;
                            var c = a[0].split("@");
                            r = c.length > 1 ? c[0] : null
                        }
                        return "chat" != t.type && "chat" != t.conversationType ? {
                            fromUid: i,
                            fromGid: r
                        } : i && i.toLowerCase() == n.toLowerCase() ? {
                            fromUid: i,
                            fromGid: r
                        } : i && i.toLowerCase() != n.toLowerCase() ? {
                            fromUid: r,
                            fromGid: t.partner || r
                        } : {
                            fromUid: r,
                            fromGid: r
                        }
                    }
                }, {
                    key: "parseToJid",
                    value: function (e) {
                        try {
                            window._n("vNJ")
                        } catch (t) { }
                        var n = "";
                        if (e) {
                            var o = e.split("@");
                            n = o.legnth > 1 ? o[0] : null
                        }
                        return n
                    }
                }, {
                    key: "getRangeMessage",
                    value: function (t) {
                        try {
                            window._n("riS")
                        } catch (n) { }
                        return c.Z.messageListModel.post(t).then(function (t) {
                            try {
                                window._n("FKn")
                            } catch (n) { }
                            var o = t.rangeMessage;
                            return o.messages = e.jsonSerialize(o.messages),
                                o
                        })
                    }
                }, {
                    key: "filterMessage",
                    value: function (e, t, n) {
                        var o = arguments.length > 3 && void 0 !== arguments[3] && arguments[3];
                        try {
                            window._n("94Q")
                        } catch (r) { }
                        for (var i = [], c = e.length - 1; c >= 0;) {
                            var s = e[c--];
                            if (o || 3 != s.status || "groupowner" != s.source) {
                                var u = !1;
                                if (a.Z.CommonEnum.NormalMessageType.indexOf(s.messageType) >= 0 && (u = !0),
                                    a.Z.CommonEnum.NotifyMessageType.indexOf(s.messageType) >= 0 && (u = !0),
                                    u) {
                                    if (s.messageCategory == CommonEnum.MessageType.SystemMessage) {
                                        var d = f.Z.Configs.getActionCodeBlackList().indexOf(s.actionCode) < 0 && o;
                                        if ("1009" != s.messageType && "1012" != s.messageType && "1023" != s.messageType && "1022" != s.messageType && "1021" != s.messageType && !("1007" == s.messageType && (a.Z.CommonEnum.AllowActionCodes.indexOf(s.actionCode) >= 0 || d)))
                                            continue
                                    }
                                    if ("CBZ06" != s.actionCode || !s.extPropertys || "FAQ" != s.extPropertys.key || s.title && "" != s.title) {
                                        if ("CTL01" == s.actionCode && s.extPropertys && t.uid) {
                                            if (s.fromUid.toLowerCase() == (t.uid || "").toLowerCase()) {
                                                if ("1" == s.extPropertys.fromCardHide)
                                                    continue
                                            } else if ("1" == s.extPropertys.toCardHide)
                                                continue
                                        }
                                        if (!o) {
                                            if (s.extPropertys && "number" == typeof s.extPropertys.visibleRule) {
                                                if ((4 & s.extPropertys.visibleRule) == 0)
                                                    continue
                                            } else if (!1 === s.isPresent)
                                                continue
                                        }
                                        i.unshift(s)
                                    }
                                }
                            }
                        }
                        return i
                    }
                }, {
                    key: "getExistMessage",
                    value: function (e, t) {
                        try {
                            window._n("jnd")
                        } catch (n) { }
                        return e.find(function (e) {
                            try {
                                window._n("NH1")
                            } catch (n) { }
                            return e.messageId === t.messageId
                        })
                    }
                }, {
                    key: "getMessageText",
                    value: function (e, t) {
                        try {
                            window._n("e0t")
                        } catch (n) { }
                        if (!e)
                            return "";
                        var o = "";
                        switch (e.messageType) {
                            case "7":
                            case 7:
                                o = "CBZ03" == e.actionCode ? "[".concat(u.Z.get("orderIdMain", "", "订单"), "]") : "CBZ09" == e.actionCode ? "[".concat(u.Z.get("voice", "", "语音"), "]") : "CBZ04" == e.actionCode ? "[".concat(u.Z.get("aiFAQTitlte", "", "问答消息"), "]") : e.showTitle || e.title;
                                break;
                            case 1007:
                            case "1007":
                                o = e.showTitle || e.title;
                                break;
                            case 1009:
                            case "1009":
                                2 == e.code && "" == e.operatorName && (e.operatorName = e.fromUid),
                                    o = (e.operatorName || u.Z.get("system", "", "系统")) + " " + u.Z.get("recall_success", "", "撤回了一条消息");
                                break;
                            case 0:
                            case "0":
                                o = e.content;
                                break;
                            case 1:
                            case "1":
                                o = "[".concat(u.Z.get("picture", "", "图片"), "]");
                                break;
                            case 2:
                            case "2":
                                o = "[".concat(u.Z.get("card", "", "卡片"), "]");
                                break;
                            case 3:
                            case "3":
                                o = "[".concat(u.Z.get("video", "", "视频"), "]");
                                break;
                            case 4:
                            case "4":
                                o = "[".concat(u.Z.get("voice", "", "语音"), "]");
                                break;
                            case 5:
                            case "5":
                                o = "[".concat(u.Z.get("file", "", "文件"), "]");
                                break;
                            case 6:
                            case "6":
                                o = "[".concat(u.Z.get("position", "", "位置"), "]");
                                break;
                            case 9:
                            case "9":
                                o = e.body
                        }
                        return "undefined" == typeof twemoji || "function" != typeof twemoji.parse || t || (o = _.Z.HTMLUtil.escape(o),
                            o = m.Z.parse((o || "").toString())),
                            "CBZ58" == e.actionCode && (o = "商品"),
                            "CBZ59" == e.actionCode && (o = "内容"),
                            o
                    }
                }, {
                    key: "sendMessage",
                    value: function (t, n, o) {
                        try {
                            window._n("EHh")
                        } catch (r) { }
                        var i = new s.Z
                            , u = function (r, c) {
                                try {
                                    window._n("7se")
                                } catch (s) { }
                                var u = e._buildAIMessageCommonAttrs(n, o, t);
                                if (t.messageType == a.Z.MessageType.TextMessage) {
                                    u.body.ext.key = (t.options || {}).aiKey || "AI";
                                    var d = (t.options || {}).isAiQuestion || !1;
                                    u.body.ext.questionid && d && (u.body.ext.key = "ANSWER"),
                                        u.body.title = t.content
                                } else
                                    t.messageType == a.Z.MessageType.ImageMessage ? (u.body.ext.key = "PICTURE",
                                        u.body.ext.userbody = JSON.stringify({
                                            btype: 1,
                                            thumbUrl: t.thumbUrl,
                                            url: t.imageUrl,
                                            height: t.height,
                                            width: t.width
                                        })) : t.messageType == a.Z.MessageType.FileMessage ? (u.body.ext.key = "PICTURE",
                                            u.body.ext.userbody = JSON.stringify({
                                                btype: 5,
                                                file: {
                                                    filename: t.filename,
                                                    url: t.url,
                                                    size: t.size
                                                }
                                            })) : t.messageType == a.Z.MessageType.VideoMessage && (u.body.ext.key = "PICTURE",
                                                u.body.ext.userbody = JSON.stringify({
                                                    btype: 5,
                                                    file: {
                                                        filename: t.filename,
                                                        url: t.url,
                                                        size: t.size
                                                    }
                                                }));
                                u.body = JSON.stringify(u.body);
                                var m = new Date;
                                e.sendAIMessage(u, n, o).then(function (e) {
                                    try {
                                        window._n("T4l")
                                    } catch (n) { }
                                    var o = new Date;
                                    i.send({
                                        type: a.Z.LogTypeEnum.send_message,
                                        sub_type: "ai_message",
                                        status: "success",
                                        timecost: o.getTime() - m.getTime()
                                    }),
                                        t.messageType == a.Z.MessageType.ImageMessage || t.messageType == a.Z.MessageType.FileMessage || t.messageType == a.Z.MessageType.VideoMessage ? l(r, c) : r(e)
                                }).catch(function (e) {
                                    try {
                                        window._n("Ptr")
                                    } catch (t) { }
                                    var n = new Date;
                                    i.send({
                                        type: a.Z.LogTypeEnum.send_message,
                                        sub_type: "ai_message",
                                        status: "fail",
                                        timecost: n.getTime() - m.getTime()
                                    }),
                                        c(e)
                                })
                            }
                            , l = function (n, o) {
                                try {
                                    window._n("yvp")
                                } catch (r) { }
                                var c = new Date;
                                e.sendNormalMessage(t).then(function (e) {
                                    try {
                                        window._n("3Qp")
                                    } catch (t) { }
                                    var o = new Date;
                                    i.send({
                                        type: a.Z.LogTypeEnum.send_message,
                                        sub_type: "normal_message",
                                        status: "success",
                                        timecost: o.getTime() - c.getTime()
                                    }),
                                        n(e)
                                }).catch(function (e) {
                                    try {
                                        window._n("IfG")
                                    } catch (t) { }
                                    var n = e.error
                                        , r = e.errmsg
                                        , s = e.message
                                        , u = new Date;
                                    (0,
                                        C.y)(e) && v.Z.logout(e),
                                        i.send({
                                            type: a.Z.LogTypeEnum.send_message,
                                            sub_type: "normal_message",
                                            status: "fail",
                                            errCode: n || "Error",
                                            errMsg: r || s,
                                            timecost: u.getTime() - c.getTime()
                                        }),
                                        o(e)
                                })
                            };
                        return new Promise(function (e, r) {
                            try {
                                window._n("znd")
                            } catch (i) { }
                            t.options && t.options.isForceHuman ? l(e, r) : 1 == n.status ? "" == n.sessionId ? u(e, r) : c.Z.getCurrentSessionMode.post({
                                customerUid: o.ctripUid,
                                gid: n.gid,
                                sessionId: n.sessionId
                            }).then(function (t) {
                                try {
                                    window._n("1WI")
                                } catch (o) { }
                                "ROBOT" == t.mode ? u(e, r) : (n.status = 2,
                                    d.Z.publish(a.Z.EventEnum.NEW_CONVERSATION_EVENT, [[n]]),
                                    l(e, r))
                            }).catch(function (t) {
                                try {
                                    window._n("PU6")
                                } catch (o) { }
                                "ROBOT" == n.initMode ? u(e, r) : l(e, r)
                            }) : l(e, r)
                        }
                        )
                    }
                }, {
                    key: "sendNormalMessage",
                    value: function (e) {
                        try {
                            window._n("1Jh")
                        } catch (t) { }
                        return (0,
                            h.Z)(a.Z.UBTTypeEnum.SEND_MESSAGE, {
                                fromway: "normal_message",
                                messages: e
                            }),
                            new Promise(function (t, n) {
                                try {
                                    window._n("Igj")
                                } catch (o) { }
                                var r = !1;
                                window.__app_config__ && window.__app_config__.useNewIMSDK && (r = !0);
                                try {
                                    r ? IMClient.sendMessageInSocket(e, function (e) {
                                        try {
                                            window._n("Hyo")
                                        } catch (n) { }
                                        var o = {
                                            localId: e.localId,
                                            createTime: e.msgCreateTime,
                                            messageId: e.msgId
                                        };
                                        t(o)
                                    }, function (e) {
                                        try {
                                            window._n("sxO")
                                        } catch (t) { }
                                        (0,
                                            C.y)(e) && v.Z.logout(e),
                                            n(e)
                                    }) : IMClient.sendMessage(e, function (e) {
                                        try {
                                            window._n("GE2")
                                        } catch (n) { }
                                        if (0 != e.msgId) {
                                            var o = {
                                                localId: e.localId,
                                                createTime: e.msgCreateTime,
                                                messageId: e.msgId
                                            };
                                            t(o)
                                        }
                                    }, function (e) {
                                        try {
                                            window._n("XLi")
                                        } catch (t) { }
                                        (0,
                                            C.y)(e) && v.Z.logout(e),
                                            n(e)
                                    })
                                } catch (i) {
                                    n(i)
                                }
                            }
                            )
                    }
                }, {
                    key: "sendAIMessage",
                    value: function (e, t, n) {
                        try {
                            window._n("wcj")
                        } catch (o) { }
                        if (e.threadId = t.threadId || "",
                            e.initiator = 2,
                            t.ext && t.ext.profile) {
                            var r = JSON.parse(e.profile);
                            r || (r = {});
                            try {
                                r = Object.assign(r, JSON.parse(t.ext.profile))
                            } catch (n) { }
                            e.profile = JSON.stringify(r)
                        }
                        return (0,
                            h.Z)(a.Z.UBTTypeEnum.SEND_MESSAGE, {
                                fromway: "ai_message",
                                messages: e
                            }),
                            c.Z.sendAIMessage.post(e).then(function (e) {
                                try {
                                    window._n("lf9")
                                } catch (n) { }
                                var o = {};
                                if (e && e.ResponseStatus && e.ResponseStatus.Ack && "success" == e.ResponseStatus.Ack.toLowerCase() && 0 != e.sessionId)
                                    e.sessionId != t.sessionId && d.Z.publish(a.Z.EventEnum.NEW_CONVERSATION_EVENT, [[T(T({}, t), {}, {
                                        sessionId: e.sessionId
                                    })]]),
                                        o = {
                                            sessionId: e.sessionId,
                                            localId: e.localId,
                                            createTime: e.msgCreateTime,
                                            messageId: e.msgId
                                        };
                                else {
                                    if (e.ResponseStatus && e.ResponseStatus.Errors && e.ResponseStatus.Errors[0]) {
                                        var r = e.ResponseStatus.Errors[0].ErrorCode
                                            , i = e.ResponseStatus.Errors[0].Message;
                                        v.Z.logout({
                                            errNo: r,
                                            errMsg: i
                                        })
                                    }
                                    reject(e)
                                }
                                return o
                            })
                    }
                }, {
                    key: "getRelativeQuestions",
                    value: function (e, t, n) {
                        try {
                            window._n("W5n")
                        } catch (o) { }
                        var r = f.Z.Configs.aiTipResCount(t)
                            , i = {
                                bu: {
                                    bizType: t.serviceType,
                                    pageCode: ""
                                },
                                groupId: t.gid,
                                thirdPartytoken: t.ext.thirdPartytoken,
                                platform: 3,
                                size: parseInt(r),
                                sessionId: t.sessionId,
                                text: e
                            };
                        return c.Z.getRelativeQuestions.post(i).then(function (e) {
                            try {
                                window._n("ATJ")
                            } catch (t) { }
                            return e
                        })
                    }
                }, {
                    key: "switchToHuman",
                    value: function (t, n, o) {
                        try {
                            window._n("Yep")
                        } catch (r) { }
                        var i = e._buildAIMessageCommonAttrs(t, n);
                        return i.body.ext.key = "Agent",
                            i.body.ext = T(T({}, i.body.ext), o || {}),
                            i.body.title = u.Z.get("callCtripOP", "", "转人工"),
                            i.body = JSON.stringify(i.body),
                            e.sendAIMessage(i, t, n)
                    }
                }, {
                    key: "requestAIFAQ",
                    value: function (t, n) {
                        try {
                            window._n("IlU")
                        } catch (o) { }
                        var r = e._buildAIMessageCommonAttrs(t, n);
                        r.body.ext.key = "FAQ",
                            r.body = JSON.stringify(r.body),
                            e.sendAIMessage(r, t, n)
                    }
                }, {
                    key: "requestAIMessage",
                    value: function (t, n, o) {
                        try {
                            window._n("sP4")
                        } catch (r) { }
                        var i = e._buildAIMessageCommonAttrs(t, n);
                        i.body.ext.key = o.aiKey || "AI",
                            i.body.title = o.startAIText || "",
                            i.body = JSON.stringify(i.body),
                            e.sendAIMessage(i, t, n)
                    }
                }, {
                    key: "commentChartMessage",
                    value: function (e, t) {
                        try {
                            window._n("VBn")
                        } catch (n) { }
                        return c.Z.commentChatMessage.post({
                            messageId: e.messageId,
                            mode: t
                        })
                    }
                }, {
                    key: "evaluateRobotMessage",
                    value: function (e, t) {
                        try {
                            window._n("ity")
                        } catch (n) { }
                        return c.Z.evaluateRobotMessage.post({
                            messageId: e.messageId,
                            groupId: e.fromGid,
                            bizType: e.bizType,
                            source: "online",
                            Messagebody: e.messageBody,
                            evaluation: 1 == t ? "GOOD" : "BAD"
                        })
                    }
                }, {
                    key: "_buildMessageCommonAttrs",
                    value: function (e, t, n) {
                        try {
                            window._n("p3i")
                        } catch (o) { }
                        return n.fromUid = t,
                            n.conversationType = e.chatType || a.Z.CommonEnum.ConversationType.GroupChat,
                            n.toJid = e.gid,
                            n.createTime = new Date().getTime() + parseInt(window.__server_client_timediff__ || 0),
                            n.bizType = parseInt(e.serviceType || e.bizType),
                            n.threadId = e.threadId,
                            n.localId = (0,
                                l.Z)(),
                            n.fromGid = e.gid,
                            n.currentConversation = e.conversationKey,
                            n
                    }
                }, {
                    key: "_buildAIMessageCommonAttrs",
                    value: function (e, t) {
                        var n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
                        try {
                            window._n("Cti")
                        } catch (o) { }
                        var r = (n.options || {}).questionid || ""
                            , i = n.aiToken || (e.ext || {}).aiToken || ""
                            , a = n.thirdPartytoken || (e.ext || {}).thirdPartytoken || ""
                            , c = (e.ext || {}).orderId || "";
                        i.oid && (c = i.oid);
                        var s = (e.ext || {}).visitToken || ""
                            , u = (0,
                                w.m)(window.location.href)
                            , d = {
                                Locale: u.lang && u.lang.replace(/-/gi, "_") || "zh_CN",
                                pageFrom: "IM+ Online"
                            };
                        return s && (d.visitToken = s),
                        {
                            localId: n.localId || (0,
                                l.Z)(),
                            groupId: e.gid,
                            msgType: 7,
                            gType: parseInt(e.serviceType),
                            buType: e.serviceType,
                            body: {
                                title: "",
                                action: "CBZ06",
                                ext: {
                                    uid: t.ctripUid,
                                    questionid: r,
                                    aitoken: i,
                                    thirdpartytoken: a,
                                    orderId: c
                                }
                            },
                            resource: "Web",
                            source: "online",
                            profile: JSON.stringify(d)
                        }
                    }
                }, {
                    key: "createTextMessage",
                    value: function (t, n, o) {
                        try {
                            window._n("so6")
                        } catch (r) { }
                        var i = new MessageFactory.TextMessage;
                        return i.content = o,
                            i = e._buildMessageCommonAttrs(t, n, i)
                    }
                }, {
                    key: "createImgMessage",
                    value: function (t, n, o) {
                        try {
                            window._n("qJF")
                        } catch (r) { }
                        var i = new MessageFactory.ImageMessage;
                        return i.imageUrl = o,
                            i.thumbUrl = o,
                            i.width = 130,
                            i.height = 130,
                            i = e._buildMessageCommonAttrs(t, n, i)
                    }
                }, {
                    key: "createFileMessage",
                    value: function (t, n, o) {
                        try {
                            window._n("45l")
                        } catch (r) { }
                        var i = new MessageFactory.FileMessage;
                        return i.size = o.size,
                            i.filename = o.name,
                            i.fileType = o.type,
                            i.file = o,
                            i = e._buildMessageCommonAttrs(t, n, i)
                    }
                }, {
                    key: "createVideoMessage",
                    value: function (t, n, o) {
                        try {
                            window._n("BlD")
                        } catch (r) { }
                        var i = new MessageFactory.VideoMessage;
                        return i.size = o.size,
                            i.filename = o.name,
                            i.fileType = o.type,
                            i.file = o,
                            i = e._buildMessageCommonAttrs(t, n, i)
                    }
                }, {
                    key: "createYouYouMessage",
                    value: function (t, n, o) {
                        try {
                            window._n("5eu")
                        } catch (r) { }
                        var i = new MessageFactory.CustomContentMessage;
                        return i.extPropertys = {
                            emotionType: "youyou",
                            emotionDes: o.cName,
                            emotionName: o.emojiName
                        },
                            i.actionCode = "CBZ05",
                            i.title = o.alt,
                            i = e._buildMessageCommonAttrs(t, n, i)
                    }
                }, {
                    key: "createQuestionMessage",
                    value: function (t, n, o) {
                        try {
                            window._n("gB1")
                        } catch (r) { }
                        var i = new MessageFactory.CustomContentMessage;
                        return i.extPropertys = o.ext,
                            i.actionCode = o.action,
                            i.title = o.title,
                            i.see = o.see,
                            i.bSee = o.bSee,
                            i.sid = o.sid,
                            i = e._buildMessageCommonAttrs(t, n, i)
                    }
                }, {
                    key: "createShoppingMessage",
                    value: function (t, n, o) {
                        try {
                            window._n("teq")
                        } catch (r) { }
                        var i = new MessageFactory.CustomContentMessage;
                        return i.extPropertys = o,
                            i.title = "商品卡片\n当前版本不支持此消息类型，请升级APP至新版查看",
                            i.actionCode = "CBZ58",
                            i = e._buildMessageCommonAttrs(t, n, i)
                    }
                }, {
                    key: "createContentMessage",
                    value: function (t, n, o) {
                        try {
                            window._n("v0C")
                        } catch (r) { }
                        var i = new MessageFactory.CustomContentMessage;
                        return i.extPropertys = o,
                            i.title = o.desc + " " + o.appUrl,
                            i.actionCode = "CBZ59",
                            i = e._buildMessageCommonAttrs(t, n, i)
                    }
                }, {
                    key: "createCardMessage",
                    value: function (t, n) {
                        try {
                            window._n("LWb")
                        } catch (o) { }
                        return c.Z.getDLTOrderInfo.post(t.ext).then(function (o) {
                            try {
                                window._n("3uO")
                            } catch (r) { }
                            var i = (o.order || {}).data;
                            if (i) {
                                var a = {};
                                return (a = new MessageFactory.CustomContentMessage).extPropertys = {
                                    productUrl: "",
                                    productNum: i.roomNum,
                                    useDate: "".concat(p()(i.checkInDate).format("YYYY-MM-DD"), " ~ ").concat(p()(i.checkoutDate).format("YYYY-MM-DD"), " <br /> ").concat(i.roomName, " <br /> ").concat(i.stayQuantity, " 晚 / ").concat(i.roomNum, "间房  "),
                                    total: "".concat(i.currency).concat(i.cost),
                                    discription: i.roomName,
                                    title: "".concat(i.hotelName),
                                    price: i.cost,
                                    bizTypeCN: i.formType || "",
                                    orderId: t.ext.orderId || "",
                                    expireDate: "",
                                    bizType: t.serviceType,
                                    jumpUrl: i.jumpUrl,
                                    currency: i.currency,
                                    status: i.orderStatusDes,
                                    opJumpUrl: i.jumpUrl
                                },
                                    a.actionCode = "CBZ30",
                                    a.title = "[".concat(u.Z.get("orderIdMain", "", "订单"), "]"),
                                    a = e._buildMessageCommonAttrs(t, n.uid, a)
                            }
                        })
                    }
                }, {
                    key: "createCardMessageFromURL",
                    value: function (t, n, o) {
                        try {
                            window._n("5Gn")
                        } catch (r) { }
                        return c.Z.getCardMessageFromURL.post({
                            groupJid: n.gid,
                            url: t
                        }).then(function (r) {
                            try {
                                window._n("0wO")
                            } catch (i) { }
                            var a = r.cardMessageInfo
                                , c = new MessageFactory.CardMessage;
                            return c.content = a.content,
                                c.avatar = a.imageUrl,
                                c.title = a.title,
                                c.desc = a.content,
                                c.linkUrl = t,
                                c = e._buildMessageCommonAttrs(n, o.uid, c)
                        })
                    }
                }, {
                    key: "getMessageImageList",
                    value: function (e, t, n) {
                        try {
                            window._n("OPC")
                        } catch (o) { }
                        for (var r = [], i = -1, c = 0; c < e.length; c++)
                            if (e[c].localId && e[c].localId == t.localId || e[c].messageId && e[c].messageId == t.messageId) {
                                if (e[c].messageType == a.Z.MessageType.ImageMessage)
                                    i = r.length,
                                        r.push(e[c].imageUrl);
                                else if (e[c].messageType == a.Z.MessageType.CustomContentMessage) {
                                    if ("CBZ04" == e[c].actionCode || "CBZ32" == e[c].actionCode || "CBZ10" == e[c].actionCode)
                                        for (var s = (e[c].extPropertys || e[c].ext || {}).decorates || [], u = 0; u < s.length; u++) {
                                            var d = s[u] || {}
                                                , l = d.attrs || {}
                                                , m = d.tag || "";
                                            l.src === n && (i = r.length),
                                                "img" == m && r.push(l.src)
                                        }
                                    else if ("CBZ47" == e[c].actionCode)
                                        for (var y = (e[c].extPropertys || e[c].ext || {}).imgList || [], h = 0; h < y.length; h++)
                                            y[h] == n && (i = r.length,
                                                r.push(y[h]))
                                }
                            } else if (e[c].messageType == a.Z.MessageType.ImageMessage)
                                r.push(e[c].imageUrl);
                            else if (e[c].messageType == a.Z.MessageType.CustomContentMessage && ("CBZ04" == e[c].actionCode || "CBZ32" == e[c].actionCode || "CBZ10" == e[c].actionCode))
                                for (var _ = (e[c].extPropertys || e[c].ext || {}).decorates || [], f = 0; f < _.length; f++) {
                                    var g = _[f] || {}
                                        , p = g.attrs || {};
                                    "img" == (g.tag || "") && r.push(p.src)
                                }
                        return {
                            imgList: r,
                            cIdx: i,
                            cUrl: n
                        }
                    }
                }, {
                    key: "getMessageTransationResult",
                    value: function (e, t, n) {
                        try {
                            window._n("aFg")
                        } catch (o) { }
                        var r = (navigator.language || "").split("-")[0].toUpperCase();
                        return c.Z.getMessageTranslate.post({
                            sessionId: e.sessionId,
                            groupId: e.gid,
                            messageId: t.messageId,
                            messageTxt: n,
                            target: r
                        })
                    }
                }, {
                    key: "recallMessage",
                    value: function (e) {
                        try {
                            window._n("WF4")
                        } catch (t) { }
                        return c.Z.RecallMessageIM.post({
                            messageId: e.messageId,
                            partnerId: e.fromGid || e.toJid,
                            recallerId: e.fromUid
                        })
                    }
                }, {
                    key: "recallMessageByAdmin",
                    value: function (e) {
                        try {
                            window._n("l1n")
                        } catch (t) { }
                        return c.Z.RecallMessageByAdmin.post({
                            messageId: e.messageId,
                            groupId: e.fromGid || e.toJid,
                            bizType: e.bizType
                        })
                    }
                }, {
                    key: "messageIsFromCustomer",
                    value: function (e, t) {
                        try {
                            window._n("9VO")
                        } catch (n) { }
                        var o = (t.uid || "").toLowerCase()
                            , r = (e.fromUid || "").toLowerCase();
                        return "" !== o && "" !== r && o == r
                    }
                }, {
                    key: "isAIMessage",
                    value: function (e) {
                        try {
                            window._n("Ub7")
                        } catch (t) { }
                        return !!e && "CBZ04" == e.actionCode
                    }
                }, {
                    key: "isAskAIMessage",
                    value: function (e) {
                        try {
                            window._n("wMu")
                        } catch (t) { }
                        return !!e && "CBZ06" == e.actionCode
                    }
                }]),
                e
        }();
        t.Z = E
    },
    34719: function (e, t, n) {
        n.d(t, {
            Z: function () {
                return c
            }
        });
        var o = n(11025)
            , r = n(74213)
            , i = n(99797);
        n(13485);
        var a = null
            , c = function () {
                function e() {
                    (0,
                        o.Z)(this, e)
                }
                return (0,
                    r.Z)(e, null, [{
                        key: "newMessageVoiceUrl",
                        value: function (e) {
                            try {
                                window._n("Wib")
                            } catch (t) { }
                            if (1 == e)
                                return i.getStaticPath("/webresource/voice/B01.wav");
                            if (2 == e)
                                return i.getStaticPath("/webresource/voice/B02.wav");
                            if (3 == e)
                                return i.getStaticPath("/webresource/voice/B03.wav");
                            if (4 == e)
                                return i.getStaticPath("/webresource/voice/B04.wav");
                            if (5 == e)
                                return i.getStaticPath("/webresource/voice/C01.wav");
                            if (6 == e)
                                return i.getStaticPath("/webresource/voice/C02.wav");
                            if (7 == e)
                                return i.getStaticPath("/webresource/voice/C03.wav");
                            else if (8 == e)
                                return i.getStaticPath("/webresource/voice/C04.wav");
                            else if (9 == e)
                                return i.getStaticPath("/webresource/voice/C05.wav");
                            else if (10 == e)
                                return i.getStaticPath("/webresource/voice/C06.wav");
                            else if (11 == e)
                                return i.getStaticPath("/webresource/voice/C07.wav");
                            else if (12 == e)
                                return i.getStaticPath("/webresource/voice/C08.wav");
                            else
                                return i.getStaticPath("/webresource/voice/msgSound.wav")
                        }
                    }, {
                        key: "playNewConversationTone",
                        value: function () {
                            try {
                                window._n("8jl")
                            } catch (t) { }
                            var n = i.getStaticPath("/webresource/voice/newconversation.mp3");
                            e.playVoice(n)
                        }
                    }, {
                        key: "playNewMsgTone",
                        value: function (t) {
                            try {
                                window._n("cN6")
                            } catch (n) { }
                            var o = (t.extPropertys || t.ext || {}).type || -1
                                , r = e.newMessageVoiceUrl(o);
                            e.playVoice(r)
                        }
                    }, {
                        key: "playVoice",
                        value: function (e) {
                            try {
                                window._n("GpP")
                            } catch (t) { }
                            a || ((a = document.createElement("audio")).setAttribute("preload", "auto"),
                                a.loop = !1,
                                a.autoplay = !0,
                                document.body.appendChild(a)),
                                a.paused ? a.src = e : (a.pause(),
                                    a.src != e && (a.src = e))
                        }
                    }, {
                        key: "stopNewMsgTone",
                        value: function () {
                            try {
                                window._n("sRR")
                            } catch (e) { }
                            a.pause()
                        }
                    }]),
                    e
            }()
    },
    89312: function (e, t, n) {
        n.d(t, {
            Z: function () {
                return i
            }
        });
        var o = n(11025)
            , r = n(74213);
        n(13485);
        var i = function () {
            function e(t, n) {
                try {
                    window._n("frL")
                } catch (r) { }
                (0,
                    o.Z)(this, e),
                    this.step = t,
                    this.stepLen = n,
                    this.queue = [],
                    this.running = !1
            }
            return (0,
                r.Z)(e, [{
                    key: "enqueue",
                    value: function (e) {
                        try {
                            window._n("V5i")
                        } catch (t) { }
                        this.queue = [].concat(this.queue, e),
                            this.dequeue()
                    }
                }, {
                    key: "dequeue",
                    value: function () {
                        var e = this;
                        try {
                            window._n("hF7")
                        } catch (t) { }
                        if (!this.running) {
                            this.running = !0;
                            var n = this.queue.splice(0, this.stepLen);
                            n.length > 0 ? this.step(n).then(function (t) {
                                try {
                                    window._n("KjM")
                                } catch (n) { }
                                setTimeout(function () {
                                    try {
                                        window._n("VVc")
                                    } catch (t) { }
                                    e.running = !1,
                                        e.dequeue()
                                }, 1)
                            }).catch(function (t) {
                                try {
                                    window._n("CmA")
                                } catch (n) { }
                                setTimeout(function () {
                                    try {
                                        window._n("l6j")
                                    } catch (t) { }
                                    e.running = !1,
                                        e.dequeue()
                                }, 1)
                            }) : this.running = !1
                        }
                    }
                }]),
                e
        }()
    },
    11549: function (e, t, n) {
        n(13485),
            t.Z = function () {
                try {
                    window._n("NmK")
                } catch (e) { }
                function t() {
                    try {
                        window._n("tQc")
                    } catch (e) { }
                    return ((1 + Math.random()) * 65536 | 0).toString(16).substring(4)
                }
                return function () {
                    try {
                        window._n("LHK")
                    } catch (e) { }
                    for (var n = "", o = 1; o <= 20; o++)
                        n += t(),
                            (8 === o || 12 === o || 16 === o || 20 === o) && (n += "-");
                    var r = parseInt(8 * Math.random())
                        , i = new Date().getTime() + "";
                    n += i.slice(0, r);
                    for (var a = 0; a < 4; a++)
                        n += t();
                    return n + i.slice(r + 5, 13)
                }()
            }
    },
    95514: function (e, t, n) {
        n(13485),
            t.Z = function (e) {
                try {
                    window._n("Yvd")
                } catch (t) { }
                var n = location.host;
                return n.match(/\.ctrip\.com/i) ? location.pathname.match(/messageList/i) ? 6 : 4 : n.match(/\.uat\.qa/i) ? 2 : n.match(/(\.fws|\.fat|\.lpt|localhost|172\.16|127\.0)/i) ? location.pathname.match(/messageList/i) ? 5 : 0 : 4
            }
    },
    17587: function (e, t, n) {
        var o = n(89312)
            , r = n(2037)
            , i = n(7315);
        n(13485);
        var a = {
            AsyncQueue: o.Z,
            MessageCenter: r.Z,
            LogUBT: i.Z,
            HTMLUtil: {
                escape: function () {
                    var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "";
                    try {
                        window._n("NTr")
                    } catch (t) { }
                    return e.replace(/</gi, "&lt;").replace(/>/, "&gt;").replace(/\n/gi, "<br/>")
                },
                getElemPos: function (e) {
                    try {
                        window._n("0Jm")
                    } catch (t) { }
                    var n = {
                        top: 0,
                        left: 0
                    };
                    if (e.offsetParent)
                        for (; e.offsetParent;)
                            n.top += e.offsetTop,
                                n.left += e.offsetLeft,
                                e = e.offsetParent;
                    else
                        e.x ? n.left += e.x : e.x && (n.top += e.y);
                    return {
                        x: n.left,
                        y: n.top
                    }
                },
                getOffset: function (e, t) {
                    try {
                        window._n("QzS")
                    } catch (n) { }
                    var o = e.offsetTop
                        , r = e.offsetLeft
                        , i = 0;
                    for (e = e.offsetParent; e && "new-invite-pop" !== e.className;)
                        o += e.offsetTop,
                            r += e.offsetLeft,
                            i += e.scrollTop,
                            e = e.offsetParent;
                    return {
                        top: o - i + 20,
                        left: r + 20
                    }
                }
            }
        };
        t.Z = a
    },
    60414: function (e, t, n) {
        n.d(t, {
            Z: function () {
                return c
            }
        });
        var o = n(11025)
            , r = n(74213)
            , i = n(92307);
        n(13485);
        var a = new i.default
            , c = function () {
                function e() {
                    (0,
                        o.Z)(this, e)
                }
                return (0,
                    r.Z)(e, null, [{
                        key: "get",
                        value: function (e, t, n) {
                            try {
                                window._n("fhQ")
                            } catch (o) { }
                            return a.get(e, 100017720) ? a.get(e, 100017720) : (window.i18n_100017720 || {})[e] || n
                        }
                    }, {
                        key: "getClientResUrl",
                        value: function (e) {
                            try {
                                window._n("41z")
                            } catch (t) { }
                            return "undefined" != typeof sharkjs && (sharkjs.get(e, 100017720) || sharkjs.get("zh-CN", 100017720)) || ""
                        }
                    }]),
                    e
            }()
    },
    7315: function (e, t, n) {
        var o = n(47813)
            , r = n(32336)
            , i = n(61023)
            , a = n(36652)
            , c = n.n(a);
        function s(e, t) {
            var n = Object.keys(e);
            if (Object.getOwnPropertySymbols) {
                var o = Object.getOwnPropertySymbols(e);
                t && (o = o.filter(function (t) {
                    return Object.getOwnPropertyDescriptor(e, t).enumerable
                })),
                    n.push.apply(n, o)
            }
            return n
        }
        function u(e) {
            for (var t = 1; t < arguments.length; t++) {
                var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? s(Object(n), !0).forEach(function (t) {
                    (0,
                        o.Z)(e, t, n[t])
                }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : s(Object(n)).forEach(function (t) {
                    Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                })
            }
            return e
        }
        function d(e) {
            var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 25600;
            try {
                window._n("djx")
            } catch (n) { }
            return e.length > t && (e = e.substring(0, t)),
                e
        }
        n(13485),
            t.Z = function (e, t) {
                var n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : "";
                try {
                    window._n("mLx")
                } catch (o) { }
                try {
                    var s = t || {};
                    "string" == typeof s && (s = {
                        msg: s
                    }),
                        "undefined" != typeof __im_current_user__ && (s.vendor = {
                            uid: __im_current_user__.ctripUid,
                            name: __im_current_user__.nickname || __im_current_user__.nickname
                        },
                            s.loginName = __im_current_user__.ctripUid),
                        "string" != typeof t && (s = JSON.stringify(t));
                    var l = i.Z.devUBTTypeEnum[e]
                        , m = JSON.stringify(s);
                    setTimeout(function () {
                        try {
                            window._n("JJU")
                        } catch (t) { }
                        if (l) {
                            !function (e, t) {
                                var n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
                                try {
                                    window._n("dKN")
                                } catch (o) { }
                                try {
                                    var r = d(JSON.stringify(u({
                                        time: Date.now(),
                                        name: e,
                                        data: t
                                    }, n)));
                                    c().send({
                                        type: a.UBT_TYPE.DEV_TRACE,
                                        key: e,
                                        data: r
                                    }, window.pvId || "")
                                } catch (i) { }
                            }(l, m);
                            return
                        }
                        !function (e, t) {
                            var n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
                            try {
                                window._n("in6")
                            } catch (o) { }
                            try {
                                var r = d(JSON.stringify(u({
                                    time: Date.now(),
                                    name: e,
                                    data: t
                                }, n)));
                                c().send({
                                    type: a.UBT_TYPE.TRACE,
                                    key: e,
                                    data: r
                                }, window.pvId || "")
                            } catch (i) { }
                        }(e, m)
                    }, 0);
                    var y = new r.Z;
                    if (l) {
                        y.send({
                            type: l,
                            type_desc: l,
                            status: "fail",
                            value_desc: m
                        });
                        return
                    }
                    if (n) {
                        var h = function (e) {
                            try {
                                window._n("S0Q")
                            } catch (t) { }
                            var n = e.err
                                , o = n.code
                                , r = n.message
                                , i = n.response
                                , a = void 0 === i ? {} : i
                                , c = "API_ERROR"
                                , s = "";
                            return o ? (c = "ECONNABORTED" === o ? r.startsWith("timeout") ? "ETIMEDOUT" : o : ("ERR_BAD_REQUEST" === o || "ERR_BAD_RESPONSE" === o) && a.status >= 400 && a.status < 600 ? a.status : o,
                                s = r) : (a && a.status && (c = a.status),
                                    s = r),
                            {
                                httpErrCode: c,
                                httpErrMsg: s
                            }
                        }(t)
                            , _ = h.httpErrCode
                            , f = h.httpErrMsg;
                        y.send({
                            type: i.Z.LogTypeEnum.ajax_status,
                            type_desc: t.params && t.params.url || "",
                            sub_type: "network error",
                            sub_type_desc: t.desc,
                            status: "fail",
                            httpErrCode: _,
                            httpErrMsg: f
                        })
                    } else {
                        var g = t.err;
                        e == i.Z.UBTTypeEnum.AJAX_ERROR && "9999" != (g || {}).errNo && y.send({
                            type: i.Z.LogTypeEnum.ajax_status,
                            type_desc: t.params && t.params.url || "",
                            sub_type: "response error",
                            sub_type_desc: t.desc,
                            status: "fail",
                            value_desc: (g || {}).errNo,
                            errCode: (g || {}).errNo,
                            errMsg: (g || {}).errMsg
                        })
                    }
                } catch (p) { }
            }
    },
    2037: function (e, t, n) {
        var o = n(11025)
            , r = n(74213)
            , i = n(47813)
            , a = n(11549);
        n(13485);
        var c = function () {
            function e() {
                (0,
                    o.Z)(this, e)
            }
            return (0,
                r.Z)(e, null, [{
                    key: "subscribe",
                    value: function (t, n, o) {
                        try {
                            window._n("p6R")
                        } catch (r) { }
                        var i = (0,
                            a.Z)();
                        return t && n && (e.messageQueue[t] || (e.messageQueue[t] = []),
                            e.messageQueue[t].push({
                                uuid: i,
                                scope: o,
                                handler: n
                            })),
                            i
                    }
                }, {
                    key: "unsubscribe",
                    value: function (t) {
                        try {
                            window._n("Xm5")
                        } catch (n) { }
                        e.messageQueue[t] && delete e.messageQueue[t]
                    }
                }, {
                    key: "removeListener",
                    value: function (t, n) {
                        try {
                            window._n("QOK")
                        } catch (o) { }
                        var r = e.messageQueue[t];
                        if (r)
                            for (var i = 0; i < r.length;)
                                r[i].uuid === n && r.splice(i, 1),
                                    i++;
                        e.messageQueue[t] = r
                    }
                }, {
                    key: "getEventEmitter",
                    value: function (t) {
                        try {
                            window._n("HZ7")
                        } catch (n) { }
                        return {
                            eventName: t,
                            eventHandler: e.messageQueue[t]
                        }
                    }
                }, {
                    key: "publish",
                    value: function (t, n) {
                        try {
                            window._n("opX")
                        } catch (o) { }
                        if (e.messageQueue[t]) {
                            for (var r = 0; r < e.messageQueue[t].length; r++) {
                                var i = e.messageQueue[t][r];
                                i.handler && i.handler.apply(i.scope ? i.scope : window, n)
                            }
                            return e.messageQueue[t].length
                        }
                        return 0
                    }
                }]),
                e
        }();
        (0,
            i.Z)(c, "messageQueue", {}),
            t.Z = c
    },
    80590: function (e, t, n) {
        n.d(t, {
            L: function () {
                return a
            }
        });
        var o = n(36652)
            , r = n.n(o)
            , i = n(95514);
        n(13485);
        var a = function (e) {
            try {
                window._n("rFP")
            } catch (t) { }
            if (e)
                try {
                    c(),
                        window.pvId = r().register({
                            pageId: e,
                            appId: 700163,
                            business: {},
                            settings: {}
                        })
                } catch (n) { }
        }
            , c = function () {
                try {
                    window._n("K20")
                } catch (e) { }
                var t = [4, 6].includes((0,
                    i.Z)());
                window.UBT_BIZCONFIG = {
                    isLoadMarketing: !1,
                    isLoadRMS: !1,
                    isLoadFP: !1,
                    isLAN: !1,
                    isProd: t
                }
            }
    },
    53122: function (e, t, n) {
        var o = n(11025)
            , r = n(74213);
        n(13485);
        var i = !1
            , a = null
            , c = null
            , s = !1
            , u = !1
            , d = null
            , l = ""
            , m = 0
            , y = function () {
                try {
                    window._n("w2E")
                } catch (e) { }
                c && clearInterval(c),
                    document.title = l
            }
            , h = function () {
                try {
                    window._n("xxB")
                } catch (e) { }
                s = !1,
                    y()
            }
            , _ = function () {
                try {
                    window._n("MPE")
                } catch (e) { }
                s = !0,
                    d && f.blinkBrowserTitle(d)
            };
        i = "Notification" in window,
            "onfocusin" in document ? (document.onfocusin = h,
                document.onfocusout = _) : (window.onblur = _,
                    window.onfocus = h),
            document.addEventListener("visibilitychange", function () {
                try {
                    window._n("WCD")
                } catch (e) { }
                u = document.hidden
            }),
            l = document.title;
        var f = function () {
            function e() {
                (0,
                    o.Z)(this, e)
            }
            return (0,
                r.Z)(e, null, [{
                    key: "getBrowserStatus",
                    value: function () {
                        try {
                            window._n("jzF")
                        } catch (e) { }
                        return {
                            isBrowserBlur: s,
                            isBrowserMinimum: u
                        }
                    }
                }, {
                    key: "blinkBrowserTitle",
                    value: function (e) {
                        try {
                            window._n("6Uw")
                        } catch (t) { }
                        s && (d = null,
                            y(),
                            m = 0,
                            c = setInterval(function () {
                                try {
                                    window._n("KE5")
                                } catch (t) { }
                                var n = document.title;
                                document.title = n == l ? e : l,
                                    ++m > 300 && y()
                            }, 1e3))
                    }
                }, {
                    key: "isShouldShowDesktopNotification",
                    value: function () {
                        try {
                            window._n("mYm")
                        } catch (e) { }
                        return i && "granted" === Notification.permission && (s || u)
                    }
                }, {
                    key: "requestDesktopNotificationPermission",
                    value: function () {
                        try {
                            window._n("alG")
                        } catch (e) { }
                        return i ? Notification.requestPermission() : Promise.reject("denied")
                    }
                }, {
                    key: "desktopNotification",
                    value: function (e) {
                        try {
                            window._n("mC2")
                        } catch (t) { }
                        return !!i && "granted" === Notification.permission && (a && a.close(),
                            (a = new Notification(e.title, {
                                icon: "/static/img/favicon.ico",
                                body: e.content || ""
                            })).onclick = function (e) {
                                try {
                                    window._n("7a4")
                                } catch (t) { }
                                e.preventDefault(),
                                    window.focus()
                            }
                            ,
                            !0)
                    }
                }, {
                    key: "createImgURL",
                    value: function (e, t) {
                        try {
                            window._n("SZw")
                        } catch (n) { }
                        var o = new FileReader;
                        o.onload = function (n) {
                            try {
                                window._n("Jfw")
                            } catch (o) { }
                            t(navigator.userAgent.indexOf("MSIE 10.0") > 0 ? window.URL.createObjectURL(e) : (window.URL || window.webkitURL).createObjectURL(e), n.target.result)
                        }
                            ,
                            o.readAsDataURL(e),
                            e.value = ""
                    }
                }, {
                    key: "isOPPO",
                    value: function () {
                        try {
                            window._n("3vm")
                        } catch (e) { }
                        return navigator.userAgent.toLowerCase().indexOf("oppobrowser") > 0
                    }
                }, {
                    key: "isWechat",
                    value: function () {
                        try {
                            window._n("W1Y")
                        } catch (e) { }
                        return navigator.userAgent.toLowerCase().indexOf("micromessenger") > 0
                    }
                }, {
                    key: "isMiniProgram",
                    value: function () {
                        try {
                            window._n("H43")
                        } catch (e) { }
                        return wx && wx.miniProgram.getEnv(),
                            !0
                    }
                }, {
                    key: "jumpPage",
                    value: function (e, t) {
                        try {
                            window._n("9Cl")
                        } catch (n) { }
                        this.isMiniProgram() && wx && wx.miniProgram ? wx.miniProgram.navigateTo({
                            url: t
                        }) : location.href = e
                    }
                }, {
                    key: "setTitle",
                    value: function (e) {
                        try {
                            window._n("AgM")
                        } catch (t) { }
                        if (!this.isMiniProgram())
                            return !1;
                        window.ensureRun(function () {
                            try {
                                window._n("nvc")
                            } catch (t) { }
                            wx && wx.miniProgram && wx.miniProgram.setNavigationBarTitle({
                                title: e
                            })
                        })
                    }
                }]),
                e
        }();
        t.Z = f
    },
    95677: function (e, t, n) {
        var o = n(11025)
            , r = n(74213)
            , i = n(61023);
        n(13485);
        var a = function () {
            function e() {
                (0,
                    o.Z)(this, e)
            }
            return (0,
                r.Z)(e, null, [{
                    key: "getEmojiCode",
                    value: function (e) {
                        try {
                            window._n("7tr")
                        } catch (t) { }
                        var n = null;
                        return i.Z.CommonEnum.EmojiMapCode.some(function (t) {
                            try {
                                window._n("CEh")
                            } catch (o) { }
                            return (t.cn === e || t.en === e) && (n = t.code,
                                !0)
                        }),
                            n
                    }
                }, {
                    key: "replacerTextEmoji",
                    value: function (e) {
                        try {
                            window._n("mXh")
                        } catch (t) { }
                        var n = this.getEmojiCode(e);
                        return n ? '<img class="emoji" alt="'.concat(e, '" style="width:20px; height: 20px;" src="//webresource.c-ctrip.com/ares2/basebiz.im/agent-site-static/^2.3.55/default/reactVersion/img/im-client/emoji/').concat(n, '@2x.png"/>') : e
                    }
                }, {
                    key: "parse",
                    value: function (e) {
                        try {
                            window._n("IEa")
                        } catch (t) { }
                        return "undefined" != typeof twemoji && (e = twemoji.parse(e)),
                            e.replace(/\[(?:(?![\[\]])[\s\S])+\]/g, this.replacerTextEmoji.bind(this))
                    }
                }]),
                e
        }();
        t.Z = a
    },
    57770: function (e, t, n) {
        n.d(t, {
            m: function () {
                return o
            }
        }),
            n(13485);
        var o = function (e) {
            try {
                window._n("wrV")
            } catch (t) { }
            var n = {}
                , o = e.lastIndexOf("?")
                , r = e.substr(o + 1);
            if ("" == r || -1 == o)
                return {};
            for (var i = r.split("&"), a = 0; a < i.length; a++) {
                var c = i[a].split("=");
                try {
                    n[decodeURIComponent(c[0])] = decodeURIComponent(c[1] || "")
                } catch (e) { }
            }
            return n
        }
    },
    46246: function (e, t, n) {
        n.d(t, {
            y: function () {
                return o
            }
        }),
            n(13485);
        var o = function (e) {
            try {
                window._n("NAe")
            } catch (t) { }
            var n = e || {}
                , o = n.errNo
                , r = n.errMsg
                , i = n.error
                , a = n.errmsg
                , c = n.subErrorCode;
            return "MobileRequestFilterException" === o || "AccountsMobileRequestFilterException" === o && ("201" == c || "101" == c) || "error auth" === r || "MobileRequestFilterException" === i || "AccountsMobileRequestFilterException" === i || "error auth" === a
        }
    },
    74892: function (e, t, n) {
        n.d(t, {
            Z: function () {
                return f
            }
        });
        var o = n(11025)
            , r = n(74213)
            , i = n(57186)
            , a = n(35322)
            , c = n(28363)
            , s = n(39125)
            , u = n(62609)
            , d = n(47015)
            , l = n(61023)
            , m = n(48978)
            , y = n(45248)
            , h = n(60414)
            , _ = n(32336);
        n(13485);
        var f = function (e) {
            (0,
                i.Z)(s, e);
            var t, n = (t = function () {
                if ("undefined" == typeof Reflect || !Reflect.construct || Reflect.construct.sham)
                    return !1;
                if ("function" == typeof Proxy)
                    return !0;
                try {
                    return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () { })),
                        !0
                } catch (e) {
                    return !1
                }
            }(),
                function () {
                    var e, n = (0,
                        c.Z)(s);
                    if (t) {
                        var o = (0,
                            c.Z)(this).constructor;
                        e = Reflect.construct(n, arguments, o)
                    } else
                        e = n.apply(this, arguments);
                    return (0,
                        a.Z)(this, e)
                }
            );
            function s(e) {
                try {
                    window._n("Z5F")
                } catch (t) { }
                return (0,
                    o.Z)(this, s),
                    n.call(this, e)
            }
            return (0,
                r.Z)(s, [{
                    key: "auth",
                    value: function (e) {
                        var t = this
                            , n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : this.defaultLoginFail;
                        try {
                            window._n("S7c")
                        } catch (o) { }
                        var r = this.props.query
                            , i = r.miniApp
                            , a = r.isTrylogin
                            , c = r.token
                            , s = r.agentuid;
                        if (i) {
                            var y = u.i0.getData();
                            if (y && s && s == y.agentuid) {
                                d.Z.Configs.init(y),
                                    e && e(y);
                                return
                            }
                        }
                        d.Z.Agent.getAgentInfo().then(function (t) {
                            try {
                                window._n("ZVW")
                            } catch (n) { }
                            s && (t.agentuid = s),
                                u.i0.setData(t),
                                e && e(t)
                        }).catch(function (e) {
                            try {
                                window._n("dhD")
                            } catch (o) { }
                            if (i)
                                return a ? (n(e),
                                    !1) : (m.Z.login({
                                        trippalAuth: decodeURIComponent(c) || ""
                                    }, function (e) {
                                        try {
                                            window._n("EFK")
                                        } catch (o) { }
                                        if (!e) {
                                            n();
                                            return
                                        }
                                        t.thirdPartLogin(e)
                                    }, function () {
                                        try {
                                            window._n("zDJ")
                                        } catch (t) { }
                                        n(e)
                                    }),
                                        !1);
                            new _.Z().send({
                                type: l.Z.LogTypeEnum.login_status,
                                status: "fail",
                                type_desc: "string" == typeof e ? e : (e || {}).ErrorCode,
                                errCode: (e || {}).ErrorCode,
                                errMsg: (e || {}).Message
                            }),
                                n(e)
                        })
                    }
                }, {
                    key: "thirdPartLogin",
                    value: function (e) {
                        try {
                            window._n("PrO")
                        } catch (t) { }
                        var n = location.href + "&isTrylogin=1";
                        e += "&backurl=" + encodeURIComponent(n),
                            location.href = e
                    }
                }, {
                    key: "defaultLoginFail",
                    value: function (e) {
                        try {
                            window._n("xdl")
                        } catch (t) { }
                        (0,
                            y.Z)(h.Z.get("fetchUserInfoFailDesc", "", "登陆异常！请关闭聊天窗口，重新登陆后再打开聊天窗口"))
                    }
                }]),
                s
        }(s.BasePage)
    },
    45248: function (e, t, n) {
        var o = n(11025)
            , r = n(74213)
            , i = n(57186)
            , a = n(35322)
            , c = n(28363)
            , s = n(67294)
            , u = n(73935)
            , d = n(60414)
            , l = n(85893);
        n(13485);
        var m = document.createElement("div");
        document.body.appendChild(m);
        var y = function (e) {
            (0,
                i.Z)(s, e);
            var t, n = (t = function () {
                if ("undefined" == typeof Reflect || !Reflect.construct || Reflect.construct.sham)
                    return !1;
                if ("function" == typeof Proxy)
                    return !0;
                try {
                    return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () { })),
                        !0
                } catch (e) {
                    return !1
                }
            }(),
                function () {
                    var e, n = (0,
                        c.Z)(s);
                    if (t) {
                        var o = (0,
                            c.Z)(this).constructor;
                        e = Reflect.construct(n, arguments, o)
                    } else
                        e = n.apply(this, arguments);
                    return (0,
                        a.Z)(this, e)
                }
            );
            function s(e) {
                var t;
                try {
                    window._n("6GW")
                } catch (r) { }
                return (0,
                    o.Z)(this, s),
                    (t = n.call(this, e)).state = {
                        msg: "",
                        show: !1
                    },
                    t
            }
            return (0,
                r.Z)(s, [{
                    key: "show",
                    value: function (e) {
                        try {
                            window._n("NpZ")
                        } catch (t) { }
                        this.setState({
                            msg: e,
                            show: !0
                        })
                    }
                }, {
                    key: "hide",
                    value: function () {
                        try {
                            window._n("wVM")
                        } catch (e) { }
                        this.setState({
                            show: !1
                        })
                    }
                }, {
                    key: "confirm",
                    value: function () {
                        try {
                            window._n("13J")
                        } catch (e) { }
                        this.props.onConfirm && this.props.onConfirm(),
                            this.hide()
                    }
                }, {
                    key: "render",
                    value: function () {
                        var e = this;
                        try {
                            window._n("Xq9")
                        } catch (t) { }
                        return !!this.state.show && (0,
                            l.jsxs)("div", {
                                children: [(0,
                                    l.jsx)("div", {
                                        style: {
                                            position: "fixed",
                                            top: 0,
                                            right: 0,
                                            bottom: 0,
                                            left: 0,
                                            zIndex: 10,
                                            background: "rgba(0, 0, 0, 0.7)"
                                        }
                                    }), (0,
                                        l.jsxs)("div", {
                                            className: "pop absolute-center",
                                            style: {
                                                width: 250,
                                                zIndex: 11
                                            },
                                            children: [(0,
                                                l.jsxs)("div", {
                                                    className: "head",
                                                    children: [d.Z.get("hintTitle", "", "提示"), (0,
                                                        l.jsx)("a", {
                                                            href: "javascript:void(0)",
                                                            onClick: function () {
                                                                try {
                                                                    window._n("9pb")
                                                                } catch (t) { }
                                                                e.hide()
                                                            },
                                                            className: "pop_close",
                                                            children: d.Z.get("close", "", "关闭")
                                                        })]
                                                }), (0,
                                                    l.jsx)("div", {
                                                        className: "content",
                                                        children: (0,
                                                            l.jsx)("p", {
                                                                style: {
                                                                    padding: "8px"
                                                                },
                                                                children: this.state.msg
                                                            })
                                                    }), (0,
                                                        l.jsx)("div", {
                                                            className: "footer",
                                                            children: (0,
                                                                l.jsx)("button", {
                                                                    onClick: function () {
                                                                        try {
                                                                            window._n("sPd")
                                                                        } catch (t) { }
                                                                        e.confirm()
                                                                    },
                                                                    className: "btn btn-blue",
                                                                    children: d.Z.get("confirm", "", "确定")
                                                                })
                                                        })]
                                        })]
                            })
                    }
                }]),
                s
        }(s.Component);
        t.Z = function (e, t) {
            try {
                window._n("R9n")
            } catch (n) { }
            t = t || function () { }
                ,
                (0,
                    u.render)((0,
                        l.jsx)(y, {
                            onConfirm: t
                        }), m, function () {
                            try {
                                window._n("hpo")
                            } catch (t) { }
                            this.show(e)
                        })
        }
    },
    56725: function (e, t, n) {
        n.d(t, {
            Z: function () {
                return f
            }
        });
        var o = n(11025)
            , r = n(74213)
            , i = n(47813)
            , a = n(57770)
            , c = n(95514)
            , s = n(7315)
            , u = n(61023)
            , d = n(9669)
            , l = n.n(d)
            , m = n(2037);
        n(32336);
        var y = n(46246);
        function h(e, t) {
            var n = Object.keys(e);
            if (Object.getOwnPropertySymbols) {
                var o = Object.getOwnPropertySymbols(e);
                t && (o = o.filter(function (t) {
                    return Object.getOwnPropertyDescriptor(e, t).enumerable
                })),
                    n.push.apply(n, o)
            }
            return n
        }
        function _(e) {
            for (var t = 1; t < arguments.length; t++) {
                var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? h(Object(n), !0).forEach(function (t) {
                    (0,
                        i.Z)(e, t, n[t])
                }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : h(Object(n)).forEach(function (t) {
                    Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                })
            }
            return e
        }
        n(13485);
        var f = function () {
            function e() {
                try {
                    window._n("n0C")
                } catch (t) { }
                (0,
                    o.Z)(this, e),
                    (0,
                        i.Z)(this, "serverPath", "restapi/soa2/16037"),
                    (0,
                        i.Z)(this, "url", ""),
                    (0,
                        i.Z)(this, "srouce", null),
                    (0,
                        i.Z)(this, "timeout", 5e3),
                    (0,
                        i.Z)(this, "infiniteTimerId", null),
                    (0,
                        i.Z)(this, "untilEndTimerId", null),
                    (0,
                        i.Z)(this, "retryCount", 0),
                    (0,
                        i.Z)(this, "getProtocol", function () {
                            try {
                                window._n("fbP")
                            } catch (e) { }
                            if (window.location && window.location.protocol)
                                return window.location.href.includes("messageList") ? "http" : "https:" == window.location.protocol ? "https" : "file:" == window.location.protocol ? "file" : "http"
                        })
            }
            return (0,
                r.Z)(e, [{
                    key: "getwayDomain",
                    value: function (e) {
                        try {
                            window._n("Fuz")
                        } catch (t) { }
                        switch (e) {
                            case 0:
                                return "gateway.m.fws.qa.nt.ctripcorp.com";
                            case 1:
                                return "**********:8080";
                            case 2:
                                return "gateway.m.uat.qa.nt.ctripcorp.com";
                            case 3:
                                return "gateway.m.lpt.qa.nt.ctripcorp.com";
                            case 4:
                            default:
                                return "m.ctrip.com";
                            case 5:
                                return "implusreport.site.fat46.qa.nt.ctripcorp.com";
                            case 6:
                                return "implusreport.site.ctripcorp.com"
                        }
                    }
                }, {
                    key: "buildUrl",
                    value: function () {
                        try {
                            window._n("TiM")
                        } catch (e) { }
                        var t = this.getEnvCode()
                            , n = "//".concat(this.getwayDomain(t), "/").concat(this.serverPath, "/").concat(this.url);
                        "getToken" == this.url && location.host.match(/\.site\.ctripcorp\.com/i) && (n = "//".concat("implusreport.site.ctripcorp.com", "/").concat(this.serverPath, "/").concat(this.url));
                        var o = (0,
                            a.m)(location.search);
                        return o.subEnv && (n += "?subEnv=" + o.subEnv),
                            n
                    }
                }, {
                    key: "buildParams",
                    value: function (e) {
                        try {
                            window._n("m6V")
                        } catch (t) { }
                        e.head = {
                            cver: "2"
                        },
                            this.isIQ && (e.head.appId = "1001");
                        var n = (0,
                            a.m)(location.search);
                        this.isUseH5Sys && e.head && (e.head.syscode = "09");
                        var o = {
                            name: "protocal",
                            value: this.getProtocol()
                        };
                        return e.head.extension = [],
                            e.head.extension.push({
                                name: "cpc",
                                value: "pc"
                            }),
                            e.head.extension.push(o),
                            e.head.extension.push({
                                name: "amp-product-type",
                                value: "IM"
                            }),
                            e.head.extension.push({
                                name: "amp-account-source",
                                value: n && n.accountsource || ""
                            }),
                            e.head.extension.push({
                                name: "client-source",
                                value: n && n["client-source"] || ""
                            }),
                            e.head.extension.push({
                                name: "locale",
                                value: n && n.lang || "zh-CN"
                            }),
                            n.auth && (e.head.auth = n.auth),
                            e
                    }
                }, {
                    key: "cancel",
                    value: function () {
                        try {
                            window._n("wu1")
                        } catch (e) { }
                        "function" == typeof this.cancelToken && this.cancelToken("cancel request by bussiness code")
                    }
                }, {
                    key: "fetch",
                    value: function (e, t) {
                        var n = this;
                        try {
                            window._n("R7D")
                        } catch (o) { }
                        var r = {
                            method: t,
                            url: this.buildUrl(),
                            data: this.buildParams(e || {}),
                            timeout: this.timeout || 5e3,
                            cancelToken: new (l()).CancelToken(function (e) {
                                try {
                                    window._n("Uaz")
                                } catch (t) { }
                                n.cancelToken = e
                            }
                            )
                        };
                        return r.data.head.auth || (r = _(_({}, r), {}, {
                            headers: {
                                cookieOrigin: window.location.origin
                            },
                            withCredentials: !0
                        })),
                            new Promise(function (e, t) {
                                try {
                                    window._n("gEr")
                                } catch (o) { }
                                l()(r).then(function (o) {
                                    try {
                                        window._n("eXJ")
                                    } catch (i) { }
                                    var a = o.data || {};
                                    a.ResponseStatus || (a = a.data);
                                    var c = a.ResponseStatus || {}
                                        , d = a.bizResponseStatus
                                        , l = a.status;
                                    if ("Success" != c.Ack) {
                                        var h = (c.Errors || [])[0] || {}
                                            , _ = h.Message
                                            , f = h.ErrorCode
                                            , g = ((h.ErrorFields || [])[0] || {}).ErrorCode;
                                        t({
                                            errNo: f,
                                            errMsg: _,
                                            subErrorCode: g
                                        }),
                                            (0,
                                                s.Z)(u.Z.UBTTypeEnum.AJAX_ERROR, {
                                                    serverPath: n.serverPath,
                                                    url: n.url,
                                                    params: r,
                                                    desc: "ack",
                                                    err: {
                                                        errNo: f,
                                                        errMsg: _
                                                    },
                                                    responseData: JSON.stringify(a)
                                                }),
                                            (0,
                                                y.y)({
                                                    errNo: f,
                                                    errMsg: _,
                                                    subErrorCode: g
                                                }) && m.Z.publish(u.Z.EventEnum.CURRENT_USER_TOKEN_INVALID, [])
                                    } else if (d && "SUCCESS" != d.resultCode) {
                                        var p = {
                                            errNo: d.resultCode,
                                            errMsg: d.resultMessage
                                        };
                                        t(p),
                                            (0,
                                                s.Z)(u.Z.UBTTypeEnum.AJAX_ERROR, {
                                                    serverPath: n.serverPath,
                                                    url: n.url,
                                                    params: r,
                                                    desc: "bizResponseStatus",
                                                    err: p,
                                                    responseData: JSON.stringify(a)
                                                })
                                    } else if (l && "number" == typeof l.code && 0 != l.code) {
                                        var w = {
                                            errNo: l.code,
                                            errMsg: l.msg
                                        };
                                        t(w),
                                            (0,
                                                s.Z)(u.Z.UBTTypeEnum.AJAX_ERROR, {
                                                    serverPath: n.serverPath,
                                                    url: n.url,
                                                    params: r,
                                                    desc: "status",
                                                    err: w,
                                                    responseData: JSON.stringify(a)
                                                })
                                    } else
                                        e(a),
                                            n.getSAuth(c)
                                }).catch(function (e) {
                                    try {
                                        window._n("qJd")
                                    } catch (o) { }
                                    l().isCancel(e) || (t(e),
                                        (0,
                                            s.Z)(u.Z.UBTTypeEnum.AJAX_ERROR, {
                                                url: n.url,
                                                params: r,
                                                desc: "network",
                                                err: e
                                            }, "netWork Error"))
                                })
                            }
                            )
                    }
                }, {
                    key: "getSAuth",
                    value: function () {
                        var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
                        try {
                            window._n("Su4")
                        } catch (t) { }
                        for (var n = e.Extension, o = void 0 === n ? [] : n, r = 0; r < o.length; r++)
                            "sauth" == o[r].Id && o[r].Value
                    }
                }, {
                    key: "post",
                    value: function (e) {
                        try {
                            window._n("ISp")
                        } catch (t) { }
                        return this.fetch(e, "POST")
                    }
                }, {
                    key: "get",
                    value: function (e) {
                        try {
                            window._n("RkX")
                        } catch (t) { }
                        return this.fetch(e, "GET")
                    }
                }, {
                    key: "infinite",
                    value: function (e, t, n, o, r) {
                        var i = this;
                        try {
                            window._n("AMR")
                        } catch (a) { }
                        var c = function () {
                            try {
                                window._n("eBX")
                            } catch (e) { }
                            i.infiniteTimerId && clearTimeout(i.infiniteTimerId)
                        }
                            , s = this;
                        return function a() {
                            try {
                                window._n("S4V")
                            } catch (d) { }
                            c(),
                                "function" == typeof r && (t = r(t)),
                                i.post(t).then(function (t) {
                                    try {
                                        window._n("v2d")
                                    } catch (o) { }
                                    n(t),
                                        s.infiniteTimerId = setTimeout(a, e)
                                }).catch(function (t) {
                                    try {
                                        window._n("IFs")
                                    } catch (n) { }
                                    o && o(t),
                                        (0,
                                            y.y)(t) ? m.Z.publish(u.Z.EventEnum.CURRENT_USER_TOKEN_INVALID, []) : s.infiniteTimerId = setTimeout(a, e)
                                })
                        }(),
                            c
                    }
                }, {
                    key: "untilEnd",
                    value: function (e, t, n, o, r) {
                        var i = this;
                        try {
                            window._n("4Ut")
                        } catch (a) { }
                        var c = function () {
                            try {
                                window._n("qBQ")
                            } catch (e) { }
                            i.untilEndTimerId && clearTimeout(i.untilEndTimerId)
                        };
                        return function a() {
                            try {
                                window._n("1EW")
                            } catch (s) { }
                            c(),
                                "function" == typeof r && (t = r(t)),
                                t.reachEnd || i.post(t).then(function (t) {
                                    try {
                                        window._n("s5F")
                                    } catch (o) { }
                                    n(t),
                                        i.retryCount = 0,
                                        i.infiniteTimerId = setTimeout(a, e)
                                }).catch(function (e) {
                                    try {
                                        window._n("zrU")
                                    } catch (t) { }
                                    o && o(e),
                                        (0,
                                            y.y)(e) ? m.Z.publish(u.Z.EventEnum.CURRENT_USER_TOKEN_INVALID, []) : i.retryCount < 5 ? (i.retryCount++,
                                                i.infiniteTimerId = setTimeout(a, 1e4)) : i.retryCount = 0
                                })
                        }(),
                            c
                    }
                }, {
                    key: "_getTopicKey",
                    value: function () {
                        try {
                            window._n("CXH")
                        } catch (e) { }
                        return "NEW_MODEL_DATA_".concat(this.url)
                    }
                }, {
                    key: "addListener",
                    value: function (e) {
                        try {
                            window._n("Ix6")
                        } catch (t) { }
                        return m.Z.subscribe(this._getTopicKey(), e)
                    }
                }, {
                    key: "removeListener",
                    value: function (e) {
                        try {
                            window._n("fol")
                        } catch (t) { }
                        m.Z.removeListener(this._getTopicKey(), e)
                    }
                }, {
                    key: "flushData",
                    value: function (e) {
                        try {
                            window._n("JbK")
                        } catch (t) { }
                        m.Z.publish(this._getTopicKey(), [e])
                    }
                }, {
                    key: "getEnvCode",
                    value: function () {
                        try {
                            window._n("LoK")
                        } catch (e) { }
                        return (0,
                            c.Z)()
                    }
                }]),
                e
        }()
    },
    73383: function (e, t, n) {
        n.d(t, {
            Z: function () {
                return e9
            }
        });
        var o = n(74213)
            , r = n(11025)
            , i = n(57186)
            , a = n(35322)
            , c = n(28363)
            , s = n(56725);
        n(13485);
        var u = function (e) {
            (0,
                i.Z)(s, e);
            var t, n = (t = function () {
                if ("undefined" == typeof Reflect || !Reflect.construct || Reflect.construct.sham)
                    return !1;
                if ("function" == typeof Proxy)
                    return !0;
                try {
                    return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () { })),
                        !0
                } catch (e) {
                    return !1
                }
            }(),
                function () {
                    var e, n = (0,
                        c.Z)(s);
                    if (t) {
                        var o = (0,
                            c.Z)(this).constructor;
                        e = Reflect.construct(n, arguments, o)
                    } else
                        e = n.apply(this, arguments);
                    return (0,
                        a.Z)(this, e)
                }
            );
            function s() {
                var e;
                try {
                    window._n("xqs")
                } catch (t) { }
                return (0,
                    r.Z)(this, s),
                    (e = n.call(this)).serverPath = "/restapi/soa2/16037",
                    e.url = "getCustomAndAgentBasicInfo.json",
                    e
            }
            return (0,
                o.Z)(s)
        }(s.Z);
        n(13485);
        var d = function (e) {
            (0,
                i.Z)(s, e);
            var t, n = (t = function () {
                if ("undefined" == typeof Reflect || !Reflect.construct || Reflect.construct.sham)
                    return !1;
                if ("function" == typeof Proxy)
                    return !0;
                try {
                    return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () { })),
                        !0
                } catch (e) {
                    return !1
                }
            }(),
                function () {
                    var e, n = (0,
                        c.Z)(s);
                    if (t) {
                        var o = (0,
                            c.Z)(this).constructor;
                        e = Reflect.construct(n, arguments, o)
                    } else
                        e = n.apply(this, arguments);
                    return (0,
                        a.Z)(this, e)
                }
            );
            function s() {
                var e;
                try {
                    window._n("XGl")
                } catch (t) { }
                return (0,
                    r.Z)(this, s),
                    (e = n.call(this)).url = "getMessageList",
                    e
            }
            return (0,
                o.Z)(s)
        }(s.Z)
            , l = n(26977);
        n(13485);
        var m = null
            , y = function (e) {
                (0,
                    i.Z)(s, e);
                var t, n = (t = function () {
                    if ("undefined" == typeof Reflect || !Reflect.construct || Reflect.construct.sham)
                        return !1;
                    if ("function" == typeof Proxy)
                        return !0;
                    try {
                        return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () { })),
                            !0
                    } catch (e) {
                        return !1
                    }
                }(),
                    function () {
                        var e, n = (0,
                            c.Z)(s);
                        if (t) {
                            var o = (0,
                                c.Z)(this).constructor;
                            e = Reflect.construct(n, arguments, o)
                        } else
                            e = n.apply(this, arguments);
                        return (0,
                            a.Z)(this, e)
                    }
                );
                function s() {
                    var e;
                    try {
                        window._n("Ei6")
                    } catch (t) { }
                    return (0,
                        r.Z)(this, s),
                        (e = n.call(this)).url = "getVenAgentByUid.json",
                        e.currentData = null,
                        e
                }
                return (0,
                    o.Z)(s, [{
                        key: "getCurrentUserInfo",
                        value: function () {
                            var e = this;
                            try {
                                window._n("jDK")
                            } catch (t) { }
                            return new Promise(function (t, n) {
                                try {
                                    window._n("sAI")
                                } catch (o) { }
                                if (null != m) {
                                    t(m);
                                    return
                                }
                                e.post({}).then(function (e) {
                                    try {
                                        window._n("j1b")
                                    } catch (n) { }
                                    (m = e.venAgent).venAgentOption = e.venAgentOption,
                                        m.hasTodoList = e.hasTodoList,
                                        m.abTestResult = e.abTestResult,
                                        t(m),
                                        window.__im_current_user__ = m,
                                        window.__server_client_timediff__ = 0,
                                        e.ResponseStatus && e.ResponseStatus.Timestamp && parseInt(e.ResponseStatus.Timestamp.substr(6, 13)) - new Date().getTime() != 0 && (window.__server_client_timediff__ = e.ResponseStatus.Timestamp.substr(6, 13) - new Date().getTime())
                                }).catch(function (e) {
                                    try {
                                        window._n("0Tx")
                                    } catch (t) { }
                                    n(e)
                                })
                            }
                            )
                        }
                    }]),
                    s
            }(s.Z);
        n(13485);
        var h = function (e) {
            (0,
                i.Z)(s, e);
            var t, n = (t = function () {
                if ("undefined" == typeof Reflect || !Reflect.construct || Reflect.construct.sham)
                    return !1;
                if ("function" == typeof Proxy)
                    return !0;
                try {
                    return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () { })),
                        !0
                } catch (e) {
                    return !1
                }
            }(),
                function () {
                    var e, n = (0,
                        c.Z)(s);
                    if (t) {
                        var o = (0,
                            c.Z)(this).constructor;
                        e = Reflect.construct(n, arguments, o)
                    } else
                        e = n.apply(this, arguments);
                    return (0,
                        a.Z)(this, e)
                }
            );
            function s() {
                var e;
                try {
                    window._n("ScY")
                } catch (t) { }
                return (0,
                    r.Z)(this, s),
                    (e = n.call(this)).serverPath = "restapi/soa2/13500",
                    e.url = "checkMemberStatus.json",
                    e
            }
            return (0,
                o.Z)(s)
        }(s.Z)
            , _ = n(47813)
            , f = n(61023)
            , g = n(7315);
        n(13485);
        var p = function (e) {
            (0,
                i.Z)(s, e);
            var t, n = (t = function () {
                if ("undefined" == typeof Reflect || !Reflect.construct || Reflect.construct.sham)
                    return !1;
                if ("function" == typeof Proxy)
                    return !0;
                try {
                    return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () { })),
                        !0
                } catch (e) {
                    return !1
                }
            }(),
                function () {
                    var e, n = (0,
                        c.Z)(s);
                    if (t) {
                        var o = (0,
                            c.Z)(this).constructor;
                        e = Reflect.construct(n, arguments, o)
                    } else
                        e = n.apply(this, arguments);
                    return (0,
                        a.Z)(this, e)
                }
            );
            function s(e) {
                var t;
                try {
                    window._n("gMB")
                } catch (o) { }
                return (0,
                    r.Z)(this, s),
                    (t = n.call(this, e)).serverPath = "restapi/soa2/11679",
                    t.url = "getGroupMembers.json",
                    t.data = {
                        pageIndex: 1,
                        reachEnd: !1,
                        fetching: !1
                    },
                    t
            }
            return (0,
                o.Z)(s, [{
                    key: "ping",
                    value: function (e, t) {
                        var n = this;
                        try {
                            window._n("pIL")
                        } catch (o) { }
                        this.data.reachEnd = !1,
                            this.dataList = [],
                            this.data.pageIndex = 1,
                            this.untilEnd(10, (0,
                                _.Z)({
                                    paging: !0,
                                    groupJid: e.gid
                                }, "paging", {
                                    pageIndex: this.data.pageIndex,
                                    pageSize: 100
                                }), function (e) {
                                    try {
                                        window._n("58w")
                                    } catch (o) { }
                                    n.dataList = n.dataList.concat(e.members),
                                        e.pageResult.pageCount != n.data.pageIndex && e.pageResult.pageCount ? (n.data.pageIndex++,
                                            n.data.reachEnd = !1) : (n.data.reachEnd = !0,
                                                t && t(n.dataList))
                                }, function (e) {
                                    try {
                                        window._n("kiD")
                                    } catch (t) { }
                                }, function (e) {
                                    try {
                                        window._n("cD3")
                                    } catch (t) { }
                                    return n.data.pageIndex && (e.paging.pageIndex = n.data.pageIndex),
                                        n.data.reachEnd && (e.reachEnd = !0),
                                        e
                                })
                    }
                }]),
                s
        }(s.Z)
            , w = n(18754);
        function v(e, t) {
            var n = Object.keys(e);
            if (Object.getOwnPropertySymbols) {
                var o = Object.getOwnPropertySymbols(e);
                t && (o = o.filter(function (t) {
                    return Object.getOwnPropertyDescriptor(e, t).enumerable
                })),
                    n.push.apply(n, o)
            }
            return n
        }
        function C(e) {
            for (var t = 1; t < arguments.length; t++) {
                var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? v(Object(n), !0).forEach(function (t) {
                    (0,
                        _.Z)(e, t, n[t])
                }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : v(Object(n)).forEach(function (t) {
                    Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                })
            }
            return e
        }
        n(13485);
        var Z = ["uid", "status", "conversationType", "serviceType", "ctripAgentId", "star"]
            , T = [1, 2, 4]
            , E = function (e) {
                try {
                    window._n("4pX")
                } catch (t) { }
                for (var n = [], o = 0; o < e.length; o++) {
                    var r = e[o]
                        , i = r.gid
                        , a = r.uid
                        , c = r.conversationType
                        , s = r.conversationKey
                        , u = r.status
                        , d = r.venAgentId
                        , l = r.sessionId;
                    n.push({
                        gid: i,
                        uid: a,
                        conversationType: c,
                        conversationKey: s,
                        status: u,
                        venAgentId: d,
                        sessionId: l
                    })
                }
                (0,
                    g.Z)(f.Z.UBTTypeEnum.NEW_CONV, {
                        convList: n
                    })
            }
            , k = function (e) {
                (0,
                    i.Z)(s, e);
                var t, n = (t = function () {
                    if ("undefined" == typeof Reflect || !Reflect.construct || Reflect.construct.sham)
                        return !1;
                    if ("function" == typeof Proxy)
                        return !0;
                    try {
                        return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () { })),
                            !0
                    } catch (e) {
                        return !1
                    }
                }(),
                    function () {
                        var e, n = (0,
                            c.Z)(s);
                        if (t) {
                            var o = (0,
                                c.Z)(this).constructor;
                            e = Reflect.construct(n, arguments, o)
                        } else
                            e = n.apply(this, arguments);
                        return (0,
                            a.Z)(this, e)
                    }
                );
                function s(e) {
                    var t;
                    try {
                        window._n("AaP")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, s),
                        (t = n.call(this, e)).url = "getConversationList.json",
                        t.timeout = 3e4,
                        t.data = {
                            lastUpdateTime: null,
                            convList: [],
                            agentStatus: null
                        },
                        t
                }
                return (0,
                    o.Z)(s, [{
                        key: "isConvChange",
                        value: function (e, t) {
                            try {
                                window._n("Khq")
                            } catch (n) { }
                            for (var o = 0; o < Z.length; o++) {
                                var r = Z[o];
                                if (e[r] !== t[r])
                                    return !0
                            }
                            return !1
                        }
                    }, {
                        key: "getChangedConvList",
                        value: function (e) {
                            try {
                                window._n("UbI")
                            } catch (t) { }
                            for (var n = [], o = [], r = !1, i = 0; i < e.length; i++) {
                                var a = e[i];
                                if (!(T.indexOf(a.status) < 0)) {
                                    r = !1;
                                    for (var c = 0; c < this.data.convList.length; c++)
                                        if (this.data.convList[c].id === a.id) {
                                            this.isConvChange(this.data.convList[c], a) && (n.push(C({}, a)),
                                                this.data.convList[c] = a),
                                                r = !0;
                                            break
                                        }
                                    r || (o.push(a),
                                        n.push(C({}, a)))
                                }
                            }
                            return this.data.convList = [].concat(this.data.convList, o),
                                n
                        }
                    }, {
                        key: "ping",
                        value: function (e) {
                            var t = this;
                            try {
                                window._n("yjW")
                            } catch (n) { }
                            return e = e || {},
                                this.data.lastUpdateTime && (e.startTime = this.data.lastUpdateTime),
                                this.post(e).then(function (e) {
                                    try {
                                        window._n("tzC")
                                    } catch (n) { }
                                    return e.lastUpdateTime && (t.data.lastUpdateTime = e.lastUpdateTime),
                                        e.conversationList = e.conversationList || [],
                                        e
                                })
                        }
                    }, {
                        key: "loop",
                        value: function (e) {
                            var t = this;
                            try {
                                window._n("KFS")
                            } catch (n) { }
                            a = a || {};
                            var o = e.interval
                                , r = e.success
                                , i = e.fail
                                , a = e.params
                                , c = function () { };
                            return r = r || c,
                                i = i || c,
                                this.infinite(o, C({}, a), function (e) {
                                    try {
                                        window._n("vuU")
                                    } catch (n) { }
                                    e.conversationList = e.conversationList || [],
                                        e.agentTodoTaskList = e.agentTodoTaskList || [],
                                        e.agentTodoTaskList.forEach(function (e) {
                                            try {
                                                window._n("jWB")
                                            } catch (t) { }
                                            e.chatType = "waitlist",
                                                e.id = e.taskKey
                                        }),
                                        r(e),
                                        e.conversationList.length > 0 && E(e.conversationList),
                                        e.lastUpdateTime && (t.data.lastUpdateTime = e.lastUpdateTime)
                                }, function (e) {
                                    try {
                                        window._n("rWC")
                                    } catch (t) { }
                                    i(e),
                                        (0,
                                            g.Z)(f.Z.UBTTypeEnum.CATCHED_ERR, e)
                                }, function (e) {
                                    try {
                                        window._n("CQ4")
                                    } catch (n) { }
                                    return t.data.lastUpdateTime && (e.startTime = t.data.lastUpdateTime),
                                        e
                                })
                        }
                    }]),
                    s
            }(s.Z);
        n(13485);
        var S = function (e) {
            (0,
                i.Z)(s, e);
            var t, n = (t = function () {
                if ("undefined" == typeof Reflect || !Reflect.construct || Reflect.construct.sham)
                    return !1;
                if ("function" == typeof Proxy)
                    return !0;
                try {
                    return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () { })),
                        !0
                } catch (e) {
                    return !1
                }
            }(),
                function () {
                    var e, n = (0,
                        c.Z)(s);
                    if (t) {
                        var o = (0,
                            c.Z)(this).constructor;
                        e = Reflect.construct(n, arguments, o)
                    } else
                        e = n.apply(this, arguments);
                    return (0,
                        a.Z)(this, e)
                }
            );
            function s(e) {
                var t;
                try {
                    window._n("ydW")
                } catch (o) { }
                return (0,
                    r.Z)(this, s),
                    (t = n.call(this, e)).url = "getLatestSingleChat",
                    t.data = {
                        lastMessageTime: null,
                        reachEnd: !1,
                        fetching: !1
                    },
                    t
            }
            return (0,
                o.Z)(s, [{
                    key: "ping",
                    value: function () {
                        var e = this;
                        try {
                            window._n("z2N")
                        } catch (t) { }
                        this.data.reachEnd = !1,
                            this.untilEnd(10, {
                                paging: !0
                            }, function (t) {
                                try {
                                    window._n("3fz")
                                } catch (n) { }
                                t.haveRest ? e.data.reachEnd = !1 : e.data.reachEnd = !0,
                                    t.lastMessageTime && (e.data.lastMessageTime = t.lastMessageTime),
                                    e.flushData(t)
                            }, function (e) {
                                try {
                                    window._n("YU2")
                                } catch (t) { }
                            }, function (t) {
                                try {
                                    window._n("oqf")
                                } catch (n) { }
                                return e.data.lastMessageTime && (t.lastMessageTime = e.data.lastMessageTime),
                                    e.data.reachEnd && (t.reachEnd = !0),
                                    t
                            })
                    }
                }]),
                s
        }(s.Z);
        n(13485);
        var M = function (e) {
            (0,
                i.Z)(s, e);
            var t, n = (t = function () {
                if ("undefined" == typeof Reflect || !Reflect.construct || Reflect.construct.sham)
                    return !1;
                if ("function" == typeof Proxy)
                    return !0;
                try {
                    return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () { })),
                        !0
                } catch (e) {
                    return !1
                }
            }(),
                function () {
                    var e, n = (0,
                        c.Z)(s);
                    if (t) {
                        var o = (0,
                            c.Z)(this).constructor;
                        e = Reflect.construct(n, arguments, o)
                    } else
                        e = n.apply(this, arguments);
                    return (0,
                        a.Z)(this, e)
                }
            );
            function s(e) {
                var t;
                try {
                    window._n("bOh")
                } catch (o) { }
                return (0,
                    r.Z)(this, s),
                    (t = n.call(this, e)).url = "getNoSheetConversationList",
                    t.timeout = 2e4,
                    t.data = {
                        lastMessageTime: null,
                        reachEnd: !1,
                        fetching: !1
                    },
                    t
            }
            return (0,
                o.Z)(s, [{
                    key: "ping",
                    value: function () {
                        var e = this;
                        try {
                            window._n("TIm")
                        } catch (t) { }
                        this.data.reachEnd = !1,
                            this.dataList = [],
                            this.untilEnd(10, {
                                paging: !0
                            }, function (t) {
                                try {
                                    window._n("uiM")
                                } catch (n) { }
                                e.dataList = e.dataList.concat(t.conversationList),
                                    t.haveRest ? e.data.reachEnd = !1 : (e.data.reachEnd = !0,
                                        t.conversationList = e.dataList,
                                        e.flushData(t)),
                                    t.lastMessageTime && (e.data.lastMessageTime = t.lastMessageTime)
                            }, function (e) {
                                try {
                                    window._n("eg1")
                                } catch (t) { }
                            }, function (t) {
                                try {
                                    window._n("Jbt")
                                } catch (n) { }
                                return e.data.lastMessageTime && (t.lastMessageTime = e.data.lastMessageTime),
                                    e.data.reachEnd && (t.reachEnd = !0),
                                    t
                            })
                    }
                }]),
                s
        }(s.Z);
        function I(e) {
            var t = function () {
                if ("undefined" == typeof Reflect || !Reflect.construct || Reflect.construct.sham)
                    return !1;
                if ("function" == typeof Proxy)
                    return !0;
                try {
                    return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () { })),
                        !0
                } catch (e) {
                    return !1
                }
            }();
            return function () {
                var n, o = (0,
                    c.Z)(e);
                if (t) {
                    var r = (0,
                        c.Z)(this).constructor;
                    n = Reflect.construct(o, arguments, r)
                } else
                    n = o.apply(this, arguments);
                return (0,
                    a.Z)(this, n)
            }
        }
        n(13485);
        var N = function (e) {
            (0,
                i.Z)(n, e);
            var t = I(n);
            function n() {
                var e;
                try {
                    window._n("ygT")
                } catch (o) { }
                return (0,
                    r.Z)(this, n),
                    (e = t.call(this)).url = "getCustomAndAgentBasicInfo.json",
                    e
            }
            return (0,
                o.Z)(n)
        }(s.Z)
            , b = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("BXk")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).serverPath = "restapi/soa2/11679",
                        e.url = "putAdviceOfReadByMsgId.json",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , O = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("Zad")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "getCustomAreaInfo.json",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , A = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("i4L")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "getCustomAreaConfig",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , j = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("ZZo")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "inviteVenAgent.json",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , L = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("0Rd")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "getInviteMemberList.json",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , P = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("eTF")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).serverPath = "restapi/soa2/12721",
                        e.url = "GetAllGroupMembersEn.json",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , R = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n(e) {
                    var o;
                    try {
                        window._n("u4z")
                    } catch (i) { }
                    return (0,
                        r.Z)(this, n),
                        (o = t.call(this, e)).url = "getRemarkList",
                        o
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , B = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("w59")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "createChatB2O",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , U = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("1Xo")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "startChatB2C",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , x = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("15S")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "createChatB2B",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , D = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("wHQ")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "createChatB2BReverse",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , G = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("0zW")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).serverPath = "restapi/soa2/13500",
                        e.url = "sendAIChatMessage.json",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , V = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("lSA")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).serverPath = "restapi/soa2/13500",
                        e.url = "getToken",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , F = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("3mw")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).serverPath = "api/manage",
                        e.url = "getToken",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , H = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("Rpa")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).serverPath = "restapi/soa2/13500",
                        e.url = "addToken",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , W = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("YMB")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "checkSessionStatus",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , J = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("Q2c")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "DropoutManual",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , q = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("msw")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).serverPath = "restapi/soa2/13500",
                        e.url = "getRelativeQuestions",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , z = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("yXd")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).serverPath = "restapi/soa2/13500",
                        e.url = "commentChat",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , Q = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("lAz")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).serverPath = "restapi/soa2/13500",
                        e.url = "evaluateRobotMessage",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , K = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("x1j")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).serverPath = "restapi/soa2/13500",
                        e.url = "checkScoreStatus",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , Y = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("HrP")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "checkScoreStatus",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , X = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("edX")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).serverPath = "restapi/soa2/13500",
                        e.url = "postScore",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , $ = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("a94")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "submitScore",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , ee = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("hM2")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).serverPath = "restapi/soa2/13500",
                        e.url = "checkChatStatus",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , et = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("mu2")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).serverPath = "restapi/soa2/11679",
                        e.url = "getMessageTranslate",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , en = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("ygt")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).serverPath = "manageapi/soaproxy/11611",
                        e.url = "getMessageTranslate",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eo = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("gzt")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).serverPath = "restapi/soa2/11679",
                        e.url = "RecallMessage",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , er = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("yeG")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).serverPath = "restapi/soa2/16037",
                        e.url = "recallMessageByAdmin",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , ei = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("5BS")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "getDLTOrderInfo",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , ea = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("rXD")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).serverPath = "restapi/soa2/15223",
                        e.url = "getCardMessageFromURL",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , ec = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("mt5")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "setWorkSheetStar",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , es = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("foy")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "transferVenMaster",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eu = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("QcS")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "getLatestSingleChat",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , ed = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("3lV")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "getHistorySingleChat",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , el = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("K7s")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "getSystemNotice",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , em = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("T07")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "getAgentIMCtripInfoByUID",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , ey = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("6hm")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "setVenAgentStatus",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eh = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("Zv0")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "getTimeZoneList",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , e_ = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("NY1")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "getAgentWorkingSchedule",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , ef = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("lv2")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "modifyAgentWorkingSchedule",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eg = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("4Zo")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).timeout = 15e3,
                        e.url = "searchConversation",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , ep = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("V1W")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "modifyRemark",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , ew = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("O5g")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "getConversationInfoByGidList",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , ev = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("333")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).timeout = 4e4,
                        e.url = "getMessagesBySession",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eC = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("XPZ")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "getWordsAboves",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eZ = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("hNC")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "saveWordsAboves",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eT = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("m0E")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "createRootCategory",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eE = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("tkr")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "getCategories",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , ek = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("qqf")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "updateCategory",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eS = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("QNP")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "updateCategorySortNo",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eM = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("P0E")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "deleteCategory",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eI = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("EUm")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "addCustomerScript",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eN = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("8r3")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "updateCustomerScript",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eb = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("PuI")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "updateCustomerScriptSort",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eO = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("ZU3")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "deleteCustomerScript",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eA = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("Bk3")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "getSkillGroupInfoByVenAgent",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , ej = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("u6q")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "getCustomerScripts",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eL = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("nQX")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "getInviteMemberListBySkillGroup",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eP = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("wvd")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "searchInviteMemberListBySkillGroup",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eR = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("FyJ")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).serverPath = "restapi/soa2/11679",
                        e.url = "batchTranslateByMsgIds",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eB = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("TQ9")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "ModifyVendorAgentOption",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eU = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("VFo")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "getVenAgentLocaleList",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , ex = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("l6B")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "sendEmailVerificationCode",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eD = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("tq3")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "checkEmailVerificationCode",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eG = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("v4p")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "agentApplyTask",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eV = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("JYa")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "startChatC2O",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eF = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("IeH")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "genTrippalLink",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eH = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("KUL")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "getEvaluateInfo",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eW = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("lFI")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "verifyOrderId",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eJ = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("h4z")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "StartChatB2MC",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eq = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("Zn8")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "collectQuestionsList",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , ez = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("sFQ")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "getReportInformation",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eQ = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("xaA")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "reportCustomer",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eK = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("JNF")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "getMailContent",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eY = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("BIc")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).serverPath = "restapi/soa2/16476",
                        e.url = "getDetailEmail.json",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , eX = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("Vk4")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "getNoSheetConversation.json",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , e$ = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("QVw")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "RemoveMember.json",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , e0 = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("feh")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "updatePlanetGroupTitle.json",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , e1 = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("dUP")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "insertOrUpdateWelcomeMessage.json",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , e2 = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("tQz")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "getPlanetGroupWelcomeMessage.json",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , e3 = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("Ftl")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "getInviteCtripData",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , e4 = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("xP6")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).url = "inviteCtrip",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , e5 = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("DPP")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).isIQ = !0,
                        e.serverPath = "restapi/soa2/11679",
                        e.url = "getIQConversations",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , e7 = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("vIb")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).isIQ = !0,
                        e.serverPath = "restapi/soa2/11679",
                        e.url = "getIQMessages",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , e6 = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("cqU")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).isIQ = !0,
                        e.serverPath = "restapi/soa2/11679",
                        e.url = "putAdviceofreadIQMessage",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , e8 = function (e) {
                (0,
                    i.Z)(n, e);
                var t = I(n);
                function n() {
                    var e;
                    try {
                        window._n("M0q")
                    } catch (o) { }
                    return (0,
                        r.Z)(this, n),
                        (e = t.call(this)).serverPath = "restapi/soa2/16037",
                        e.url = "uploadTokenCreate",
                        e
                }
                return (0,
                    o.Z)(n)
            }(s.Z)
            , e9 = {
                customerInfoModel: new u,
                messageListModel: new d,
                messageListModelV2: new d,
                imUploadImageModel: new l.Z,
                userInfoModel: new y,
                customAndAgentBasicInfoModel: new N,
                putAdviceOfReadByMsgIdModel: new b,
                customAreaModel: new O,
                customAreaConfig: new A,
                inviteAgentModel: new j,
                inviteMemberListModel: new L,
                getAllGroupMembers: new P,
                getAllGroupMembersAndRemark: new R,
                modifyRemark: new ep,
                createChatB2O: new B,
                createChatB2B: new x,
                createChatB2BReverse: new D,
                sendAIMessage: new G,
                getCurrentSessionMode: new W,
                dropoutManualModel: new J,
                getRelativeQuestions: new q,
                commentChatMessage: new z,
                evaluateRobotMessage: new Q,
                postScore: new X,
                submitScore: new $,
                checkChatStatus: new ee,
                checkScoreStatus: new K,
                checkScoreStatusBySession: new Y,
                getDLTOrderInfo: new ei,
                getCardMessageFromURL: new ea,
                createChartB2C: new U,
                setWorkSheetStar: new ec,
                transferVenMaster: new es,
                getSingleChatList: new eu,
                getSingleChatMessageList: new ed,
                groupMemberModel: new h,
                GroupMemberModel2: new p,
                getSystemNotice: new el,
                getAgentIMCtripInfoByUID: new em,
                finishConversationModel: new w.Z,
                conversationModel: new k,
                setVenAgentStatus: new ey,
                getTimeZoomList: new eh,
                getAgentWorkingSchedule: new e_,
                modifyAgentWorkingSchedule: new ef,
                getMessageTranslate: new et,
                getMessageTranslateOffline: new en,
                RecallMessageIM: new eo,
                RecallMessageByAdmin: new er,
                searchConversation: new eg,
                getConversationInfoByGidList: new ew,
                getToken: new V,
                GetTokenOffline: new F,
                addToken: new H,
                getMessagesBySession: new ev,
                getAutoReplayWords: new eC,
                saveAutoReplayWords: new eZ,
                signleChatModel: new S,
                xingqiuChatModel: new M,
                createCategory: new eT,
                getCategories: new eE,
                updateCategory: new ek,
                updateCategorySortNo: new eS,
                deleteCategory: new eM,
                addCustomerScript: new eI,
                updateCustomerScript: new eN,
                deleteCustomerScript: new eO,
                updateCustomerScriptSort: new eb,
                getCustomerScripts: new ej,
                getInviteMemberListBySkillGroup: new eL,
                searchInviteMemberListBySkillGroup: new eP,
                batchTranslate: new eR,
                modifyVendorAgentOption: new eB,
                getVenAgentLocaleList: new eU,
                sendEmailVerificationCode: new ex,
                checkEmailVerificationCode: new eD,
                agentApplyTask: new eG,
                startChatC2O: new eV,
                genTrippalLink: new eF,
                getEvaluateInfo: new eH,
                verifyOrderId: new eW,
                startChatB2MC: new eJ,
                collectQuestionsList: new eq,
                getReportInfomation: new ez,
                reportCustomer: new eQ,
                getMailContent: new eK,
                getMailContentOffline: new eY,
                getNoSheetConversation: new eX,
                removeMember: new e$,
                updateGroupTitle: new e0,
                updateWelcomeMessage: new e1,
                getWelcomeMessage: new e2,
                getInviteCtripData: new e3,
                inviteCtrip: new e4,
                getIQConversations: new e5,
                getIQMessages: new e7,
                putAdviceofreadIQMessage: new e6,
                uploadTokenCreate: new e8,
                getSkillGroupInfoByVenAgent: new eA
            }
    },
    18754: function (e, t, n) {
        n.d(t, {
            Z: function () {
                return u
            }
        });
        var o = n(74213)
            , r = n(11025)
            , i = n(57186)
            , a = n(35322)
            , c = n(28363)
            , s = n(56725);
        n(13485);
        var u = function (e) {
            (0,
                i.Z)(s, e);
            var t, n = (t = function () {
                if ("undefined" == typeof Reflect || !Reflect.construct || Reflect.construct.sham)
                    return !1;
                if ("function" == typeof Proxy)
                    return !0;
                try {
                    return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () { })),
                        !0
                } catch (e) {
                    return !1
                }
            }(),
                function () {
                    var e, n = (0,
                        c.Z)(s);
                    if (t) {
                        var o = (0,
                            c.Z)(this).constructor;
                        e = Reflect.construct(n, arguments, o)
                    } else
                        e = n.apply(this, arguments);
                    return (0,
                        a.Z)(this, e)
                }
            );
            function s() {
                var e;
                try {
                    window._n("RWG")
                } catch (t) { }
                return (0,
                    r.Z)(this, s),
                    (e = n.call(this)).url = "finishConversation.json",
                    e
            }
            return (0,
                o.Z)(s)
        }(s.Z)
    },
    32336: function (e, t, n) {
        n.d(t, {
            Z: function () {
                return d
            }
        });
        var o = n(47813)
            , r = n(11025)
            , i = n(74213);
        n(56725);
        var a = n(57770);
        n(95514);
        var c = n(36652)
            , s = n.n(c);
        function u(e, t) {
            var n = Object.keys(e);
            if (Object.getOwnPropertySymbols) {
                var o = Object.getOwnPropertySymbols(e);
                t && (o = o.filter(function (t) {
                    return Object.getOwnPropertyDescriptor(e, t).enumerable
                })),
                    n.push.apply(n, o)
            }
            return n
        }
        n(13485);
        var d = function () {
            function e() {
                try {
                    window._n("4oT")
                } catch (t) { }
                (0,
                    r.Z)(this, e)
            }
            return (0,
                i.Z)(e, [{
                    key: "getCommonParam",
                    value: function (e) {
                        try {
                            window._n("R29")
                        } catch (t) { }
                        "undefined" != typeof __im_current_user__ && (e.uid = __im_current_user__.ctripUid || ""),
                            $_bf && "function" == typeof $_bf._getStatus && (e.vid = ($_bf._getStatus() || {}).vid);
                        var n = (0,
                            a.m)(location.search);
                        return e.url || (e.url = encodeURIComponent(location.href),
                            e.pathname = location.pathname),
                            e.client_source || (e.client_source = n && n.accountsource && n.accountsource.toLowerCase() || ""),
                            e
                    }
                }, {
                    key: "getCommonParamV2",
                    value: function (e) {
                        try {
                            window._n("eaT")
                        } catch (t) { }
                        "undefined" != typeof __im_current_user__ && (e.uid = __im_current_user__.ctripUid || ""),
                            $_bf && "function" == typeof $_bf._getStatus && (e.vid = ($_bf._getStatus() || {}).vid);
                        var n = (0,
                            a.m)(location.search) || {};
                        e.url || (e.url = encodeURIComponent(location.href),
                            e.pathname = location.pathname);
                        var o = n.appid
                            , r = n.accountsource
                            , i = n["client-source"];
                        !o && i && (o = i.split("-")[0]);
                        var c = r || o;
                        return c && (e.client_source = c.toLowerCase()),
                            e
                    }
                }, {
                    key: "send",
                    value: function (e) {
                        try {
                            window._n("Ync")
                        } catch (t) { }
                        var n = this.getCommonParam(e);
                        this.customizeDevTrace(n)
                    }
                }, {
                    key: "sendV2",
                    value: function (e) {
                        try {
                            window._n("tqF")
                        } catch (t) { }
                        var n = this.getCommonParamV2(e);
                        this.customizeDevTrace(n)
                    }
                }, {
                    key: "customizeDevTrace",
                    value: function (e) {
                        var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "b_implus_log_data";
                        try {
                            window._n("Cci")
                        } catch (n) { }
                        try {
                            var r = this.buildData(e);
                            s().send({
                                type: c.UBT_TYPE.DEV_TRACE,
                                key: t,
                                data: function (e) {
                                    for (var t = 1; t < arguments.length; t++) {
                                        var n = null != arguments[t] ? arguments[t] : {};
                                        t % 2 ? u(Object(n), !0).forEach(function (t) {
                                            (0,
                                                o.Z)(e, t, n[t])
                                        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : u(Object(n)).forEach(function (t) {
                                            Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                                        })
                                    }
                                    return e
                                }({
                                    time: Date.now(),
                                    name: t
                                }, r)
                            }, window.pvId || "")
                        } catch (i) {
                            s().send({
                                type: c.UBT_TYPE.TRACE,
                                key: "b_implus_log_err",
                                data: {
                                    data: JSON.stringify(e) + "error:" + i,
                                    time: Date.now(),
                                    name: "b_implus_log_err"
                                }
                            }, window.pvId || "")
                        }
                    }
                }, {
                    key: "buildData",
                    value: function (e) {
                        try {
                            window._n("Ocb")
                        } catch (t) { }
                        try {
                            var n = JSON.stringify(e).length > 12800
                                , o = {};
                            if (n)
                                o = {
                                    data: this.cutString(JSON.stringify(e))
                                };
                            else {
                                for (var r in e)
                                    null == e[r] || void 0 == e[r] ? delete e[r] : "object" == typeof e[r] && (e[r] = JSON.stringify(e[r]));
                                o = e
                            }
                            return o
                        } catch (i) {
                            throw Error("[ubt buildData error]: ", i)
                        }
                    }
                }, {
                    key: "cutString",
                    value: function (e) {
                        var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 2048;
                        try {
                            window._n("2rm")
                        } catch (n) { }
                        return e.length > t && (e = e.substring(0, t)),
                            e
                    }
                }, {
                    key: "loop",
                    value: function (e) {
                        var t = this;
                        try {
                            window._n("5AI")
                        } catch (n) { }
                        var o = e.interval
                            , r = e.params
                            , i = function () {
                                try {
                                    window._n("4Qb")
                                } catch (e) { }
                                t.infiniteTimerId && clearTimeout(t.infiniteTimerId)
                            }
                            , a = this;
                        return function e() {
                            try {
                                window._n("AbH")
                            } catch (n) { }
                            i(),
                                t.send(r),
                                a.infiniteTimerId = setTimeout(e, o)
                        }(),
                            i
                    }
                }]),
                e
        }()
    },
    26977: function (e, t, n) {
        n.d(t, {
            Z: function () {
                return u
            }
        });
        var o = n(74213)
            , r = n(11025)
            , i = n(57186)
            , a = n(35322)
            , c = n(28363)
            , s = n(56725);
        n(13485);
        var u = function (e) {
            (0,
                i.Z)(s, e);
            var t, n = (t = function () {
                if ("undefined" == typeof Reflect || !Reflect.construct || Reflect.construct.sham)
                    return !1;
                if ("function" == typeof Proxy)
                    return !0;
                try {
                    return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () { })),
                        !0
                } catch (e) {
                    return !1
                }
            }(),
                function () {
                    var e, n = (0,
                        c.Z)(s);
                    if (t) {
                        var o = (0,
                            c.Z)(this).constructor;
                        e = Reflect.construct(n, arguments, o)
                    } else
                        e = n.apply(this, arguments);
                    return (0,
                        a.Z)(this, e)
                }
            );
            function s() {
                var e;
                try {
                    window._n("l8I")
                } catch (t) { }
                return (0,
                    r.Z)(this, s),
                    (e = n.call(this)).serverPath = "restapi/soa2/18837",
                    e.url = "ImUploadImage.json",
                    e.timeout = 15e3,
                    e
            }
            return (0,
                o.Z)(s)
        }(s.Z)
    },
    62609: function (e, t, n) {
        n.d(t, {
            $N: function () {
                return d
            },
            i0: function () {
                return m
            },
            w9: function () {
                return l
            },
            x1: function () {
                return y
            }
        });
        var o = n(57186)
            , r = n(35322)
            , i = n(28363)
            , a = n(11025)
            , c = n(74213);
        function s(e) {
            var t = function () {
                if ("undefined" == typeof Reflect || !Reflect.construct || Reflect.construct.sham)
                    return !1;
                if ("function" == typeof Proxy)
                    return !0;
                try {
                    return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () { })),
                        !0
                } catch (e) {
                    return !1
                }
            }();
            return function () {
                var n, o = (0,
                    i.Z)(e);
                if (t) {
                    var a = (0,
                        i.Z)(this).constructor;
                    n = Reflect.construct(o, arguments, a)
                } else
                    n = o.apply(this, arguments);
                return (0,
                    r.Z)(this, n)
            }
        }
        n(13485);
        var u = function () {
            function e() {
                try {
                    window._n("lnV")
                } catch (t) { }
                (0,
                    a.Z)(this, e),
                    this.key = "",
                    this.lifetime = "30M",
                    this.storage = window.localStorage
            }
            return (0,
                c.Z)(e, [{
                    key: "setData",
                    value: function (e) {
                        var t;
                        try {
                            window._n("lR2")
                        } catch (n) { }
                        try {
                            return t = this._buildStorageData(e),
                                this.storage.setItem(this.key, JSON.stringify(t)),
                                !0
                        } catch (o) {
                            ("QuotaExceededError" == o.name || "QUOTA_EXCEEDED_ERR" == o.name) && (this.storage.clear(),
                                this.setData(e))
                        }
                        return !1
                    }
                }, {
                    key: "getData",
                    value: function () {
                        try {
                            window._n("xXo")
                        } catch (e) { }
                        return (this._getData() || {}).data
                    }
                }, {
                    key: "_setData",
                    value: function (e) {
                        try {
                            window._n("tyB")
                        } catch (t) { }
                        try {
                            return this.storage.setItem(this.key, JSON.stringify(e)),
                                !0
                        } catch (n) {
                            ("QuotaExceededError" == n.name || "QUOTA_EXCEEDED_ERR" == n.name) && (this.storage.clear(),
                                this._setData(e))
                        }
                        return !1
                    }
                }, {
                    key: "_getData",
                    value: function () {
                        try {
                            window._n("YJq")
                        } catch (e) { }
                        var t, n, o = null;
                        try {
                            (n = (t = this.storage.getItem(this.key)) ? JSON.parse(t) : null) && n.expiretime && (this._isExpiredTime(n.expiretime) ? this.storage.removeItem(this.key) : o = n)
                        } catch (r) { }
                        return o
                    }
                }, {
                    key: "_isExpiredTime",
                    value: function (e) {
                        try {
                            window._n("aRN")
                        } catch (t) { }
                        return this._getNowTime() >= e
                    }
                }, {
                    key: "_buildStorageData",
                    value: function (e) {
                        try {
                            window._n("vio")
                        } catch (t) { }
                        return {
                            data: e,
                            savetime: this._getNowTime(),
                            expiretime: this._getExpireTime()
                        }
                    }
                }, {
                    key: "_getNowTime",
                    value: function () {
                        try {
                            window._n("wSZ")
                        } catch (e) { }
                        return new Date().getTime()
                    }
                }, {
                    key: "_getExpireTime",
                    value: function () {
                        try {
                            window._n("A7N")
                        } catch (e) { }
                        var t, n = this.lifetime, o = this._getNowTime(), r = n.slice(-1), i = Number(n.slice(0, n.length - 1));
                        switch (r) {
                            case "M":
                                t = 6e4;
                                break;
                            case "H":
                                t = 36e5;
                                break;
                            case "D":
                                t = 864e5
                        }
                        return o + i * t
                    }
                }, {
                    key: "remove",
                    value: function () {
                        try {
                            window._n("hh4")
                        } catch (e) { }
                        this.storage.removeItem(this.key)
                    }
                }, {
                    key: "_sendLog",
                    value: function (e) {
                        try {
                            window._n("bEb")
                        } catch (t) { }
                        "function" == typeof __mw_send_req__ && __mw_send_req__(e)
                    }
                }]),
                e
        }();
        u.getInstance = function () {
            try {
                window._n("WZM")
            } catch (e) { }
            return this.instance && this.instance instanceof this || (this.instance = new this),
                this.instance
        }
            ;
        var d = (function (e) {
            (0,
                o.Z)(n, e);
            var t = s(n);
            function n(e) {
                var o;
                try {
                    window._n("S5n")
                } catch (r) { }
                return (0,
                    a.Z)(this, n),
                    (o = t.call(this, e)).key = "__im_online_conv_status__",
                    o.lifetime = "6H",
                    o
            }
            return (0,
                c.Z)(n, [{
                    key: "setExpandedStatus",
                    value: function (e) {
                        try {
                            window._n("3Ln")
                        } catch (t) { }
                        var n = this.getData() || {};
                        n[e] = !0,
                            this.setData(n)
                    }
                }]),
                n
        }
        )(u).getInstance()
            , l = (function (e) {
                (0,
                    o.Z)(n, e);
                var t = s(n);
                function n(e) {
                    var o;
                    try {
                        window._n("2xj")
                    } catch (r) { }
                    return (0,
                        a.Z)(this, n),
                        (o = t.call(this, e)).key = "__im_online_conv_thread__",
                        o.storage = window.sessionStorage,
                        o
                }
                return (0,
                    c.Z)(n, [{
                        key: "saveThreadId",
                        value: function (e) {
                            try {
                                window._n("XzY")
                            } catch (t) { }
                            if (e) {
                                var n = this.getData() || {};
                                n[e] = !0,
                                    this.setData(n)
                            }
                        }
                    }, {
                        key: "checkExsit",
                        value: function (e) {
                            try {
                                window._n("47w")
                            } catch (t) { }
                            return !!e && !!(this.getData() || {})[e]
                        }
                    }]),
                    n
            }
            )(u).getInstance()
            , m = (function (e) {
                (0,
                    o.Z)(n, e);
                var t = s(n);
                function n(e) {
                    var o;
                    try {
                        window._n("aCQ")
                    } catch (r) { }
                    return (0,
                        a.Z)(this, n),
                        (o = t.call(this, e)).key = "__im_online_agent_info__",
                        o.lifetime = "6H",
                        o
                }
                return (0,
                    c.Z)(n)
            }
            )(u).getInstance()
            , y = (function (e) {
                (0,
                    o.Z)(n, e);
                var t = s(n);
                function n(e) {
                    var o;
                    try {
                        window._n("0T9")
                    } catch (r) { }
                    return (0,
                        a.Z)(this, n),
                        (o = t.call(this, e)).key = "__im_online_currentconv_info__",
                        o.lifetime = "30D",
                        o
                }
                return (0,
                    c.Z)(n)
            }
            )(u).getInstance()
    }
}]);