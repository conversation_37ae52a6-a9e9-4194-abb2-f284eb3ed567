{"name": "ctrip-helper", "version": "1.0.3", "description": "携程IM拦截器 - 拦截和分析携程网站的IM WebSocket通信，支持自动回复功能", "scripts": {"dev": "npm run build:dev && npm run watch", "dev:build": "webpack --mode=development --config webpack.config.cjs", "dev:watch": "webpack --mode=development --config webpack.config.cjs --watch", "dev:reload": "npm run dev:build && echo '🔄 Extension rebuilt - please reload in Chrome'", "build:dev": "npm run dev:build", "build:prod": "webpack --mode=production --config webpack.config.cjs", "watch": "npm-watch", "clean": "<PERSON><PERSON><PERSON> dist", "validate:dev": "echo '✅ Development build validated'", "validate:prod": "echo '✅ Production build validated'", "size:dev": "echo '📊 Development bundle sizes:' && du -h dist/**/*-bundle.js 2>/dev/null || echo 'No bundles found'", "size:prod": "echo '📊 Production bundle sizes:' && du -h dist/**/*-bundle.js 2>/dev/null || echo 'No bundles found'", "info": "echo '📋 Available commands:' && echo '  dev:build    - Build development version' && echo '  build:prod   - Build production version' && echo '  dev:watch    - Watch and rebuild on changes' && echo '  dev:reload   - Quick rebuild with reload reminder' && echo '  size:dev     - Show development bundle sizes' && echo '  size:prod    - Show production bundle sizes'"}, "watch": {"src/**/*.js": {"patterns": ["src"], "extensions": "js", "quiet": false, "runOnChangeOnly": true}}, "keywords": ["chrome-extension", "websocket", "interceptor", "ctrip", "im", "auto-reply"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"@babel/core": "^7.26.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/preset-env": "^7.26.0", "babel-loader": "^9.2.1", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^12.0.2", "css-loader": "^7.1.2", "npm-watch": "^0.13.0", "rimraf": "^6.0.1", "style-loader": "^4.0.0", "webpack": "^5.99.9", "webpack-cli": "^5.1.4"}}