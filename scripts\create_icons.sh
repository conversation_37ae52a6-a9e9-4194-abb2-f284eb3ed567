#!/bin/bash
# 创建简单的单色图标 (需要ImageMagick)
# 安装: brew install imagemagick (macOS) 或 apt-get install imagemagick (Ubuntu)

echo "正在创建简单图标..."

# 确保目录存在
mkdir -p src/assets/icons

# 创建16x16图标
convert -size 16x16 xc:"#667eea" -fill white -pointsize 10 -gravity center -annotate +0+0 "C" src/assets/icons/icon16.png

# 创建48x48图标  
convert -size 48x48 xc:"#667eea" -fill white -pointsize 24 -gravity center -annotate +0+0 "携" src/assets/icons/icon48.png

# 创建128x128图标
convert -size 128x128 xc:"#667eea" -fill white -pointsize 48 -gravity center -annotate +0+0 "携程" src/assets/icons/icon128.png

echo "图标创建完成！"
ls -la src/assets/icons/

echo "图标文件已保存到 src/assets/icons/ 目录" 