/**
 * 自动回复引擎
 * 负责处理自动回复逻辑和模板管理
 */
import { EventEmitter } from '../utils/event-emitter.js';
import { backgroundLogger } from '../utils/logger.js';

export class AutoReplyEngine extends EventEmitter {
  constructor() {
    super();
    this.logger = backgroundLogger;
    this.config = {
      enabled: false,
      mode: 'smart',
      delay: { min: 2000, max: 8000 },
      maxRepliesPerUser: 10,
      maxRepliesPerHour: 50,
      workingHours: {
        enabled: false,
        start: 9,
        end: 18
      },
      replyRate: 0.8,
      avoidDetection: true,
      personalization: true,
      duplicateCheck: true,
      contextWindow: 300000,
      keywords: { exclude: ['测试', 'test'], required: [] },
      blacklist: [],
      whitelist: []
    };
    this.templates = new Map();
    this.userStats = new Map();
    this.replyHistory = [];
    this.contextCache = new Map();
    this.initialized = false;
  }

  /**
   * 初始化自动回复引擎
   */
  async initialize() {
    if (this.initialized) {
      this.logger.warn('自动回复引擎已经初始化');
      return;
    }

    try {
      this.logger.info('初始化自动回复引擎...');
      
      // 加载配置
      await this.loadConfig();
      
      // 加载模板
      await this.loadTemplates();
      
      // 启动清理定时器
      this.startCleanupTimer();
      
      this.initialized = true;
      this.logger.info('自动回复引擎初始化完成');
    } catch (error) {
      this.logger.exception(error, '初始化失败');
      throw error;
    }
  }

  /**
   * 处理消息并生成回复
   */
  async processMessage(messageData) {
    if (!this.initialized || !this.config.enabled) {
      return null;
    }

    try {
      const { parsed, connectionId, direction } = messageData;
      
      // 只处理接收的消息
      if (direction !== 'received') {
        return null;
      }

      // 检查是否应该回复
      const shouldReply = await this.shouldReply(messageData);
      if (!shouldReply) {
        return null;
      }

      // 生成回复
      const reply = await this.generateReply(messageData);
      if (!reply) {
        return null;
      }

      // 记录回复
      this.recordReply(messageData, reply);

      // 延迟发送回复
      const delay = this.calculateDelay();
      setTimeout(() => {
        this.sendReply(connectionId, reply);
      }, delay);

      return reply;
    } catch (error) {
      this.logger.exception(error, '处理消息失败');
      return null;
    }
  }

  /**
   * 判断是否应该回复
   */
  async shouldReply(messageData) {
    const { parsed, connectionId } = messageData;
    
    // 检查工作时间
    if (!this.isWorkingHours()) {
      this.logger.debug('不在工作时间内，跳过回复');
      return false;
    }

    // 检查回复率
    if (Math.random() > this.config.replyRate) {
      this.logger.debug('回复率检查失败，跳过回复');
      return false;
    }

    // 检查用户限制
    if (!this.checkUserLimits(messageData)) {
      this.logger.debug('用户限制检查失败，跳过回复');
      return false;
    }

    // 检查全局限制
    if (!this.checkGlobalLimits()) {
      this.logger.debug('全局限制检查失败，跳过回复');
      return false;
    }

    // 检查关键词
    if (!this.checkKeywords(parsed.content)) {
      this.logger.debug('关键词检查失败，跳过回复');
      return false;
    }

    // 检查黑名单
    if (this.isBlacklisted(messageData)) {
      this.logger.debug('用户在黑名单中，跳过回复');
      return false;
    }

    // 检查重复消息
    if (this.config.duplicateCheck && this.isDuplicateMessage(messageData)) {
      this.logger.debug('重复消息检查失败，跳过回复');
      return false;
    }

    return true;
  }

  /**
   * 生成回复
   */
  async generateReply(messageData) {
    const { parsed } = messageData;
    const content = parsed.content;
    
    try {
      // 根据模式选择回复策略
      switch (this.config.mode) {
        case 'smart':
          return await this.generateSmartReply(messageData);
        case 'template':
          return await this.generateTemplateReply(messageData);
        case 'keyword':
          return await this.generateKeywordReply(messageData);
        default:
          return await this.generateDefaultReply(messageData);
      }
    } catch (error) {
      this.logger.exception(error, '生成回复失败');
      return null;
    }
  }

  /**
   * 生成智能回复
   */
  async generateSmartReply(messageData) {
    const { parsed } = messageData;
    const content = parsed.content;
    
    // 分析消息内容
    const analysis = this.analyzeMessage(content);
    
    // 根据分析结果选择回复策略
    if (analysis.isQuestion) {
      return this.getQuestionReply(content, analysis);
    } else if (analysis.isGreeting) {
      return this.getGreetingReply(content, analysis);
    } else if (analysis.isComplaint) {
      return this.getComplaintReply(content, analysis);
    } else if (analysis.isInquiry) {
      return this.getInquiryReply(content, analysis);
    } else {
      return this.getGeneralReply(content, analysis);
    }
  }

  /**
   * 生成模板回复
   */
  async generateTemplateReply(messageData) {
    const templates = this.templates.get('general') || [];
    if (templates.length === 0) {
      return null;
    }

    const template = templates[Math.floor(Math.random() * templates.length)];
    return this.personalizeReply(template, messageData);
  }

  /**
   * 生成关键词回复
   */
  async generateKeywordReply(messageData) {
    const { parsed } = messageData;
    const content = parsed.content.toLowerCase();
    
    // 遍历关键词模板
    for (const [category, templates] of this.templates) {
      for (const template of templates) {
        if (template.keywords) {
          for (const keyword of template.keywords) {
            if (content.includes(keyword.toLowerCase())) {
              return this.personalizeReply(template.content, messageData);
            }
          }
        }
      }
    }
    
    return null;
  }

  /**
   * 生成默认回复
   */
  async generateDefaultReply(messageData) {
    const defaultReplies = [
      '感谢您的咨询，我们会尽快为您处理',
      '您好，我已收到您的消息，稍后回复',
      '谢谢您的反馈，我们会认真处理',
      '收到您的消息，我们会及时跟进'
    ];
    
    const reply = defaultReplies[Math.floor(Math.random() * defaultReplies.length)];
    return this.personalizeReply(reply, messageData);
  }

  /**
   * 分析消息内容
   */
  analyzeMessage(content) {
    const analysis = {
      isQuestion: false,
      isGreeting: false,
      isComplaint: false,
      isInquiry: false,
      sentiment: 'neutral',
      keywords: [],
      urgency: 'normal'
    };

    // 问句检测
    const questionPatterns = [
      /[？?]/, /怎么/, /如何/, /什么/, /哪里/, /何时/, /为什么/
    ];
    analysis.isQuestion = questionPatterns.some(pattern => pattern.test(content));

    // 问候语检测
    const greetingPatterns = [
      /你好/, /您好/, /早上好/, /下午好/, /晚上好/, /hi/, /hello/
    ];
    analysis.isGreeting = greetingPatterns.some(pattern => pattern.test(content));

    // 投诉检测
    const complaintPatterns = [
      /投诉/, /不满/, /问题/, /故障/, /错误/, /bug/, /差评/
    ];
    analysis.isComplaint = complaintPatterns.some(pattern => pattern.test(content));

    // 询问检测
    const inquiryPatterns = [
      /咨询/, /了解/, /请问/, /询问/, /想知道/
    ];
    analysis.isInquiry = inquiryPatterns.some(pattern => pattern.test(content));

    // 情感分析
    const positivePatterns = [/谢谢/, /感谢/, /好的/, /满意/, /不错/];
    const negativePatterns = [/不好/, /差/, /垃圾/, /失望/, /愤怒/];
    
    if (positivePatterns.some(pattern => pattern.test(content))) {
      analysis.sentiment = 'positive';
    } else if (negativePatterns.some(pattern => pattern.test(content))) {
      analysis.sentiment = 'negative';
    }

    // 紧急程度分析
    const urgentPatterns = [/紧急/, /急/, /马上/, /立即/, /尽快/];
    if (urgentPatterns.some(pattern => pattern.test(content))) {
      analysis.urgency = 'high';
    }

    return analysis;
  }

  /**
   * 获取问题回复
   */
  getQuestionReply(content, analysis) {
    const questionReplies = [
      '这是一个很好的问题，让我为您详细解答',
      '关于您的问题，我需要一些时间来查找相关信息',
      '您的问题我已经记录，稍后会有专业人员回复',
      '感谢您的提问，我会尽快为您找到答案'
    ];
    
    return questionReplies[Math.floor(Math.random() * questionReplies.length)];
  }

  /**
   * 获取问候回复
   */
  getGreetingReply(content, analysis) {
    const greetingReplies = [
      '您好！很高兴为您服务',
      '您好！有什么可以帮您的吗？',
      '您好！欢迎咨询',
      '您好！我是您的专属客服'
    ];
    
    return greetingReplies[Math.floor(Math.random() * greetingReplies.length)];
  }

  /**
   * 获取投诉回复
   */
  getComplaintReply(content, analysis) {
    const complaintReplies = [
      '非常抱歉给您带来了不便，我们会认真处理您的问题',
      '感谢您的反馈，我们会立即调查并改进',
      '对于您遇到的问题，我深表歉意，我们会尽快解决',
      '您的意见非常重要，我们会及时跟进处理'
    ];
    
    return complaintReplies[Math.floor(Math.random() * complaintReplies.length)];
  }

  /**
   * 获取询问回复
   */
  getInquiryReply(content, analysis) {
    const inquiryReplies = [
      '感谢您的咨询，我很乐意为您提供帮助',
      '您想了解什么信息？我会详细为您介绍',
      '您的咨询我已收到，让我为您详细解答',
      '关于您的咨询，我会提供最准确的信息'
    ];
    
    return inquiryReplies[Math.floor(Math.random() * inquiryReplies.length)];
  }

  /**
   * 获取一般回复
   */
  getGeneralReply(content, analysis) {
    const generalReplies = [
      '收到您的消息，我会尽快处理',
      '感谢您的反馈，我们会认真对待',
      '您的消息我已收到，稍后回复',
      '谢谢您的沟通，我们会及时跟进'
    ];
    
    return generalReplies[Math.floor(Math.random() * generalReplies.length)];
  }

  /**
   * 个性化回复
   */
  personalizeReply(template, messageData) {
    if (!this.config.personalization) {
      return template;
    }

    // 添加时间相关的个性化
    const hour = new Date().getHours();
    let timeGreeting = '';
    if (hour < 12) {
      timeGreeting = '早上好';
    } else if (hour < 18) {
      timeGreeting = '下午好';
    } else {
      timeGreeting = '晚上好';
    }

    // 简单的模板替换
    return template
      .replace(/\{time\}/g, timeGreeting)
      .replace(/\{timestamp\}/g, new Date().toLocaleTimeString());
  }

  /**
   * 检查是否在工作时间
   */
  isWorkingHours() {
    if (!this.config.workingHours.enabled) {
      return true;
    }

    const now = new Date();
    const hour = now.getHours();
    const { start, end } = this.config.workingHours;
    
    return hour >= start && hour < end;
  }

  /**
   * 检查用户限制
   */
  checkUserLimits(messageData) {
    const userId = this.extractUserId(messageData);
    if (!userId) return true;

    const userStat = this.userStats.get(userId) || {
      replyCount: 0,
      lastReply: 0,
      dailyCount: 0,
      lastDaily: 0
    };

    // 检查每用户回复限制
    if (userStat.replyCount >= this.config.maxRepliesPerUser) {
      return false;
    }

    // 检查每日限制
    const now = Date.now();
    if (now - userStat.lastDaily > 24 * 60 * 60 * 1000) {
      userStat.dailyCount = 0;
      userStat.lastDaily = now;
    }

    if (userStat.dailyCount >= this.config.maxRepliesPerUser) {
      return false;
    }

    return true;
  }

  /**
   * 检查全局限制
   */
  checkGlobalLimits() {
    const now = Date.now();
    const oneHourAgo = now - 60 * 60 * 1000;
    
    // 统计最近一小时的回复数
    const recentReplies = this.replyHistory.filter(
      reply => reply.timestamp > oneHourAgo
    ).length;

    return recentReplies < this.config.maxRepliesPerHour;
  }

  /**
   * 检查关键词
   */
  checkKeywords(content) {
    const lowerContent = content.toLowerCase();
    
    // 检查排除关键词
    for (const keyword of this.config.keywords.exclude) {
      if (lowerContent.includes(keyword.toLowerCase())) {
        return false;
      }
    }
    
    // 检查必需关键词
    if (this.config.keywords.required.length > 0) {
      return this.config.keywords.required.some(keyword =>
        lowerContent.includes(keyword.toLowerCase())
      );
    }
    
    return true;
  }

  /**
   * 检查是否在黑名单
   */
  isBlacklisted(messageData) {
    const userId = this.extractUserId(messageData);
    return userId && this.config.blacklist.includes(userId);
  }

  /**
   * 检查是否是重复消息
   */
  isDuplicateMessage(messageData) {
    const content = messageData.parsed.content;
    const now = Date.now();
    
    // 检查最近的消息历史
    const recentMessages = this.replyHistory.filter(
      reply => now - reply.timestamp < this.config.contextWindow
    );
    
    return recentMessages.some(reply => reply.originalMessage === content);
  }

  /**
   * 计算延迟时间
   */
  calculateDelay() {
    const { min, max } = this.config.delay;
    const baseDelay = min + Math.random() * (max - min);
    
    // 如果启用了避免检测，添加随机变化
    if (this.config.avoidDetection) {
      const variation = baseDelay * 0.2 * (Math.random() - 0.5);
      return Math.max(min, baseDelay + variation);
    }
    
    return baseDelay;
  }

  /**
   * 记录回复
   */
  recordReply(messageData, reply) {
    const userId = this.extractUserId(messageData);
    const replyRecord = {
      id: this.generateId(),
      userId,
      originalMessage: messageData.parsed.content,
      reply,
      timestamp: Date.now(),
      connectionId: messageData.connectionId
    };

    this.replyHistory.push(replyRecord);
    
    // 更新用户统计
    if (userId) {
      const userStat = this.userStats.get(userId) || {
        replyCount: 0,
        lastReply: 0,
        dailyCount: 0,
        lastDaily: 0
      };
      
      userStat.replyCount++;
      userStat.dailyCount++;
      userStat.lastReply = Date.now();
      
      this.userStats.set(userId, userStat);
    }

    // 发射事件
    this.emit('reply-generated', replyRecord);
  }

  /**
   * 发送回复
   */
  sendReply(connectionId, reply) {
    this.emit('send-reply', {
      connectionId,
      message: reply,
      timestamp: Date.now()
    });
    
    this.logger.info('自动回复已发送:', { connectionId, reply });
  }

  /**
   * 提取用户ID
   */
  extractUserId(messageData) {
    // 从消息元数据中提取用户ID
    const metadata = messageData.parsed.metadata;
    return metadata.from || metadata.userId || metadata.user || null;
  }

  /**
   * 加载配置
   */
  async loadConfig() {
    try {
      const result = await chrome.storage.local.get(['autoReplyConfig']);
      if (result.autoReplyConfig) {
        this.config = { ...this.config, ...result.autoReplyConfig };
      }
    } catch (error) {
      this.logger.exception(error, '加载配置失败');
    }
  }

  /**
   * 保存配置
   */
  async saveConfig() {
    try {
      await chrome.storage.local.set({ autoReplyConfig: this.config });
    } catch (error) {
      this.logger.exception(error, '保存配置失败');
    }
  }

  /**
   * 加载模板
   */
  async loadTemplates() {
    try {
      const result = await chrome.storage.local.get(['replyTemplates']);
      if (result.replyTemplates) {
        this.templates = new Map(result.replyTemplates);
      }
    } catch (error) {
      this.logger.exception(error, '加载模板失败');
    }
  }

  /**
   * 保存模板
   */
  async saveTemplates() {
    try {
      await chrome.storage.local.set({
        replyTemplates: Array.from(this.templates.entries())
      });
    } catch (error) {
      this.logger.exception(error, '保存模板失败');
    }
  }

  /**
   * 启动清理定时器
   */
  startCleanupTimer() {
    // 每小时清理一次过期数据
    setInterval(() => {
      this.cleanupExpiredData();
    }, 60 * 60 * 1000);
  }

  /**
   * 清理过期数据
   */
  cleanupExpiredData() {
    const now = Date.now();
    const expiredThreshold = 24 * 60 * 60 * 1000; // 24小时
    
    // 清理过期的回复历史
    this.replyHistory = this.replyHistory.filter(
      reply => now - reply.timestamp < expiredThreshold
    );
    
    // 清理过期的用户统计
    for (const [userId, userStat] of this.userStats) {
      if (now - userStat.lastReply > expiredThreshold) {
        this.userStats.delete(userId);
      }
    }
    
    // 清理过期的上下文缓存
    for (const [key, data] of this.contextCache) {
      if (now - data.timestamp > this.config.contextWindow) {
        this.contextCache.delete(key);
      }
    }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.saveConfig();
    this.logger.info('配置已更新');
  }

  /**
   * 获取统计信息
   */
  getStats() {
    const now = Date.now();
    const oneHourAgo = now - 60 * 60 * 1000;
    
    return {
      totalReplies: this.replyHistory.length,
      recentReplies: this.replyHistory.filter(r => r.timestamp > oneHourAgo).length,
      activeUsers: this.userStats.size,
      templatesCount: this.templates.size,
      enabled: this.config.enabled,
      mode: this.config.mode
    };
  }

  /**
   * 生成ID
   */
  generateId() {
    return Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 销毁引擎
   */
  destroy() {
    this.logger.info('销毁自动回复引擎');
    
    this.userStats.clear();
    this.replyHistory = [];
    this.contextCache.clear();
    this.templates.clear();
    this.removeAllListeners();
    
    this.initialized = false;
  }
} 