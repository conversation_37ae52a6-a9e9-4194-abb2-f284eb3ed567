/**
 * @fileoverview 定时任务管理器 - 管理和执行定时任务
 * <AUTHOR> Extension Developer
 */

import { configManager } from '../utils/config.js';
import { ALARM_NAMES, STORAGE_KEYS, TIME_CONSTANTS } from '../utils/constants.js';
import { alarmLogger } from '../utils/logger.js';

/**
 * 定时任务管理器类
 */
class AlarmManager {
  /**
   * 构造函数
   */
  constructor() {
    this.logger = alarmLogger;
    this.initialized = false;
    this.alarmHandlers = new Map();
    this.stats = {
      totalExecutions: 0,
      successfulExecutions: 0,
      failedExecutions: 0,
      lastExecution: null
    };
  }

  /**
   * 初始化定时任务管理器
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      if (this.initialized) {
        return;
      }

      this.logger.info('正在初始化定时任务管理器...');
      
      // 注册任务处理器
      this.registerAlarmHandlers();
      
      // 设置定时任务
      await this.setupAlarms();
      
      // 设置监听器
      this.setupAlarmListeners();
      
      this.initialized = true;
      this.logger.info('定时任务管理器初始化完成');
      
    } catch (error) {
      this.logger.exception(error, '定时任务管理器初始化失败');
      throw error;
    }
  }

  /**
   * 注册任务处理器
   */
  registerAlarmHandlers() {
    this.alarmHandlers.set(ALARM_NAMES.CLEANUP_EXPIRED_DATA, this.cleanupExpiredData.bind(this));
    this.alarmHandlers.set(ALARM_NAMES.SYNC_STATISTICS, this.syncStatistics.bind(this));
    this.alarmHandlers.set(ALARM_NAMES.AUTO_EXPORT, this.handleAutoExport.bind(this));
    
    this.logger.debug('任务处理器已注册:', Array.from(this.alarmHandlers.keys()));
  }

  /**
   * 设置定时任务
   * @returns {Promise<void>}
   */
  async setupAlarms() {
    try {
      // 清理过期数据的定时任务
      await this.createAlarm(ALARM_NAMES.CLEANUP_EXPIRED_DATA, {
        delayInMinutes: 1,
        periodInMinutes: TIME_CONSTANTS.CLEANUP_INTERVAL
      });
      
      // 统计信息同步任务
      await this.createAlarm(ALARM_NAMES.SYNC_STATISTICS, {
        delayInMinutes: 5,
        periodInMinutes: TIME_CONSTANTS.SYNC_INTERVAL
      });
      
      // 自动导出任务
      await this.createAlarm(ALARM_NAMES.AUTO_EXPORT, {
        delayInMinutes: 60,
        periodInMinutes: TIME_CONSTANTS.EXPORT_INTERVAL
      });
      
      this.logger.info('定时任务已设置');
    } catch (error) {
      this.logger.exception(error, '设置定时任务失败');
    }
  }

  /**
   * 创建定时任务
   * @param {string} name - 任务名称
   * @param {Object} options - 任务选项
   * @returns {Promise<void>}
   */
  async createAlarm(name, options) {
    try {
      // 先清除现有的相同名称的任务
      await chrome.alarms.clear(name);
      
      // 创建新任务
      await chrome.alarms.create(name, options);
      
      this.logger.debug(`定时任务已创建: ${name}`, options);
    } catch (error) {
      this.logger.exception(error, `创建定时任务失败: ${name}`);
      throw error;
    }
  }

  /**
   * 设置任务监听器
   */
  setupAlarmListeners() {
    chrome.alarms.onAlarm.addListener(async (alarm) => {
      await this.handleAlarm(alarm);
    });
  }

  /**
   * 处理定时任务
   * @param {Object} alarm - 任务对象
   * @returns {Promise<void>}
   */
  async handleAlarm(alarm) {
    const timer = this.logger.createTimer(`执行定时任务: ${alarm.name}`);
    
    try {
      this.logger.debug('定时任务触发:', alarm.name);
      
      // 更新统计
      this.stats.totalExecutions++;
      this.stats.lastExecution = Date.now();
      
      // 获取处理器
      const handler = this.alarmHandlers.get(alarm.name);
      
      if (handler) {
        await handler(alarm);
        this.stats.successfulExecutions++;
        this.logger.info(`定时任务执行成功: ${alarm.name}`);
      } else {
        this.logger.warn(`未找到定时任务处理器: ${alarm.name}`);
        this.stats.failedExecutions++;
      }
      
    } catch (error) {
      this.logger.exception(error, `定时任务执行失败: ${alarm.name}`);
      this.stats.failedExecutions++;
    } finally {
      timer();
    }
  }

  /**
   * 清理过期数据
   * @returns {Promise<void>}
   */
  async cleanupExpiredData() {
    try {
      const now = Date.now();
      const cutoffTime = now - TIME_CONSTANTS.WEEK; // 7天前
      
      // 获取需要清理的数据
      const dataToClean = await chrome.storage.local.get([
        STORAGE_KEYS.AUTO_REPLY_STATS,
        STORAGE_KEYS.MESSAGE_HISTORY,
        STORAGE_KEYS.CTRIP_ACTIVITY
      ]);
      
      let cleanedItems = 0;
      
      // 清理过期的回复统计
      if (dataToClean[STORAGE_KEYS.AUTO_REPLY_STATS] && 
          dataToClean[STORAGE_KEYS.AUTO_REPLY_STATS].dailyStats) {
        const originalLength = dataToClean[STORAGE_KEYS.AUTO_REPLY_STATS].dailyStats.length;
        
        const filteredStats = dataToClean[STORAGE_KEYS.AUTO_REPLY_STATS].dailyStats.filter(
          ([date, count]) => new Date(date).getTime() > cutoffTime
        );
        
        if (filteredStats.length !== originalLength) {
          dataToClean[STORAGE_KEYS.AUTO_REPLY_STATS].dailyStats = filteredStats;
          await chrome.storage.local.set({ 
            [STORAGE_KEYS.AUTO_REPLY_STATS]: dataToClean[STORAGE_KEYS.AUTO_REPLY_STATS] 
          });
          cleanedItems += originalLength - filteredStats.length;
        }
      }
      
      // 清理过期的消息历史
      if (dataToClean[STORAGE_KEYS.MESSAGE_HISTORY] && 
          dataToClean[STORAGE_KEYS.MESSAGE_HISTORY].length > 0) {
        const originalLength = dataToClean[STORAGE_KEYS.MESSAGE_HISTORY].length;
        
        const filteredHistory = dataToClean[STORAGE_KEYS.MESSAGE_HISTORY].filter(
          msg => msg.timestamp > cutoffTime
        );
        
        if (filteredHistory.length !== originalLength) {
          await chrome.storage.local.set({ 
            [STORAGE_KEYS.MESSAGE_HISTORY]: filteredHistory 
          });
          cleanedItems += originalLength - filteredHistory.length;
        }
      }
      
      // 清理过期的活动记录
      if (dataToClean[STORAGE_KEYS.CTRIP_ACTIVITY] && 
          dataToClean[STORAGE_KEYS.CTRIP_ACTIVITY].length > 0) {
        const originalLength = dataToClean[STORAGE_KEYS.CTRIP_ACTIVITY].length;
        
        const filteredActivity = dataToClean[STORAGE_KEYS.CTRIP_ACTIVITY].filter(
          activity => activity.timestamp > cutoffTime
        );
        
        if (filteredActivity.length !== originalLength) {
          await chrome.storage.local.set({ 
            [STORAGE_KEYS.CTRIP_ACTIVITY]: filteredActivity 
          });
          cleanedItems += originalLength - filteredActivity.length;
        }
      }
      
      if (cleanedItems > 0) {
        this.logger.info(`过期数据清理完成，清理了 ${cleanedItems} 条记录`);
      } else {
        this.logger.debug('没有过期数据需要清理');
      }
      
    } catch (error) {
      this.logger.exception(error, '清理过期数据失败');
      throw error;
    }
  }

  /**
   * 同步统计信息
   * @returns {Promise<void>}
   */
  async syncStatistics() {
    try {
      // 获取当前统计信息
      const currentStats = await configManager.getConfig(STORAGE_KEYS.STATISTICS);
      
      // 获取额外的统计数据
      const additionalData = await chrome.storage.local.get([
        STORAGE_KEYS.AUTO_REPLY_STATS,
        STORAGE_KEYS.MESSAGE_HISTORY
      ]);
      
      // 计算新的统计信息
      const updatedStats = {
        ...currentStats,
        lastSync: Date.now(),
        alarmStats: {
          ...this.stats
        }
      };
      
      // 如果有消息历史，计算相关统计
      if (additionalData[STORAGE_KEYS.MESSAGE_HISTORY]) {
        const messageHistory = additionalData[STORAGE_KEYS.MESSAGE_HISTORY];
        const recentMessages = messageHistory.filter(
          msg => msg.timestamp > (Date.now() - TIME_CONSTANTS.DAY)
        );
        
        updatedStats.dailyMessageCount = recentMessages.length;
        updatedStats.totalStoredMessages = messageHistory.length;
      }
      
      // 如果有自动回复统计，计算成功率
      if (additionalData[STORAGE_KEYS.AUTO_REPLY_STATS]) {
        const autoReplyStats = additionalData[STORAGE_KEYS.AUTO_REPLY_STATS];
        if (autoReplyStats.totalReplies > 0) {
          updatedStats.successRate = 
            (autoReplyStats.successfulReplies || 0) / autoReplyStats.totalReplies;
        }
      }
      
      // 保存更新后的统计信息
      await configManager.setConfig(STORAGE_KEYS.STATISTICS, updatedStats);
      
      this.logger.debug('统计信息同步完成');
      
    } catch (error) {
      this.logger.exception(error, '同步统计信息失败');
      throw error;
    }
  }

  /**
   * 处理自动导出
   * @returns {Promise<void>}
   */
  async handleAutoExport() {
    try {
      const interceptorConfig = await configManager.getConfig(STORAGE_KEYS.INTERCEPTOR_CONFIG);
      
      if (interceptorConfig && interceptorConfig.autoExport) {
        // 获取所有携程相关的标签页
        const tabs = await chrome.tabs.query({ url: '*://*.ctrip.com/*' });
        
        let exportedCount = 0;
        
        for (const tab of tabs) {
          try {
            await chrome.tabs.sendMessage(tab.id, {
              action: 'autoExport',
              timestamp: Date.now()
            });
            exportedCount++;
          } catch (error) {
            this.logger.debug(`无法向标签页 ${tab.id} 发送导出消息:`, error.message);
          }
        }
        
        this.logger.info(`自动导出任务已执行，通知了 ${exportedCount} 个标签页`);
      } else {
        this.logger.debug('自动导出功能未启用');
      }
      
    } catch (error) {
      this.logger.exception(error, '自动导出失败');
      throw error;
    }
  }

  /**
   * 获取所有定时任务
   * @returns {Promise<Array>} 定时任务列表
   */
  async getAllAlarms() {
    try {
      const alarms = await chrome.alarms.getAll();
      return alarms.map(alarm => ({
        name: alarm.name,
        scheduledTime: alarm.scheduledTime,
        periodInMinutes: alarm.periodInMinutes
      }));
    } catch (error) {
      this.logger.exception(error, '获取定时任务失败');
      return [];
    }
  }

  /**
   * 获取任务统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      ...this.stats,
      successRate: this.stats.totalExecutions > 0 ? 
        this.stats.successfulExecutions / this.stats.totalExecutions : 0,
      isInitialized: this.initialized,
      registeredHandlers: Array.from(this.alarmHandlers.keys())
    };
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.stats = {
      totalExecutions: 0,
      successfulExecutions: 0,
      failedExecutions: 0,
      lastExecution: null
    };
    
    this.logger.info('定时任务统计信息已重置');
  }

  /**
   * 清除所有定时任务
   * @returns {Promise<void>}
   */
  async clearAllAlarms() {
    try {
      await chrome.alarms.clearAll();
      this.logger.info('所有定时任务已清除');
    } catch (error) {
      this.logger.exception(error, '清除定时任务失败');
      throw error;
    }
  }

  /**
   * 清除指定的定时任务
   * @param {string} name - 任务名称
   * @returns {Promise<boolean>} 是否成功清除
   */
  async clearAlarm(name) {
    try {
      const wasCleared = await chrome.alarms.clear(name);
      if (wasCleared) {
        this.logger.info(`定时任务已清除: ${name}`);
      } else {
        this.logger.warn(`定时任务不存在: ${name}`);
      }
      return wasCleared;
    } catch (error) {
      this.logger.exception(error, `清除定时任务失败: ${name}`);
      return false;
    }
  }

  /**
   * 重新设置定时任务
   * @returns {Promise<void>}
   */
  async resetAlarms() {
    try {
      this.logger.info('正在重新设置定时任务...');
      
      // 清除所有现有任务
      await this.clearAllAlarms();
      
      // 重新设置任务
      await this.setupAlarms();
      
      this.logger.info('定时任务已重新设置');
      
    } catch (error) {
      this.logger.exception(error, '重新设置定时任务失败');
      throw error;
    }
  }

  /**
   * 更新任务配置
   * @param {string} name - 任务名称
   * @param {Object} options - 新的任务选项
   * @returns {Promise<void>}
   */
  async updateAlarm(name, options) {
    try {
      await this.createAlarm(name, options);
      this.logger.info(`定时任务已更新: ${name}`);
    } catch (error) {
      this.logger.exception(error, `更新定时任务失败: ${name}`);
      throw error;
    }
  }

  /**
   * 手动触发任务
   * @param {string} name - 任务名称
   * @returns {Promise<void>}
   */
  async triggerAlarm(name) {
    try {
      const handler = this.alarmHandlers.get(name);
      
      if (handler) {
        this.logger.info(`手动触发定时任务: ${name}`);
        await handler({ name, manual: true });
      } else {
        throw new Error(`未找到任务处理器: ${name}`);
      }
    } catch (error) {
      this.logger.exception(error, `手动触发任务失败: ${name}`);
      throw error;
    }
  }

  /**
   * 确保已初始化
   * @returns {Promise<void>}
   */
  async ensureInitialized() {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  /**
   * 销毁管理器
   * @returns {Promise<void>}
   */
  async destroy() {
    try {
      await this.clearAllAlarms();
      this.alarmHandlers.clear();
      this.initialized = false;
      this.logger.info('定时任务管理器已销毁');
    } catch (error) {
      this.logger.exception(error, '销毁定时任务管理器失败');
    }
  }
}

/**
 * 定时任务管理器实例
 */
export const alarmManager = new AlarmManager();

/**
 * 导出定时任务管理器类
 */
export default AlarmManager; 