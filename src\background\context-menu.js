/**
 * @fileoverview 上下文菜单管理器 - 管理右键菜单
 * <AUTHOR> Extension Developer
 */

import { configManager } from '../utils/config.js';
import { CONTEXT_MENU_IDS, STORAGE_KEYS, URL_PATTERNS } from '../utils/constants.js';
import { contextMenuLogger } from '../utils/logger.js';
import { notificationManager } from './notification-manager.js';

/**
 * 上下文菜单管理器类
 */
class ContextMenuManager {
  /**
   * 构造函数
   */
  constructor() {
    this.logger = contextMenuLogger;
    this.initialized = false;
    this.menuItems = new Map();
    this.stats = {
      totalClicks: 0,
      menuItemClicks: new Map(),
      lastClick: null
    };
  }

  /**
   * 初始化上下文菜单管理器
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      if (this.initialized) {
        return;
      }

      this.logger.info('正在初始化上下文菜单管理器...');
      
      // 检查contextMenus API是否可用
      if (!chrome.contextMenus) {
        this.logger.warn('contextMenus API不可用，跳过上下文菜单设置');
        return;
      }
      
      // 创建上下文菜单项
      await this.createContextMenus();
      
      // 设置菜单点击监听器
      this.setupMenuListeners();
      
      this.initialized = true;
      this.logger.info('上下文菜单管理器初始化完成');
      
    } catch (error) {
      this.logger.exception(error, '上下文菜单管理器初始化失败');
      throw error;
    }
  }

  /**
   * 创建上下文菜单项
   * @returns {Promise<void>}
   */
  async createContextMenus() {
    try {
      // 清除现有菜单项
      await this.clearAllMenus();
      
      // 创建主菜单项
      await this.createMainMenuItems();
      
      // 创建子菜单项
      await this.createSubMenuItems();
      
      this.logger.info('上下文菜单已创建');
      
    } catch (error) {
      this.logger.exception(error, '创建上下文菜单失败');
      throw error;
    }
  }

  /**
   * 创建主菜单项
   * @returns {Promise<void>}
   */
  async createMainMenuItems() {
    // 切换自动回复
    await this.createMenuItem({
      id: CONTEXT_MENU_IDS.TOGGLE_AUTO_REPLY,
      title: '切换自动回复',
      contexts: ['page'],
      documentUrlPatterns: URL_PATTERNS.CTRIP_SITES
    });

    // 导出消息历史
    await this.createMenuItem({
      id: CONTEXT_MENU_IDS.EXPORT_HISTORY,
      title: '导出消息历史',
      contexts: ['page'],
      documentUrlPatterns: URL_PATTERNS.CTRIP_SITES
    });

    // 打开设置
    await this.createMenuItem({
      id: CONTEXT_MENU_IDS.OPEN_SETTINGS,
      title: '打开设置',
      contexts: ['page'],
      documentUrlPatterns: URL_PATTERNS.CTRIP_SITES
    });
  }

  /**
   * 创建子菜单项
   * @returns {Promise<void>}
   */
  async createSubMenuItems() {
    // 创建分隔符
    await this.createMenuItem({
      id: 'separator-1',
      type: 'separator',
      contexts: ['page'],
      documentUrlPatterns: URL_PATTERNS.CTRIP_SITES
    });

    // 快速操作子菜单
    await this.createMenuItem({
      id: 'quick-actions',
      title: '快速操作',
      contexts: ['page'],
      documentUrlPatterns: URL_PATTERNS.CTRIP_SITES
    });

    // 清理数据
    await this.createMenuItem({
      id: 'clear-data',
      title: '清理数据',
      parentId: 'quick-actions',
      contexts: ['page'],
      documentUrlPatterns: URL_PATTERNS.CTRIP_SITES
    });

    // 重置统计
    await this.createMenuItem({
      id: 'reset-stats',
      title: '重置统计',
      parentId: 'quick-actions',
      contexts: ['page'],
      documentUrlPatterns: URL_PATTERNS.CTRIP_SITES
    });

    // 手动触发导出
    await this.createMenuItem({
      id: 'manual-export',
      title: '手动导出',
      parentId: 'quick-actions',
      contexts: ['page'],
      documentUrlPatterns: URL_PATTERNS.CTRIP_SITES
    });
  }

  /**
   * 创建菜单项
   * @param {Object} options - 菜单项选项
   * @returns {Promise<void>}
   */
  async createMenuItem(options) {
    try {
      return new Promise((resolve, reject) => {
        chrome.contextMenus.create(options, () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            this.menuItems.set(options.id, {
              ...options,
              createdAt: Date.now()
            });
            this.logger.debug(`菜单项已创建: ${options.id}`);
            resolve();
          }
        });
      });
    } catch (error) {
      this.logger.exception(error, `创建菜单项失败: ${options.id}`);
      throw error;
    }
  }

  /**
   * 设置菜单监听器
   */
  setupMenuListeners() {
    chrome.contextMenus.onClicked.addListener(async (info, tab) => {
      await this.handleMenuClick(info, tab);
    });
  }

  /**
   * 处理菜单点击
   * @param {Object} info - 点击信息
   * @param {Object} tab - 标签页信息
   * @returns {Promise<void>}
   */
  async handleMenuClick(info, tab) {
    const timer = this.logger.createTimer(`处理菜单点击: ${info.menuItemId}`);
    
    try {
      this.logger.debug('上下文菜单点击:', info.menuItemId);
      
      // 更新统计
      this.stats.totalClicks++;
      this.stats.lastClick = Date.now();
      
      const clickCount = this.stats.menuItemClicks.get(info.menuItemId) || 0;
      this.stats.menuItemClicks.set(info.menuItemId, clickCount + 1);
      
      // 处理不同的菜单项
      switch (info.menuItemId) {
        case CONTEXT_MENU_IDS.TOGGLE_AUTO_REPLY:
          await this.handleToggleAutoReply(tab);
          break;
          
        case CONTEXT_MENU_IDS.EXPORT_HISTORY:
          await this.handleExportHistory(tab);
          break;
          
        case CONTEXT_MENU_IDS.OPEN_SETTINGS:
          await this.handleOpenSettings();
          break;
          
        case 'clear-data':
          await this.handleClearData();
          break;
          
        case 'reset-stats':
          await this.handleResetStats();
          break;
          
        case 'manual-export':
          await this.handleManualExport(tab);
          break;
          
        default:
          this.logger.warn('未知的菜单项:', info.menuItemId);
      }
      
    } catch (error) {
      this.logger.exception(error, '处理菜单点击失败');
      await notificationManager.showErrorNotification(`菜单操作失败: ${error.message}`);
    } finally {
      timer();
    }
  }

  /**
   * 处理切换自动回复
   * @param {Object} tab - 标签页信息
   * @returns {Promise<void>}
   */
  async handleToggleAutoReply(tab) {
    try {
      const config = await configManager.getConfig(STORAGE_KEYS.AUTO_REPLY_CONFIG);
      const newEnabled = !config.enabled;
      
      await configManager.setConfig(STORAGE_KEYS.AUTO_REPLY_CONFIG, {
        ...config,
        enabled: newEnabled
      });
      
      // 通知内容脚本更新配置
      if (tab?.id) {
        try {
          await chrome.tabs.sendMessage(tab.id, {
            action: 'updateAutoReplyConfig',
            config: { ...config, enabled: newEnabled }
          });
        } catch (error) {
          this.logger.debug('无法向内容脚本发送消息:', error.message);
        }
      }
      
      // 显示通知
      await notificationManager.showAutoReplyStatusNotification(newEnabled);
      
      this.logger.info(`自动回复已${newEnabled ? '启用' : '禁用'}`);
      
    } catch (error) {
      this.logger.exception(error, '切换自动回复失败');
      throw error;
    }
  }

  /**
   * 处理导出历史
   * @param {Object} tab - 标签页信息
   * @returns {Promise<void>}
   */
  async handleExportHistory(tab) {
    try {
      if (tab?.id) {
        await chrome.tabs.sendMessage(tab.id, {
          action: 'exportHistory'
        });
      }
      
      await notificationManager.showInfoNotification('消息历史导出已开始');
      
    } catch (error) {
      this.logger.exception(error, '导出历史失败');
      throw error;
    }
  }

  /**
   * 处理打开设置
   * @returns {Promise<void>}
   */
  async handleOpenSettings() {
    try {
      await chrome.tabs.create({
        url: chrome.runtime.getURL('popup.html'),
        active: true
      });
      
    } catch (error) {
      this.logger.exception(error, '打开设置失败');
      throw error;
    }
  }

  /**
   * 处理清理数据
   * @returns {Promise<void>}
   */
  async handleClearData() {
    try {
      // 清理历史数据
      await chrome.storage.local.remove([
        STORAGE_KEYS.MESSAGE_HISTORY,
        STORAGE_KEYS.CTRIP_ACTIVITY
      ]);
      
      await notificationManager.showSuccessNotification('数据已清理');
      
    } catch (error) {
      this.logger.exception(error, '清理数据失败');
      throw error;
    }
  }

  /**
   * 处理重置统计
   * @returns {Promise<void>}
   */
  async handleResetStats() {
    try {
      // 重置统计信息
      await configManager.resetConfig(STORAGE_KEYS.STATISTICS);
      
      // 重置本地统计
      this.stats = {
        totalClicks: 0,
        menuItemClicks: new Map(),
        lastClick: null
      };
      
      await notificationManager.showSuccessNotification('统计信息已重置');
      
    } catch (error) {
      this.logger.exception(error, '重置统计失败');
      throw error;
    }
  }

  /**
   * 处理手动导出
   * @param {Object} tab - 标签页信息
   * @returns {Promise<void>}
   */
  async handleManualExport(tab) {
    try {
      if (tab?.id) {
        await chrome.tabs.sendMessage(tab.id, {
          action: 'manualExport',
          timestamp: Date.now()
        });
      }
      
      await notificationManager.showInfoNotification('手动导出已开始');
      
    } catch (error) {
      this.logger.exception(error, '手动导出失败');
      throw error;
    }
  }

  /**
   * 更新菜单项
   * @param {string} id - 菜单项ID
   * @param {Object} updateProperties - 更新属性
   * @returns {Promise<void>}
   */
  async updateMenuItem(id, updateProperties) {
    try {
      return new Promise((resolve, reject) => {
        chrome.contextMenus.update(id, updateProperties, () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            const menuItem = this.menuItems.get(id);
            if (menuItem) {
              this.menuItems.set(id, {
                ...menuItem,
                ...updateProperties,
                updatedAt: Date.now()
              });
            }
            this.logger.debug(`菜单项已更新: ${id}`);
            resolve();
          }
        });
      });
    } catch (error) {
      this.logger.exception(error, `更新菜单项失败: ${id}`);
      throw error;
    }
  }

  /**
   * 移除菜单项
   * @param {string} id - 菜单项ID
   * @returns {Promise<void>}
   */
  async removeMenuItem(id) {
    try {
      return new Promise((resolve, reject) => {
        chrome.contextMenus.remove(id, () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            this.menuItems.delete(id);
            this.logger.debug(`菜单项已移除: ${id}`);
            resolve();
          }
        });
      });
    } catch (error) {
      this.logger.exception(error, `移除菜单项失败: ${id}`);
      throw error;
    }
  }

  /**
   * 清除所有菜单项
   * @returns {Promise<void>}
   */
  async clearAllMenus() {
    try {
      return new Promise((resolve, reject) => {
        chrome.contextMenus.removeAll(() => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            this.menuItems.clear();
            this.logger.debug('所有菜单项已清除');
            resolve();
          }
        });
      });
    } catch (error) {
      this.logger.exception(error, '清除所有菜单项失败');
      throw error;
    }
  }

  /**
   * 动态更新菜单状态
   * @returns {Promise<void>}
   */
  async updateMenuStatus() {
    try {
      const autoReplyConfig = await configManager.getConfig(STORAGE_KEYS.AUTO_REPLY_CONFIG);
      
      // 更新自动回复菜单项标题
      if (this.menuItems.has(CONTEXT_MENU_IDS.TOGGLE_AUTO_REPLY)) {
        const title = autoReplyConfig.enabled ? '禁用自动回复' : '启用自动回复';
        await this.updateMenuItem(CONTEXT_MENU_IDS.TOGGLE_AUTO_REPLY, { title });
      }
      
    } catch (error) {
      this.logger.exception(error, '更新菜单状态失败');
    }
  }

  /**
   * 获取菜单项列表
   * @returns {Array} 菜单项列表
   */
  getMenuItems() {
    return Array.from(this.menuItems.values());
  }

  /**
   * 获取菜单统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      ...this.stats,
      totalMenuItems: this.menuItems.size,
      menuItemClicks: Object.fromEntries(this.stats.menuItemClicks),
      isInitialized: this.initialized
    };
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.stats = {
      totalClicks: 0,
      menuItemClicks: new Map(),
      lastClick: null
    };
    
    this.logger.info('上下文菜单统计信息已重置');
  }

  /**
   * 启用/禁用菜单项
   * @param {string} id - 菜单项ID
   * @param {boolean} enabled - 是否启用
   * @returns {Promise<void>}
   */
  async setMenuItemEnabled(id, enabled) {
    try {
      await this.updateMenuItem(id, { enabled });
      this.logger.debug(`菜单项 ${id} 已${enabled ? '启用' : '禁用'}`);
    } catch (error) {
      this.logger.exception(error, `设置菜单项状态失败: ${id}`);
      throw error;
    }
  }

  /**
   * 检查菜单项是否存在
   * @param {string} id - 菜单项ID
   * @returns {boolean} 是否存在
   */
  hasMenuItem(id) {
    return this.menuItems.has(id);
  }

  /**
   * 重新创建菜单
   * @returns {Promise<void>}
   */
  async recreateMenus() {
    try {
      this.logger.info('正在重新创建上下文菜单...');
      
      await this.clearAllMenus();
      await this.createContextMenus();
      
      this.logger.info('上下文菜单已重新创建');
      
    } catch (error) {
      this.logger.exception(error, '重新创建菜单失败');
      throw error;
    }
  }

  /**
   * 确保已初始化
   * @returns {Promise<void>}
   */
  async ensureInitialized() {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  /**
   * 销毁上下文菜单管理器
   * @returns {Promise<void>}
   */
  async destroy() {
    try {
      await this.clearAllMenus();
      this.initialized = false;
      this.logger.info('上下文菜单管理器已销毁');
    } catch (error) {
      this.logger.exception(error, '销毁上下文菜单管理器失败');
    }
  }
}

/**
 * 上下文菜单管理器实例
 */
export const contextMenuManager = new ContextMenuManager();

/**
 * 导出上下文菜单管理器类
 */
export default ContextMenuManager; 