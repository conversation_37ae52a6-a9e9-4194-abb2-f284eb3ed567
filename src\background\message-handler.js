/**
 * @fileoverview 消息处理器 - 处理来自content script和popup的消息
 * <AUTHOR> Extension Developer
 */

import { configManager } from '../utils/config.js';
import { ERROR_CODES, MESSAGE_TYPES, STORAGE_KEYS, SUCCESS_MESSAGES } from '../utils/constants.js';
import { messageLogger } from '../utils/logger.js';

/**
 * 消息处理器类
 */
class MessageHandler {
  /**
   * 构造函数
   */
  constructor() {
    this.logger = messageLogger;
    this.initialized = false;
    this.activeConnections = new Map();
    this.messageQueue = [];
    this.isProcessing = false;
  }

  /**
   * 初始化消息处理器
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      if (this.initialized) {
        return;
      }

      this.logger.info('正在初始化消息处理器...');
      
      // 设置消息监听器
      this.setupMessageListeners();
      
      // 设置连接监听器
      this.setupConnectionListeners();
      
      this.initialized = true;
      this.logger.info('消息处理器初始化完成');
      
    } catch (error) {
      this.logger.exception(error, '消息处理器初始化失败');
      throw error;
    }
  }

  /**
   * 设置消息监听器
   */
  setupMessageListeners() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // 保持消息通道打开
    });
  }

  /**
   * 设置连接监听器
   */
  setupConnectionListeners() {
    chrome.runtime.onConnect.addListener((port) => {
      this.handleConnection(port);
    });
  }

  /**
   * 处理消息
   * @param {Object} request - 请求对象
   * @param {Object} sender - 发送者信息
   * @param {Function} sendResponse - 响应函数
   */
  async handleMessage(request, sender, sendResponse) {
    const startTime = performance.now();
    
    try {
      this.logger.debug('收到消息:', request.action || request.type, '来自:', sender.tab?.id || 'popup');
      
      // 确保初始化完成
      await this.ensureInitialized();
      
      // 获取消息类型
      const messageType = request.type || request.action;
      
      // 直接处理消息，不再等待
      const response = await Promise.race([
        this.processMessage(messageType, request, sender),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('消息处理超时')), 5000)
        )
      ]);
      
      // 检查sendResponse是否可用
      if (typeof sendResponse === 'function') {
        try {
          sendResponse(response);
        } catch (error) {
          this.logger.error('发送响应失败:', error);
        }
      }
      
      // 记录性能
      const endTime = performance.now();
      this.logger.performance(`处理消息: ${messageType}`, startTime, endTime);
      
    } catch (error) {
      this.logger.exception(error, '处理消息失败');
      
      // 检查sendResponse是否可用
      if (typeof sendResponse === 'function') {
        try {
          sendResponse({ 
            success: false, 
            error: error.message,
            code: ERROR_CODES.MESSAGE_HANDLER_ERROR
          });
        } catch (sendError) {
          this.logger.error('发送错误响应失败:', sendError);
        }
      }
    }
  }

  /**
   * 处理连接
   * @param {Object} port - 连接端口
   */
  handleConnection(port) {
    this.logger.debug('新连接建立:', port.name);
    
    // 存储连接
    this.activeConnections.set(port.name, port);
    
    // 监听连接断开
    port.onDisconnect.addListener(() => {
      this.logger.debug('连接断开:', port.name);
      this.activeConnections.delete(port.name);
    });
    
    // 监听连接消息
    port.onMessage.addListener((message) => {
      this.handlePortMessage(message, port);
    });
  }

  /**
   * 处理端口消息
   * @param {Object} message - 消息对象
   * @param {Object} port - 端口对象
   */
  async handlePortMessage(message, port) {
    try {
      this.logger.debug('收到端口消息:', message.type, '来自:', port.name);
      
      const response = await this.processMessage(message.type, message, { port });
      
      // 发送响应
      port.postMessage(response);
      
    } catch (error) {
      this.logger.exception(error, '处理端口消息失败');
      port.postMessage({ 
        success: false, 
        error: error.message,
        code: ERROR_CODES.MESSAGE_HANDLER_ERROR
      });
    }
  }

  /**
   * 处理消息
   * @param {string} messageType - 消息类型
   * @param {Object} request - 请求对象
   * @param {Object} sender - 发送者信息
   * @returns {Promise<Object>} 处理结果
   */
  async processMessage(messageType, request, sender) {
    this.logger.trace('processMessage', [messageType, request.data]);
    
    switch (messageType) {
      // 拦截相关消息
      case MESSAGE_TYPES.INTERCEPTED_MESSAGE:
        return await this.handleInterceptedMessage(request.data, sender.tab);
        
      case MESSAGE_TYPES.WEBSOCKET_DETECTED:
      case MESSAGE_TYPES.EARLY_WEBSOCKET_DETECTED:
        return await this.handleWebSocketDetected(request.data);
        
      case MESSAGE_TYPES.INTERCEPTOR_INSTALLED:
        return await this.handleInterceptorInstalled(request.data);
        
      case MESSAGE_TYPES.CONNECTION_STATUS_UPDATE:
        return await this.handleConnectionStatusUpdate(request.data);
        
      // 配置相关消息
      case MESSAGE_TYPES.GET_CONFIG:
        return await this.handleGetConfig(request.configType);
        
      case MESSAGE_TYPES.UPDATE_CONFIG:
        return await this.handleUpdateConfig(request.data);
        
      // 状态相关消息
      case MESSAGE_TYPES.GET_STATUS:
        return await this.handleGetStatus();
        
      case MESSAGE_TYPES.RESET_STATS:
        return await this.handleResetStats();
        
      case MESSAGE_TYPES.UPDATE_STATS:
        return await this.handleUpdateStats(request.data);
        
      // 标签页管理
      case MESSAGE_TYPES.REGISTER_TAB:
        return await this.handleRegisterTab(sender.tab);
        
      case MESSAGE_TYPES.UNREGISTER_TAB:
        return await this.handleUnregisterTab(sender.tab?.id);
        
      // 通知相关
      case MESSAGE_TYPES.SHOW_NOTIFICATION:
        return await this.handleShowNotification(request.data);
        
      // 导出相关
      case MESSAGE_TYPES.EXPORT_LOGS:
        return await this.handleExportLogs();
        
      case MESSAGE_TYPES.EXPORT_HISTORY:
        return await this.handleExportHistory(sender.tab);
        
      // 自动回复相关
      case MESSAGE_TYPES.TOGGLE_AUTO_REPLY:
        return await this.handleToggleAutoReply(sender.tab);
        
      default:
        this.logger.warn('未知消息类型:', messageType);
        return { 
          success: false, 
          error: '未知消息类型',
          code: ERROR_CODES.MESSAGE_HANDLER_ERROR
        };
    }
  }

  /**
   * 处理拦截消息
   * @param {Object} messageData - 消息数据
   * @param {Object} tab - 标签页信息
   * @returns {Promise<Object>} 处理结果
   */
  async handleInterceptedMessage(messageData, tab) {
    this.logger.info('处理拦截消息:', messageData.type);
    
    // 存储消息到历史记录
    await this.storeMessageToHistory(messageData, tab);
    
    // 更新统计信息
    await this.updateMessageStats();
    
    // 通知相关标签页
    await this.notifyTabsOfMessage(messageData, tab);
    
    return { success: true, message: '消息已处理' };
  }

  /**
   * 处理WebSocket检测
   * @param {Object} data - 检测数据
   * @returns {Promise<Object>} 处理结果
   */
  async handleWebSocketDetected(data) {
    this.logger.info('处理WebSocket检测:', data.url);
    
    // 记录活动
    await this.recordActivity('websocket_detected', `检测到WebSocket连接: ${data.url}`, data);
    
    return { success: true, message: 'WebSocket检测已记录' };
  }

  /**
   * 处理拦截器安装
   * @param {Object} data - 安装数据
   * @returns {Promise<Object>} 处理结果
   */
  async handleInterceptorInstalled(data) {
    this.logger.info('处理拦截器安装:', data.url);
    
    // 记录活动
    await this.recordActivity('interceptor_installed', '拦截器已安装并就绪', data);
    
    return { success: true, message: '拦截器安装已记录' };
  }

  /**
   * 处理连接状态更新
   * @param {Object} data - 连接状态数据
   * @returns {Promise<Object>} 处理结果
   */
  async handleConnectionStatusUpdate(data) {
    this.logger.info('处理连接状态更新:', data.connectionCount);
    
    // 记录活动
    await this.recordActivity('connection_status', `连接状态更新: ${data.connectionCount}个活跃连接`, data);
    
    return { success: true, message: '连接状态已更新' };
  }

  /**
   * 处理获取配置
   * @param {string} configType - 配置类型
   * @returns {Promise<Object>} 处理结果
   */
  async handleGetConfig(configType) {
    this.logger.debug('获取配置:', configType);
    
    try {
      let config;
      
      if (configType) {
        config = await configManager.getConfig(configType);
      } else {
        config = await configManager.getAllConfigs();
      }
      
      return { success: true, data: config };
    } catch (error) {
      this.logger.exception(error, '获取配置失败');
      return { success: false, error: error.message };
    }
  }

  /**
   * 处理更新配置
   * @param {Object} configData - 配置数据
   * @returns {Promise<Object>} 处理结果
   */
  async handleUpdateConfig(configData) {
    this.logger.info('更新配置:', Object.keys(configData));
    
    try {
      await configManager.updateConfig(configData);
      return { success: true, message: SUCCESS_MESSAGES.CONFIG_UPDATED };
    } catch (error) {
      this.logger.exception(error, '更新配置失败');
      return { success: false, error: error.message };
    }
  }

  /**
   * 处理获取状态
   * @returns {Promise<Object>} 处理结果
   */
  async handleGetStatus() {
    this.logger.debug('获取状态');
    
    try {
      const status = await this.getServiceStatus();
      return { success: true, data: status };
    } catch (error) {
      this.logger.exception(error, '获取状态失败');
      return { success: false, error: error.message };
    }
  }

  /**
   * 处理重置统计
   * @returns {Promise<Object>} 处理结果
   */
  async handleResetStats() {
    this.logger.info('重置统计');
    
    try {
      await this.resetStatistics();
      return { success: true, message: SUCCESS_MESSAGES.STATS_RESET };
    } catch (error) {
      this.logger.exception(error, '重置统计失败');
      return { success: false, error: error.message };
    }
  }

  /**
   * 处理更新统计
   * @param {Object} statsData - 统计数据
   * @returns {Promise<Object>} 处理结果
   */
  async handleUpdateStats(statsData) {
    this.logger.debug('更新统计:', statsData);
    
    try {
      await this.updateStatistics(statsData);
      return { success: true, message: '统计已更新' };
    } catch (error) {
      this.logger.exception(error, '更新统计失败');
      return { success: false, error: error.message };
    }
  }

  /**
   * 处理标签页注册
   * @param {Object} tab - 标签页信息
   * @returns {Promise<Object>} 处理结果
   */
  async handleRegisterTab(tab) {
    this.logger.debug('注册标签页:', tab?.id);
    
    // 这里可以添加标签页管理逻辑
    return { success: true, message: '标签页已注册' };
  }

  /**
   * 处理标签页注销
   * @param {number} tabId - 标签页ID
   * @returns {Promise<Object>} 处理结果
   */
  async handleUnregisterTab(tabId) {
    this.logger.debug('注销标签页:', tabId);
    
    // 这里可以添加标签页管理逻辑
    return { success: true, message: '标签页已注销' };
  }

  /**
   * 处理显示通知
   * @param {Object} notificationData - 通知数据
   * @returns {Promise<Object>} 处理结果
   */
  async handleShowNotification(notificationData) {
    this.logger.debug('显示通知:', notificationData.title);
    
    // 这里可以添加通知显示逻辑
    return { success: true, message: '通知已显示' };
  }

  /**
   * 处理导出日志
   * @returns {Promise<Object>} 处理结果
   */
  async handleExportLogs() {
    this.logger.info('导出日志');
    
    try {
      const logs = await this.exportLogs();
      return { success: true, data: logs };
    } catch (error) {
      this.logger.exception(error, '导出日志失败');
      return { success: false, error: error.message };
    }
  }

  /**
   * 处理导出历史
   * @param {Object} tab - 标签页信息
   * @returns {Promise<Object>} 处理结果
   */
  async handleExportHistory(tab) {
    this.logger.info('导出历史');
    
    try {
      // 通知标签页执行导出
      if (tab?.id) {
        await chrome.tabs.sendMessage(tab.id, {
          action: MESSAGE_TYPES.EXPORT_HISTORY
        });
      }
      
      return { success: true, message: SUCCESS_MESSAGES.HISTORY_EXPORTED };
    } catch (error) {
      this.logger.exception(error, '导出历史失败');
      return { success: false, error: error.message };
    }
  }

  /**
   * 处理切换自动回复
   * @param {Object} tab - 标签页信息
   * @returns {Promise<Object>} 处理结果
   */
  async handleToggleAutoReply(tab) {
    this.logger.info('切换自动回复');
    
    try {
      const config = await configManager.getConfig(STORAGE_KEYS.AUTO_REPLY_CONFIG);
      const newEnabled = !config.enabled;
      
      await configManager.setConfig(STORAGE_KEYS.AUTO_REPLY_CONFIG, {
        ...config,
        enabled: newEnabled
      });
      
      // 通知标签页更新配置
      if (tab?.id) {
        await chrome.tabs.sendMessage(tab.id, {
          action: MESSAGE_TYPES.UPDATE_AUTO_REPLY_CONFIG,
          config: { ...config, enabled: newEnabled }
        });
      }
      
      const message = newEnabled ? SUCCESS_MESSAGES.AUTO_REPLY_ENABLED : SUCCESS_MESSAGES.AUTO_REPLY_DISABLED;
      return { success: true, message, enabled: newEnabled };
    } catch (error) {
      this.logger.exception(error, '切换自动回复失败');
      return { success: false, error: error.message };
    }
  }

  /**
   * 存储消息到历史记录
   * @param {Object} messageData - 消息数据
   * @param {Object} tab - 标签页信息
   * @returns {Promise<void>}
   */
  async storeMessageToHistory(messageData, tab) {
    try {
      const history = await chrome.storage.local.get([STORAGE_KEYS.MESSAGE_HISTORY]);
      const messageHistory = history[STORAGE_KEYS.MESSAGE_HISTORY] || [];
      
      messageHistory.push({
        ...messageData,
        timestamp: Date.now(),
        tabId: tab?.id,
        url: tab?.url
      });
      
      // 保持历史记录大小限制
      if (messageHistory.length > 1000) {
        messageHistory.splice(0, messageHistory.length - 1000);
      }
      
      await chrome.storage.local.set({ [STORAGE_KEYS.MESSAGE_HISTORY]: messageHistory });
    } catch (error) {
      this.logger.exception(error, '存储消息历史失败');
    }
  }

  /**
   * 记录活动
   * @param {string} type - 活动类型
   * @param {string} description - 描述
   * @param {Object} data - 数据
   * @returns {Promise<void>}
   */
  async recordActivity(type, description, data) {
    try {
      const result = await chrome.storage.local.get([STORAGE_KEYS.CTRIP_ACTIVITY]);
      const activities = result[STORAGE_KEYS.CTRIP_ACTIVITY] || [];
      
      activities.push({
        timestamp: Date.now(),
        type,
        description,
        data
      });
      
      // 保持最新的100条记录
      if (activities.length > 100) {
        activities.splice(0, activities.length - 100);
      }
      
      await chrome.storage.local.set({ [STORAGE_KEYS.CTRIP_ACTIVITY]: activities });
    } catch (error) {
      this.logger.exception(error, '记录活动失败');
    }
  }

  /**
   * 更新消息统计
   * @returns {Promise<void>}
   */
  async updateMessageStats() {
    try {
      const stats = await configManager.getConfig(STORAGE_KEYS.STATISTICS);
      stats.totalMessages = (stats.totalMessages || 0) + 1;
      stats.lastUpdate = Date.now();
      
      await configManager.setConfig(STORAGE_KEYS.STATISTICS, stats);
    } catch (error) {
      this.logger.exception(error, '更新消息统计失败');
    }
  }

  /**
   * 通知标签页消息
   * @param {Object} messageData - 消息数据
   * @param {Object} tab - 标签页信息
   * @returns {Promise<void>}
   */
  async notifyTabsOfMessage(messageData, tab) {
    try {
      // 可以在这里添加通知其他标签页的逻辑
      this.logger.debug('通知标签页消息:', messageData.type);
    } catch (error) {
      this.logger.exception(error, '通知标签页失败');
    }
  }

  /**
   * 获取服务状态
   * @returns {Promise<Object>} 服务状态
   */
  async getServiceStatus() {
    const stats = await configManager.getConfig(STORAGE_KEYS.STATISTICS);
    return {
      isRunning: true,
      totalMessages: stats?.totalMessages || 0,
      totalReplies: stats?.totalReplies || 0,
      uptime: Date.now() - (stats?.lastReset || Date.now()),
      activeConnections: this.activeConnections.size,
      lastActivity: stats?.lastUpdate || null
    };
  }

  /**
   * 重置统计信息
   * @returns {Promise<void>}
   */
  async resetStatistics() {
    const resetStats = {
      totalMessages: 0,
      totalReplies: 0,
      successRate: 0,
      lastReset: Date.now(),
      lastUpdate: Date.now()
    };
    
    await configManager.setConfig(STORAGE_KEYS.STATISTICS, resetStats);
  }

  /**
   * 更新统计信息
   * @param {Object} statsData - 统计数据
   * @returns {Promise<void>}
   */
  async updateStatistics(statsData) {
    const currentStats = await configManager.getConfig(STORAGE_KEYS.STATISTICS);
    const updatedStats = {
      ...currentStats,
      ...statsData,
      lastUpdate: Date.now()
    };
    
    await configManager.setConfig(STORAGE_KEYS.STATISTICS, updatedStats);
  }

  /**
   * 导出日志
   * @returns {Promise<Object>} 导出的日志数据
   */
  async exportLogs() {
    const data = await chrome.storage.local.get([
      STORAGE_KEYS.MESSAGE_HISTORY,
      STORAGE_KEYS.AUTO_REPLY_STATS,
      STORAGE_KEYS.STATISTICS,
      STORAGE_KEYS.CTRIP_ACTIVITY
    ]);
    
    return {
      timestamp: Date.now(),
      serviceStatus: await this.getServiceStatus(),
      ...data
    };
  }

  /**
   * 确保已初始化
   * @returns {Promise<void>}
   */
  async ensureInitialized() {
    if (!this.initialized) {
      await this.initialize();
    }
  }
}

/**
 * 消息处理器实例
 */
export const messageHandler = new MessageHandler();

/**
 * 导出消息处理器类
 */
export default MessageHandler; 