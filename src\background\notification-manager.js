/**
 * @fileoverview 通知管理器 - 管理系统通知
 * <AUTHOR> Extension Developer
 */

import { APP_CONFIG, NOTIFICATION_TYPES } from '../utils/constants.js';
import { notificationLogger } from '../utils/logger.js';

/**
 * 通知管理器类
 */
class NotificationManager {
  /**
   * 构造函数
   */
  constructor() {
    this.logger = notificationLogger;
    this.initialized = false;
    this.activeNotifications = new Map();
    this.notificationQueue = [];
    this.isProcessing = false;
    this.stats = {
      totalNotifications: 0,
      successfulNotifications: 0,
      failedNotifications: 0,
      lastNotification: null
    };
  }

  /**
   * 初始化通知管理器
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      if (this.initialized) {
        return;
      }

      this.logger.info('正在初始化通知管理器...');
      
      // 检查通知API是否可用
      if (!chrome.notifications) {
        this.logger.warn('通知API不可用');
        return;
      }
      
      // 设置通知监听器
      this.setupNotificationListeners();
      
      this.initialized = true;
      this.logger.info('通知管理器初始化完成');
      
    } catch (error) {
      this.logger.exception(error, '通知管理器初始化失败');
      throw error;
    }
  }

  /**
   * 设置通知监听器
   */
  setupNotificationListeners() {
    // 监听通知点击事件
    chrome.notifications.onClicked.addListener((notificationId) => {
      this.handleNotificationClick(notificationId);
    });

    // 监听通知关闭事件
    chrome.notifications.onClosed.addListener((notificationId, byUser) => {
      this.handleNotificationClose(notificationId, byUser);
    });

    // 监听通知按钮点击事件
    chrome.notifications.onButtonClicked.addListener((notificationId, buttonIndex) => {
      this.handleNotificationButtonClick(notificationId, buttonIndex);
    });
  }

  /**
   * 显示通知
   * @param {Object} options - 通知选项
   * @returns {Promise<string>} 通知ID
   */
  async showNotification(options) {
    try {
      await this.ensureInitialized();
      
      if (!chrome.notifications) {
        throw new Error('通知API不可用');
      }

      // 验证通知选项
      this.validateNotificationOptions(options);
      
      // 创建通知配置
      const notificationConfig = this.createNotificationConfig(options);
      
      // 创建通知
      const notificationId = await this.createNotification(notificationConfig);
      
      // 记录通知信息
      this.recordNotification(notificationId, options);
      
      // 更新统计
      this.stats.totalNotifications++;
      this.stats.successfulNotifications++;
      this.stats.lastNotification = Date.now();
      
      this.logger.info(`通知已显示: ${notificationId}`);
      
      return notificationId;
      
    } catch (error) {
      this.logger.exception(error, '显示通知失败');
      this.stats.failedNotifications++;
      throw error;
    }
  }

  /**
   * 验证通知选项
   * @param {Object} options - 通知选项
   */
  validateNotificationOptions(options) {
    if (!options) {
      throw new Error('通知选项不能为空');
    }

    if (!options.title && !options.message) {
      throw new Error('通知必须包含标题或消息');
    }

    // 验证通知类型
    if (options.type && !Object.values(NOTIFICATION_TYPES).includes(options.type)) {
      throw new Error(`无效的通知类型: ${options.type}`);
    }
  }

  /**
   * 创建通知配置
   * @param {Object} options - 通知选项
   * @returns {Object} 通知配置
   */
  createNotificationConfig(options) {
    return {
      type: options.type || NOTIFICATION_TYPES.BASIC,
      iconUrl: options.iconUrl || 'icons/icon48.png',
      title: options.title || APP_CONFIG.NAME,
      message: options.message || '操作完成',
      priority: options.priority || 0,
      requireInteraction: options.requireInteraction || false,
      silent: options.silent || false,
      buttons: options.buttons || [],
      contextMessage: options.contextMessage || '',
      eventTime: options.eventTime || Date.now(),
      isClickable: options.isClickable !== false
    };
  }

  /**
   * 创建通知
   * @param {Object} config - 通知配置
   * @returns {Promise<string>} 通知ID
   */
  async createNotification(config) {
    return new Promise((resolve, reject) => {
      chrome.notifications.create(config, (notificationId) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(notificationId);
        }
      });
    });
  }

  /**
   * 记录通知信息
   * @param {string} notificationId - 通知ID
   * @param {Object} options - 通知选项
   */
  recordNotification(notificationId, options) {
    this.activeNotifications.set(notificationId, {
      id: notificationId,
      title: options.title,
      message: options.message,
      type: options.type,
      createdAt: Date.now(),
      clicked: false,
      closed: false
    });
  }

  /**
   * 处理通知点击
   * @param {string} notificationId - 通知ID
   */
  handleNotificationClick(notificationId) {
    this.logger.debug('通知被点击:', notificationId);
    
    const notification = this.activeNotifications.get(notificationId);
    if (notification) {
      notification.clicked = true;
      notification.clickedAt = Date.now();
    }
    
    // 清除通知
    this.clearNotification(notificationId);
    
    // 处理特定的通知点击逻辑
    this.handleSpecificNotificationClick(notificationId);
  }

  /**
   * 处理通知关闭
   * @param {string} notificationId - 通知ID
   * @param {boolean} byUser - 是否由用户关闭
   */
  handleNotificationClose(notificationId, byUser) {
    this.logger.debug('通知被关闭:', notificationId, '用户关闭:', byUser);
    
    const notification = this.activeNotifications.get(notificationId);
    if (notification) {
      notification.closed = true;
      notification.closedAt = Date.now();
      notification.closedByUser = byUser;
    }
    
    // 从活动通知中移除
    this.activeNotifications.delete(notificationId);
  }

  /**
   * 处理通知按钮点击
   * @param {string} notificationId - 通知ID
   * @param {number} buttonIndex - 按钮索引
   */
  handleNotificationButtonClick(notificationId, buttonIndex) {
    this.logger.debug('通知按钮被点击:', notificationId, '按钮索引:', buttonIndex);
    
    const notification = this.activeNotifications.get(notificationId);
    if (notification) {
      notification.buttonClicked = buttonIndex;
      notification.buttonClickedAt = Date.now();
    }
    
    // 处理特定的按钮点击逻辑
    this.handleSpecificButtonClick(notificationId, buttonIndex);
  }

  /**
   * 处理特定的通知点击逻辑
   * @param {string} notificationId - 通知ID
   */
  handleSpecificNotificationClick(notificationId) {
    // 可以根据通知ID或类型执行特定的逻辑
    // 例如：打开设置页面、显示详细信息等
  }

  /**
   * 处理特定的按钮点击逻辑
   * @param {string} notificationId - 通知ID
   * @param {number} buttonIndex - 按钮索引
   */
  handleSpecificButtonClick(notificationId, buttonIndex) {
    // 可以根据通知ID和按钮索引执行特定的逻辑
  }

  /**
   * 清除通知
   * @param {string} notificationId - 通知ID
   * @returns {Promise<boolean>} 是否成功清除
   */
  async clearNotification(notificationId) {
    try {
      if (!chrome.notifications) {
        return false;
      }

      return new Promise((resolve) => {
        chrome.notifications.clear(notificationId, (wasCleared) => {
          if (wasCleared) {
            this.logger.debug(`通知已清除: ${notificationId}`);
          }
          resolve(wasCleared);
        });
      });
    } catch (error) {
      this.logger.exception(error, `清除通知失败: ${notificationId}`);
      return false;
    }
  }

  /**
   * 清除所有通知
   * @returns {Promise<void>}
   */
  async clearAllNotifications() {
    try {
      if (!chrome.notifications) {
        return;
      }

      const activeIds = Array.from(this.activeNotifications.keys());
      
      for (const id of activeIds) {
        await this.clearNotification(id);
      }
      
      this.activeNotifications.clear();
      this.logger.info('所有通知已清除');
      
    } catch (error) {
      this.logger.exception(error, '清除所有通知失败');
    }
  }

  /**
   * 显示成功通知
   * @param {string} message - 消息内容
   * @param {Object} options - 附加选项
   * @returns {Promise<string>} 通知ID
   */
  async showSuccessNotification(message, options = {}) {
    return this.showNotification({
      title: '操作成功',
      message: message,
      iconUrl: 'icons/icon48.png',
      type: NOTIFICATION_TYPES.BASIC,
      ...options
    });
  }

  /**
   * 显示错误通知
   * @param {string} message - 消息内容
   * @param {Object} options - 附加选项
   * @returns {Promise<string>} 通知ID
   */
  async showErrorNotification(message, options = {}) {
    return this.showNotification({
      title: '操作失败',
      message: message,
      iconUrl: 'icons/icon48.png',
      type: NOTIFICATION_TYPES.BASIC,
      priority: 2,
      requireInteraction: true,
      ...options
    });
  }

  /**
   * 显示警告通知
   * @param {string} message - 消息内容
   * @param {Object} options - 附加选项
   * @returns {Promise<string>} 通知ID
   */
  async showWarningNotification(message, options = {}) {
    return this.showNotification({
      title: '警告',
      message: message,
      iconUrl: 'icons/icon48.png',
      type: NOTIFICATION_TYPES.BASIC,
      priority: 1,
      ...options
    });
  }

  /**
   * 显示信息通知
   * @param {string} message - 消息内容
   * @param {Object} options - 附加选项
   * @returns {Promise<string>} 通知ID
   */
  async showInfoNotification(message, options = {}) {
    return this.showNotification({
      title: APP_CONFIG.NAME,
      message: message,
      iconUrl: 'icons/icon48.png',
      type: NOTIFICATION_TYPES.BASIC,
      ...options
    });
  }

  /**
   * 显示自动回复状态通知
   * @param {boolean} enabled - 是否启用
   * @returns {Promise<string>} 通知ID
   */
  async showAutoReplyStatusNotification(enabled) {
    return this.showNotification({
      title: APP_CONFIG.NAME,
      message: `自动回复已${enabled ? '启用' : '禁用'}`,
      iconUrl: 'icons/icon48.png',
      type: NOTIFICATION_TYPES.BASIC
    });
  }

  /**
   * 显示导出完成通知
   * @param {string} filename - 文件名
   * @returns {Promise<string>} 通知ID
   */
  async showExportCompleteNotification(filename) {
    return this.showNotification({
      title: '导出完成',
      message: `文件已保存: ${filename}`,
      iconUrl: 'icons/icon48.png',
      type: NOTIFICATION_TYPES.BASIC
    });
  }

  /**
   * 显示带按钮的通知
   * @param {string} title - 标题
   * @param {string} message - 消息
   * @param {Array} buttons - 按钮数组
   * @param {Object} options - 附加选项
   * @returns {Promise<string>} 通知ID
   */
  async showNotificationWithButtons(title, message, buttons, options = {}) {
    return this.showNotification({
      title,
      message,
      buttons,
      type: NOTIFICATION_TYPES.BASIC,
      requireInteraction: true,
      ...options
    });
  }

  /**
   * 获取活动通知列表
   * @returns {Array} 活动通知列表
   */
  getActiveNotifications() {
    return Array.from(this.activeNotifications.values());
  }

  /**
   * 获取通知统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      ...this.stats,
      activeNotifications: this.activeNotifications.size,
      successRate: this.stats.totalNotifications > 0 ? 
        this.stats.successfulNotifications / this.stats.totalNotifications : 0,
      isInitialized: this.initialized
    };
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.stats = {
      totalNotifications: 0,
      successfulNotifications: 0,
      failedNotifications: 0,
      lastNotification: null
    };
    
    this.logger.info('通知统计信息已重置');
  }

  /**
   * 检查通知权限
   * @returns {Promise<string>} 权限状态
   */
  async checkNotificationPermission() {
    try {
      if (!chrome.notifications) {
        return 'unavailable';
      }

      return new Promise((resolve) => {
        chrome.notifications.getPermissionLevel((level) => {
          resolve(level);
        });
      });
    } catch (error) {
      this.logger.exception(error, '检查通知权限失败');
      return 'error';
    }
  }

  /**
   * 确保已初始化
   * @returns {Promise<void>}
   */
  async ensureInitialized() {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  /**
   * 销毁通知管理器
   * @returns {Promise<void>}
   */
  async destroy() {
    try {
      await this.clearAllNotifications();
      this.initialized = false;
      this.logger.info('通知管理器已销毁');
    } catch (error) {
      this.logger.exception(error, '销毁通知管理器失败');
    }
  }
}

/**
 * 通知管理器实例
 */
export const notificationManager = new NotificationManager();

/**
 * 导出通知管理器类
 */
export default NotificationManager; 