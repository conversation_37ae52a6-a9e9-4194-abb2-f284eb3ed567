console.log('==== service-worker 入口文件已加载 ====');
/**
 * 携程IM拦截器后台服务工作者
 * 整合所有后台功能模块
 */
import { configManager } from '../utils/config.js';
import { backgroundLogger } from '../utils/logger.js';
import AlarmManager from './alarm-manager.js';
import ContextMenuManager from './context-menu.js';
import MessageHandler from './message-handler.js';
import NotificationManager from './notification-manager.js';

class CtripIMServiceWorker {
  constructor() {
    this.logger = backgroundLogger;
    this.config = configManager;
    this.messageHandler = null;
    this.alarmManager = null;
    this.notificationManager = null;
    this.contextMenuManager = null;
    this.initialized = false;
    this.serviceStatus = {
      isRunning: false,
      startTime: Date.now(),
      lastActivity: null,
      totalMessages: 0,
      totalReplies: 0,
      errorCount: 0
    };
  }

  /**
   * 初始化服务工作者
   */
  async initialize() {
    if (this.initialized) {
      this.logger.warn('服务工作者已经初始化');
      return;
    }

    try {
      this.logger.info('初始化携程IM服务工作者...');

      // 初始化配置
      this.config = configManager;
      await this.config.ensureInitialized();

      // 初始化各个管理器
      await this.initializeManagers();

      // 设置事件监听器
      this.setupEventListeners();

      // 初始化存储
      await this.initializeStorage();

      this.serviceStatus.isRunning = true;
      this.initialized = true;
      
      this.logger.info('携程IM服务工作者初始化完成');
    } catch (error) {
      this.logger.exception(error, '初始化失败');
      this.serviceStatus.errorCount++;
      throw error;
    }
  }

  /**
   * 初始化管理器
   */
  async initializeManagers() {
    // 初始化消息处理器
    this.messageHandler = new MessageHandler();
    await this.messageHandler.initialize();

    // 初始化告警管理器
    this.alarmManager = new AlarmManager();
    await this.alarmManager.initialize();

    // 初始化通知管理器
    this.notificationManager = new NotificationManager();
    await this.notificationManager.initialize();

    // 初始化上下文菜单管理器
    this.contextMenuManager = new ContextMenuManager();
    await this.contextMenuManager.initialize();
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 监听消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // 保持异步消息通道打开
    });

    // 监听扩展安装/更新
    chrome.runtime.onInstalled.addListener((details) => {
      this.handleInstalled(details);
    });

    // 监听扩展启动
    chrome.runtime.onStartup.addListener(() => {
      this.handleStartup();
    });

    // 监听标签页更新
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.handleTabUpdated(tabId, changeInfo, tab);
    });

    // 监听告警事件
    chrome.alarms.onAlarm.addListener((alarm) => {
      this.alarmManager.handleAlarm(alarm);
    });

    // 监听通知点击
    chrome.notifications.onClicked.addListener((notificationId) => {
      this.notificationManager.handleNotificationClick(notificationId);
    });

    // 监听上下文菜单点击
    chrome.contextMenus.onClicked.addListener((info, tab) => {
      this.contextMenuManager.handleContextMenuClick(info, tab);
    });
  }

  /**
   * 处理消息
   */
  async handleMessage(request, sender, sendResponse) {
    const startTime = performance.now();
    
    try {
      const { source, action, data } = request;
      
      this.logger.debug('收到消息:', { action, source, tabId: sender.tab?.id });
      
      // 更新活动时间
      this.serviceStatus.lastActivity = Date.now();

      // 使用Promise.race添加超时处理
      const result = await Promise.race([
        this.messageHandler.handleMessage(request, sender),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('消息处理超时(5秒)')), 5000)
        )
      ]);
      
      // 更新统计信息
      this.updateStats(action, result);
      
      // 发送响应
      if (typeof sendResponse === 'function') {
        try {
          sendResponse(result);
        } catch (error) {
          this.logger.error('发送响应失败:', error);
          try {
            sendResponse({ success: false, error: '发送响应失败' });
          } catch (retryError) {
            this.logger.error('重试发送响应也失败:', retryError);
          }
        }
      }

      // 记录性能指标
      const endTime = performance.now();
      this.logger.performance(`消息处理完成: ${action}`, startTime, endTime);
      
    } catch (error) {
      this.logger.exception(error, '处理消息失败');
      this.serviceStatus.errorCount++;
      
      if (typeof sendResponse === 'function') {
        try {
          sendResponse({
            success: false,
            error: error.message || '未知错误',
            code: 'MESSAGE_HANDLER_ERROR'
          });
        } catch (sendError) {
          this.logger.error('发送错误响应失败:', sendError);
        }
      }
    }
  }

  /**
   * 处理扩展安装/更新
   */
  async handleInstalled(details) {
    try {
      this.logger.info('扩展已安装/更新:', details.reason);
      
      if (details.reason === 'install') {
        // 首次安装
        await this.handleFirstInstall();
      } else if (details.reason === 'update') {
        // 更新
        await this.handleUpdate(details.previousVersion);
      }
    } catch (error) {
      this.logger.exception(error, '处理安装/更新失败');
    }
  }

  /**
   * 处理首次安装
   */
  async handleFirstInstall() {
    this.logger.info('首次安装处理');
    
    // 设置默认配置
    await this.config.resetToDefaults();
    
    // 创建上下文菜单
    await this.contextMenuManager.createMenus();
    
    // 设置定时任务
    await this.alarmManager.setupDefaultAlarms();
    
    // 显示欢迎通知
    await this.notificationManager.showWelcomeNotification();
  }

  /**
   * 处理更新
   */
  async handleUpdate(previousVersion) {
    this.logger.info('更新处理:', { previousVersion });
    
    // 迁移配置
    await this.config.migrateFromVersion(previousVersion);
    
    // 更新上下文菜单
    await this.contextMenuManager.updateMenus();
    
    // 显示更新通知
    await this.notificationManager.showUpdateNotification(previousVersion);
  }

  /**
   * 处理扩展启动
   */
  async handleStartup() {
    this.logger.info('扩展启动处理');
    
    // 重置启动时间
    this.serviceStatus.startTime = Date.now();
    
    // 清理过期数据
    await this.cleanupExpiredData();
    
    // 恢复定时任务
    await this.alarmManager.restoreAlarms();
  }

  /**
   * 处理标签页更新
   */
  async handleTabUpdated(tabId, changeInfo, tab) {
    // 只处理完成加载的携程网站
    if (changeInfo.status === 'complete' && tab.url) {
      const isCtripSite = /^https?:\/\/.*\.ctrip\.com/.test(tab.url);
      
      if (isCtripSite) {
        this.logger.debug('检测到携程网站:', { tabId, url: tab.url });
        
        // 更新上下文菜单
        try {
          await this.contextMenuManager.updateMenuStatus();
        } catch (error) {
          this.logger.exception(error, '更新上下文菜单状态失败');
        }
      }
    }
  }

  /**
   * 初始化存储
   */
  async initializeStorage() {
    try {
      // 检查存储是否需要初始化
      const result = await chrome.storage.local.get(['initialized']);
      
      if (!result.initialized) {
        this.logger.info('初始化存储...');
        
        // 设置默认值
        await chrome.storage.local.set({
          initialized: true,
          version: chrome.runtime.getManifest().version,
          installTime: Date.now(),
          ctripActivity: [],
          messageHistory: [],
          connectionHistory: [],
          replyHistory: [],
          userStats: {},
          globalStats: {
            totalConnections: 0,
            totalMessages: 0,
            totalReplies: 0,
            startTime: Date.now()
          }
        });
        
        this.logger.info('存储初始化完成');
      }
    } catch (error) {
      this.logger.exception(error, '初始化存储失败');
    }
  }

  /**
   * 更新统计信息
   */
  updateStats(action, result) {
    switch (action) {
      case 'intercepted_message':
        this.serviceStatus.totalMessages++;
        break;
      case 'auto_reply_sent':
        this.serviceStatus.totalReplies++;
        break;
    }
    
    // 异步更新存储中的统计信息
    this.updateStorageStats();
  }

  /**
   * 更新存储中的统计信息
   */
  async updateStorageStats() {
    try {
      const result = await chrome.storage.local.get(['globalStats']);
      const globalStats = result.globalStats || {};
      
      globalStats.totalMessages = this.serviceStatus.totalMessages;
      globalStats.totalReplies = this.serviceStatus.totalReplies;
      globalStats.lastActivity = this.serviceStatus.lastActivity;
      
      await chrome.storage.local.set({ globalStats });
    } catch (error) {
      this.logger.exception(error, '更新存储统计信息失败');
    }
  }

  /**
   * 清理过期数据
   */
  async cleanupExpiredData() {
    try {
      this.logger.info('开始清理过期数据...');
      
      const now = Date.now();
      const expiredThreshold = 30 * 24 * 60 * 60 * 1000; // 30天
      
      // 清理活动历史
      const activityResult = await chrome.storage.local.get(['ctripActivity']);
      const activities = activityResult.ctripActivity || [];
      const validActivities = activities.filter(
        activity => now - activity.timestamp < expiredThreshold
      );
      
      if (validActivities.length !== activities.length) {
        await chrome.storage.local.set({ ctripActivity: validActivities });
        this.logger.info('清理活动历史:', {
          原始: activities.length,
          剩余: validActivities.length
        });
      }
      
      // 清理消息历史
      const messageResult = await chrome.storage.local.get(['messageHistory']);
      const messages = messageResult.messageHistory || [];
      const validMessages = messages.filter(
        message => now - message.timestamp < expiredThreshold
      );
      
      if (validMessages.length !== messages.length) {
        await chrome.storage.local.set({ messageHistory: validMessages });
        this.logger.info('清理消息历史:', {
          原始: messages.length,
          剩余: validMessages.length
        });
      }
      
      this.logger.info('过期数据清理完成');
    } catch (error) {
      this.logger.exception(error, '清理过期数据失败');
    }
  }

  /**
   * 获取服务状态
   */
  getServiceStatus() {
    return {
      ...this.serviceStatus,
      uptime: Date.now() - this.serviceStatus.startTime,
      initialized: this.initialized
    };
  }

  /**
   * 获取所有管理器状态
   */
  getManagersStatus() {
    return {
      messageHandler: this.messageHandler?.getStatus() || null,
      alarmManager: this.alarmManager?.getStatus() || null,
      notificationManager: this.notificationManager?.getStatus() || null,
      contextMenuManager: this.contextMenuManager?.getStatus() || null
    };
  }

  /**
   * 导出调试信息
   */
  async exportDebugInfo() {
    try {
      const debugInfo = {
        serviceStatus: this.getServiceStatus(),
        managersStatus: this.getManagersStatus(),
        storage: await chrome.storage.local.get(),
        manifest: chrome.runtime.getManifest(),
        timestamp: Date.now()
      };
      
      return debugInfo;
    } catch (error) {
      this.logger.exception(error, '导出调试信息失败');
      return null;
    }
  }

  /**
   * 重置服务工作者
   */
  async reset() {
    try {
      this.logger.info('重置服务工作者...');
      
      // 停止所有管理器
      if (this.alarmManager) {
        await this.alarmManager.clearAllAlarms();
      }
      
      if (this.notificationManager) {
        await this.notificationManager.clearAllNotifications();
      }
      
      if (this.contextMenuManager) {
        await this.contextMenuManager.removeAllMenus();
      }
      
      // 重置配置
      await this.config.resetToDefaults();
      
      // 清理存储
      await chrome.storage.local.clear();
      
      // 重新初始化
      await this.initialize();
      
      this.logger.info('服务工作者重置完成');
    } catch (error) {
      this.logger.exception(error, '重置服务工作者失败');
      throw error;
    }
  }

  /**
   * 销毁服务工作者
   */
  destroy() {
    this.logger.info('销毁服务工作者');
    
    // 停止所有管理器
    if (this.messageHandler) {
      this.messageHandler.destroy();
      this.messageHandler = null;
    }
    
    if (this.alarmManager) {
      this.alarmManager.destroy();
      this.alarmManager = null;
    }
    
    if (this.notificationManager) {
      this.notificationManager.destroy();
      this.notificationManager = null;
    }
    
    if (this.contextMenuManager) {
      this.contextMenuManager.destroy();
      this.contextMenuManager = null;
    }
    
    if (this.config) {
      this.config.destroy();
      this.config = null;
    }
    
    this.serviceStatus.isRunning = false;
    this.initialized = false;
  }
}

// 创建全局实例
const ctripIMServiceWorker = new CtripIMServiceWorker();

// 立即初始化
ctripIMServiceWorker.initialize().catch(error => {
  console.error('[CtripIMServiceWorker] 初始化失败:', error);
});

// 导出全局对象供调试使用
self.ctripIMServiceWorker = ctripIMServiceWorker;

// 添加全局错误处理
self.addEventListener('error', (event) => {
  console.error('[CtripIMServiceWorker] 全局错误:', event.error);
  ctripIMServiceWorker.serviceStatus.errorCount++;
});

self.addEventListener('unhandledrejection', (event) => {
  console.error('[CtripIMServiceWorker] 未处理的Promise拒绝:', event.reason);
  ctripIMServiceWorker.serviceStatus.errorCount++;
});

// Service Worker 生命周期事件
self.addEventListener('install', (event) => {
  console.log('[CtripIMServiceWorker] Service Worker 安装中...');
  event.waitUntil(
    Promise.resolve().then(() => {
      console.log('[CtripIMServiceWorker] Service Worker 安装完成');
    })
  );
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  console.log('[CtripIMServiceWorker] Service Worker 激活中...');
  event.waitUntil(
    Promise.resolve().then(() => {
      console.log('[CtripIMServiceWorker] Service Worker 激活完成');
    })
  );
  event.waitUntil(self.clients.claim());
});

export default ctripIMServiceWorker; 