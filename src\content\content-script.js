// ==== Content Script 入口点 ====
console.log('===== CtripIM Content Script 开始加载 =====');

/**
 * 携程IM内容脚本
 * 整合WebSocket拦截、连接管理和消息处理功能
 */
import { MessageProcessor } from '../message-parser/message-processor.js';
import { configManager } from '../utils/config.js';
import { contentLogger } from '../utils/logger.js';
import { ConnectionManager } from './connection-manager.js';

class CtripIMContentScript {
  constructor() {
    console.log('[CtripIM Content] 创建内容脚本实例');
    this.logger = contentLogger;
    this.connectionManager = null;
    this.messageProcessor = null;
    this.config = null;
    this.initialized = false;
    this.webSocketInterceptor = null;

    // WebSocket拦截器数据
    this.proxyData = {
      activeConnections: new Map(),
      connectionStats: {
        totalConnections: 0,
        activeConnections: 0,
        messagesReceived: 0,
        messagesSent: 0,
        lastActivity: null
      },
      messageHandlers: new Set(),
      installTime: Date.now()
    };

    // 保存原始WebSocket
    this.OriginalWebSocket = window.WebSocket;

    // 验证WebSocket是否可用
    if (!this.OriginalWebSocket) {
      console.warn('[CtripIM Content] ⚠️ WebSocket不可用，可能在不支持的环境中运行');
    } else {
      console.log('[CtripIM Content] ✅ 原始WebSocket已保存:', this.OriginalWebSocket.name);
    }
  }

  /**
   * 初始化内容脚本
   */
  async initialize() {
    console.log('[CtripIM Content] ===== 开始初始化内容脚本 =====');
    
    if (this.initialized) {
      console.warn('[CtripIM Content] 内容脚本已经初始化过');
      return;
    }

    try {
      console.log('[CtripIM Content] 当前页面URL:', window.location.href);
      console.log('[CtripIM Content] 当前文档状态:', document.readyState);

      // 初始化WebSocket拦截器
      const webSocketInitSuccess = this.initializeWebSocketInterceptor();

      if (!webSocketInitSuccess) {
        console.warn('[CtripIM Content] ⚠️ WebSocket拦截器初始化失败，但继续初始化其他组件');
      }

      // 检查WebSocket拦截器状态
      this.checkWebSocketInterceptor();

      // 初始化配置
      this.config = configManager;
      await this.config.ensureInitialized();
      console.log('[CtripIM Content] 配置初始化完成');

      // 初始化组件
      console.log('[CtripIM Content] 正在初始化组件...');
      await this.initializeComponents();
      console.log('[CtripIM Content] 组件初始化完成');

      // 连接组件
      this.connectComponents();

      // 设置消息监听
      this.setupMessageListener();

      // 设置WebSocket拦截器事件监听
      this.setupWebSocketInterceptorListeners();

      // 处理已存在的连接
      this.processExistingConnections();

      this.initialized = true;
      this.logger.info('携程IM内容脚本初始化完成');

      // 通知后台脚本
      this.notifyBackgroundScript('interceptor-installed', {
        url: window.location.href,
        timestamp: Date.now(),
        webSocketIntercepted: !!window._ctripWebSocketInterceptorInstalled
      });

    } catch (error) {
      console.error('[CtripIM Content] 初始化失败:', error.message);
      throw error;
    }
  }

  /**
   * 初始化WebSocket拦截器
   */
  initializeWebSocketInterceptor() {
    console.log('[CtripIM Content] 🚀 初始化WebSocket拦截器...');

    // 检查是否已经安装
    if (window._ctripWebSocketInterceptorInstalled) {
      console.log('[CtripIM Content] ⚠️ WebSocket拦截器已安装，跳过');
      return true;
    }

    // 检查WebSocket是否可用
    if (!this.OriginalWebSocket) {
      console.error('[CtripIM Content] ❌ WebSocket不可用，无法初始化拦截器');
      return false;
    }

    console.log('[CtripIM Content] 💾 原始WebSocket已保存:', this.OriginalWebSocket.name);

    // 创建并安装WebSocket代理
    const success = this.installWebSocketProxy();

    if (success) {
      console.log('[CtripIM Content] ✅ WebSocket拦截器初始化成功');
    } else {
      console.error('[CtripIM Content] ❌ WebSocket拦截器初始化失败');
    }

    return success;
  }

  /**
   * 生成连接ID
   */
  generateConnectionId() {
    return 'ws_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 生成消息ID
   */
  generateMessageId() {
    return 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 处理WebSocket消息
   */
  handleWebSocketMessage(connectionId, data, direction) {
    try {
      const connectionInfo = this.proxyData.activeConnections.get(connectionId);
      if (!connectionInfo) return;

      // 更新统计
      if (direction === 'received') {
        this.proxyData.connectionStats.messagesReceived++;
      } else {
        this.proxyData.connectionStats.messagesSent++;
      }

      this.proxyData.connectionStats.lastActivity = Date.now();
      connectionInfo.lastActivity = Date.now();
      connectionInfo.messageCount++;

      console.log(`[CtripIM Content] 📨 ${direction === 'sent' ? '发送' : '接收'}消息:`, {
        connectionId: connectionId,
        url: connectionInfo.url,
        dataLength: data?.length || 0,
        data: typeof data === 'string' ? data.slice(0, 100) : '非文本数据'
      });

      // 创建消息数据
      const messageData = {
        id: this.generateMessageId(),
        connectionId: connectionId,
        message: data,
        direction: direction,
        timestamp: Date.now(),
        isCtripProxy: true,
        source: 'ctrip_websocket_proxy'
      };

      // 触发自定义事件
      window.dispatchEvent(new CustomEvent('ctripWebSocketMessage', {
        detail: messageData
      }));

    } catch (error) {
      console.error('[CtripIM Content] ❌ 处理消息失败:', error);
    }
  }

  /**
   * 更新WebSocket连接状态
   */
  updateWebSocketConnectionStatus(connectionId, status) {
    const connectionInfo = this.proxyData.activeConnections.get(connectionId);
    if (connectionInfo) {
      connectionInfo.status = status;
      connectionInfo.lastActivity = Date.now();

      console.log('[CtripIM Content] 📊 连接状态更新:', {
        connectionId,
        status,
        url: connectionInfo.url,
        protocol: connectionInfo.protocols
      });

      // 触发状态变化事件
      window.dispatchEvent(new CustomEvent('ctripWebSocketStatusChange', {
        detail: {
          connectionId: connectionId,
          status: status,
          url: connectionInfo.url,
          timestamp: Date.now()
        }
      }));
    }
  }

  /**
   * 设置WebSocket事件监听器
   */
  setupWebSocketEventListeners(websocket, connectionId) {
    const connectionInfo = this.proxyData.activeConnections.get(connectionId);
    let heartbeatInterval = null;
    let lastPongTime = null;

    // 心跳检测函数
    const startHeartbeat = () => {
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
      }
      
      heartbeatInterval = setInterval(() => {
        try {
          if (websocket.readyState === WebSocket.OPEN) {
            websocket.send(JSON.stringify({
              type: 'ping',
              timestamp: Date.now()
            }));
            
            if (lastPongTime && Date.now() - lastPongTime > 10000) {
              console.log('[CtripIM Content] ⚠️ 心跳检测超时:', {
                connectionId,
                lastPongTime: new Date(lastPongTime).toISOString()
              });
              this.updateWebSocketConnectionStatus(connectionId, 'error');
            }
          }
        } catch (error) {
          console.error('[CtripIM Content] ❌ 发送心跳消息失败:', error);
          this.updateWebSocketConnectionStatus(connectionId, 'error');
        }
      }, 30000);
    };

    // 拦截发送方法
    const originalSend = websocket.send;
    websocket.send = (data) => {
      this.handleWebSocketMessage(connectionId, data, 'sent');
      return originalSend.call(websocket, data);
    };

    // 拦截消息事件
    const originalAddEventListener = websocket.addEventListener;
    websocket.addEventListener = (type, listener, options) => {
      if (type === 'message') {
        const wrappedListener = (event) => {
          if (typeof event.data === 'string') {
            try {
              const data = JSON.parse(event.data);
              if (data.type === 'pong') {
                lastPongTime = Date.now();
                return;
              }
            } catch (e) {
              // 不是JSON或不是pong消息，当作普通消息处理
            }
          }
          
          this.handleWebSocketMessage(connectionId, event.data, 'received');
          return listener.call(websocket, event);
        };
        return originalAddEventListener.call(websocket, type, wrappedListener, options);
      }
      return originalAddEventListener.call(websocket, type, listener, options);
    };

    // 拦截onmessage属性
    let originalOnMessage = websocket.onmessage;
    Object.defineProperty(websocket, 'onmessage', {
      get: () => originalOnMessage,
      set: (handler) => {
        originalOnMessage = handler;
        websocket.onmessage = (event) => {
          if (typeof event.data === 'string') {
            try {
              const data = JSON.parse(event.data);
              if (data.type === 'pong') {
                lastPongTime = Date.now();
                return;
              }
            } catch (e) {
              // 不是JSON或不是pong消息，当作普通消息处理
            }
          }
          
          this.handleWebSocketMessage(connectionId, event.data, 'received');
          if (handler) handler(event);
        };
      }
    });

    // 监听连接状态
    websocket.addEventListener('open', () => {
      console.log('[CtripIM Content] ✅ WebSocket连接已打开:', {
        url: connectionInfo?.url,
        protocol: websocket.protocol,
        extensions: websocket.extensions,
        readyState: websocket.readyState,
        connectionId: connectionId,
        timestamp: Date.now()
      });
      
      this.updateWebSocketConnectionStatus(connectionId, 'connected');
      startHeartbeat();
      
      try {
        websocket.send(JSON.stringify({
          type: 'ping',
          timestamp: Date.now()
        }));
        console.log('[CtripIM Content] 🏓 发送测试消息成功');
      } catch (error) {
        console.error('[CtripIM Content] ❌ 发送测试消息失败:', error);
        this.updateWebSocketConnectionStatus(connectionId, 'error');
      }
    });

    websocket.addEventListener('close', (event) => {
      console.log('[CtripIM Content] ❌ WebSocket连接已关闭:', {
        url: connectionInfo?.url,
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean
      });
      
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
        heartbeatInterval = null;
      }
      
      this.updateWebSocketConnectionStatus(connectionId, 'closed');

      if (this.proxyData.connectionStats.activeConnections > 0) {
        this.proxyData.connectionStats.activeConnections--;
      }
    });

    websocket.addEventListener('error', (error) => {
      console.log('[CtripIM Content] 🚫 WebSocket连接错误:', {
        url: connectionInfo?.url,
        error: error
      });
      
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
        heartbeatInterval = null;
      }
      
      this.updateWebSocketConnectionStatus(connectionId, 'error');
    });
  }

  /**
   * 创建WebSocket代理
   */
  createWebSocketProxy(url, protocols) {
    const connectionId = this.generateConnectionId();
    
    console.log('[CtripIM Content] 🔍 创建新的WebSocket连接:', {
      url,
      protocols,
      connectionId,
      origin: window.location.origin,
      host: new URL(url).host
    });

    // 创建原始WebSocket连接
    const originalWebSocket = new this.OriginalWebSocket(url, protocols);

    // 更新统计信息
    this.proxyData.connectionStats.totalConnections++;
    this.proxyData.connectionStats.activeConnections++;
    this.proxyData.connectionStats.lastActivity = Date.now();

    // 存储连接信息
    const connectionInfo = {
      id: connectionId,
      url: url,
      protocols: protocols,
      status: 'connecting',
      createdAt: Date.now(),
      lastActivity: Date.now(),
      messageCount: 0,
      websocket: originalWebSocket,
      source: 'ctrip_proxy'
    };

    this.proxyData.activeConnections.set(connectionId, connectionInfo);

    // 设置事件监听器
    this.setupWebSocketEventListeners(originalWebSocket, connectionId);

    return originalWebSocket;
  }

  /**
   * 安装WebSocket代理
   */
  installWebSocketProxy() {
    // 验证原始WebSocket是否可用
    if (!this.OriginalWebSocket) {
      console.error('[CtripIM Content] ❌ 无法安装WebSocket代理：原始WebSocket不可用');
      return false;
    }

    // 验证原始WebSocket的prototype
    if (!this.OriginalWebSocket.prototype) {
      console.error('[CtripIM Content] ❌ 无法安装WebSocket代理：原始WebSocket.prototype不可用');
      return false;
    }

    console.log('[CtripIM Content] 🔧 开始安装WebSocket代理...');

    const createWebSocketProxy = (url, protocols) => this.createWebSocketProxy(url, protocols);

    // 复制原始WebSocket的属性和方法
    try {
      // 安全地设置原型链
      if (this.OriginalWebSocket.prototype) {
        Object.setPrototypeOf(createWebSocketProxy.prototype, this.OriginalWebSocket.prototype);
      }

      if (this.OriginalWebSocket) {
        Object.setPrototypeOf(createWebSocketProxy, this.OriginalWebSocket);
      }

      // 复制静态属性（跳过只读常量）
      const readOnlyProperties = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];
      for (const key in this.OriginalWebSocket) {
        if (this.OriginalWebSocket.hasOwnProperty(key) && !readOnlyProperties.includes(key)) {
          try {
            const descriptor = Object.getOwnPropertyDescriptor(this.OriginalWebSocket, key);
            if (descriptor && descriptor.writable !== false) {
              createWebSocketProxy[key] = this.OriginalWebSocket[key];
            }
          } catch (e) {
            // 忽略只读属性错误
            console.debug('[CtripIM Content] 跳过只读属性:', key, e.message);
          }
        }
      }

      // 使用Object.defineProperty安全地设置WebSocket常量
      try {
        Object.defineProperty(createWebSocketProxy, 'CONNECTING', {
          value: 0,
          writable: false,
          enumerable: true,
          configurable: false
        });
        Object.defineProperty(createWebSocketProxy, 'OPEN', {
          value: 1,
          writable: false,
          enumerable: true,
          configurable: false
        });
        Object.defineProperty(createWebSocketProxy, 'CLOSING', {
          value: 2,
          writable: false,
          enumerable: true,
          configurable: false
        });
        Object.defineProperty(createWebSocketProxy, 'CLOSED', {
          value: 3,
          writable: false,
          enumerable: true,
          configurable: false
        });
      } catch (e) {
        console.warn('[CtripIM Content] ⚠️ 设置WebSocket常量时出错:', e.message);
        // 如果defineProperty失败，尝试直接赋值（可能在某些环境中有效）
        try {
          createWebSocketProxy.CONNECTING = 0;
          createWebSocketProxy.OPEN = 1;
          createWebSocketProxy.CLOSING = 2;
          createWebSocketProxy.CLOSED = 3;
        } catch (e2) {
          console.warn('[CtripIM Content] ⚠️ 直接赋值WebSocket常量也失败:', e2.message);
        }
      }

    } catch (e) {
      console.error('[CtripIM Content] ❌ 复制WebSocket属性时出错:', e);
      // 如果代理初始化失败，确保原始WebSocket仍然可用
      if (!window.WebSocket && this.OriginalWebSocket) {
        window.WebSocket = this.OriginalWebSocket;
      }
      return false; // 表示初始化失败
    }

    // 替换全局WebSocket
    window.WebSocket = createWebSocketProxy;

    // 验证替换是否成功
    console.log('[CtripIM Content] 🔄 WebSocket已替换为代理函数:', {
      isProxy: window.WebSocket === createWebSocketProxy,
      name: window.WebSocket.name,
      prototype: Object.getPrototypeOf(window.WebSocket)
    });

    // 标记已安装
    window._ctripWebSocketInterceptorInstalled = true;
    window._ctripWebSocketInterceptor = this;

    console.log('[CtripIM Content] ✅ WebSocket代理初始化完成');
    return true; // 表示初始化成功
  }

  /**
   * 获取调试信息
   */
  getDebugInfo() {
    return {
      activeConnections: this.proxyData.connectionStats.activeConnections,
      totalConnections: this.proxyData.connectionStats.totalConnections,
      installTime: this.proxyData.installTime,
      lastActivity: this.proxyData.connectionStats.lastActivity
    };
  }

  /**
   * 获取活跃连接
   */
  getActiveConnections() {
    return Array.from(this.proxyData.activeConnections.values());
  }

  /**
   * 初始化组件
   */
  async initializeComponents() {
    try {
      // 初始化连接管理器
      this.connectionManager = new ConnectionManager();
      await this.connectionManager.initialize();
      console.log('[CtripIM Content] 连接管理器初始化完成');

      // 初始化消息处理器
      this.messageProcessor = new MessageProcessor();
      await this.messageProcessor.initialize();
      console.log('[CtripIM Content] 消息处理器初始化完成');

    } catch (error) {
      console.error('[CtripIM Content] 组件初始化失败:', error);
      throw error;
    }
  }

  /**
   * 连接组件
   */
  connectComponents() {
    if (this.connectionManager && this.messageProcessor) {
      // 设置消息处理器到连接管理器
      this.connectionManager.setMessageProcessor(this.messageProcessor);
      console.log('[CtripIM Content] 组件连接完成');
    }
  }

  /**
   * 检查WebSocket拦截器状态
   */
  checkWebSocketInterceptor() {
    console.log('[CtripIM Content] 检查WebSocket拦截器状态...');
    
    if (window._ctripWebSocketInterceptorInstalled) {
      console.log('[CtripIM Content] ✅ WebSocket拦截器已安装');
      
      // 获取拦截器API
      this.webSocketInterceptor = window._ctripWebSocketInterceptor;
      
      if (this.webSocketInterceptor) {
        const debugInfo = this.webSocketInterceptor.getDebugInfo();
        console.log('[CtripIM Content] 📊 WebSocket拦截器数据:', {
          activeConnections: debugInfo.activeConnections,
          totalConnections: debugInfo.totalConnections,
          attemptedUrls: debugInfo.attemptedUrls.length,
          installTime: new Date(debugInfo.installTime).toISOString()
        });
      }
    } else {
      console.log('[CtripIM Content] ⚠️ WebSocket拦截器未安装');
    }
  }

  /**
   * 设置WebSocket拦截器事件监听
   */
  setupWebSocketInterceptorListeners() {
    // 监听WebSocket消息
    window.addEventListener('ctripWebSocketMessage', (event) => {
      const messageData = event.detail;
      
      console.log('[CtripIM Content] 📨 WebSocket消息:', {
        direction: messageData.direction,
        connectionId: messageData.connectionId,
        dataLength: messageData.message?.length || 0
      });
      
      // 转发给连接管理器
      if (this.connectionManager) {
        this.connectionManager.recordMessage(
          messageData.connectionId, 
          messageData.message, 
          messageData.direction
        );
      }
      
      // 转发给消息处理器
      if (this.messageProcessor) {
        this.messageProcessor.processMessage(messageData).then(processedMessage => {
          if (processedMessage) {
            this.notifyBackgroundScript('intercepted_message', processedMessage);
          }
        });
      }
    });
    
    // 监听WebSocket状态变化
    window.addEventListener('ctripWebSocketStatusChange', (event) => {
      const { connectionId, status, url, timestamp } = event.detail;
      
      console.log('[CtripIM Content] 🔗 WebSocket状态变化:', {
        connectionId, 
        status, 
        url,
        timestamp,
        currentTime: Date.now(),
        delay: Date.now() - timestamp
      });
      
      // 确保连接管理器知道这个状态变化
      if (this.connectionManager) {
        this.connectionManager.updateConnectionStatus(connectionId, status, {
          url,
          timestamp,
          source: 'websocket_interceptor'
        });
      }
      
      // 通知后台脚本
      this.notifyBackgroundScript('connection-status-changed', {
        connectionId: connectionId,
        status: status,
        url: url,
        timestamp: timestamp,
        source: 'websocket_interceptor'
      });
      
      // 立即更新popup状态
      this.notifyPopup('status-update', {
        status: status,
        connectionId: connectionId,
        timestamp: Date.now()
      });
    });
  }

  /**
   * 处理已存在的连接
   */
  processExistingConnections() {
    if (!this.webSocketInterceptor || !this.connectionManager) {
      return;
    }
    
    console.log('[CtripIM Content] 🔄 处理已存在的连接...');
    
    // 获取已拦截的连接
    const activeConnections = this.webSocketInterceptor.getActiveConnections();
    
    activeConnections.forEach(conn => {
      const connectionInfo = {
        id: conn.id,
        url: conn.url,
        protocols: conn.protocols || [],
        status: conn.status,
        createdAt: conn.createdAt,
        lastActivity: conn.lastActivity,
        messageCount: conn.messageCount,
        source: 'websocket_interceptor',
        isExisting: true
      };
      
      this.connectionManager.registerConnection(connectionInfo);
      
      console.log('[CtripIM Content] ✅ 已注册WebSocket连接:', conn.id);
    });
  }

  /**
   * 更新UI状态
   */
  updateUIStatus(data) {
    try {
      // 通知popup更新状态
      chrome.runtime.sendMessage({
        source: 'ctrip-im-content',
        action: 'update-ui-status',
        data: {
          ...data,
          timestamp: Date.now()
        }
      });
    } catch (error) {
      this.logger.error('更新UI状态失败:', error);
    }
  }

  /**
   * 设置消息监听器
   */
  setupMessageListener() {
    // 监听来自popup和background的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      // 对于异步操作，返回true保持消息通道打开
      return true;
    });
  }

  /**
   * 处理消息
   */
  handleMessage(request, sender, sendResponse) {
    const { source, action, data } = request;

    console.log('[CtripIM Content] 📨 收到消息:', { source, action, data });

    // 确保sendResponse总是被调用
    const safeResponse = (response) => {
      try {
        console.log('[CtripIM Content] 📤 发送响应:', response);
        sendResponse(response);
      } catch (error) {
        console.error('[CtripIM Content] ❌ 发送响应失败:', error);
      }
    };

    try {
      switch (action) {
        case 'get-status':
          try {
            const status = this.getStatus();
            safeResponse({
              success: true,
              data: status
            });
          } catch (error) {
            console.error('[CtripIM Content] ❌ 获取状态失败:', error);
            safeResponse({
              success: false,
              error: `获取状态失败: ${error.message}`
            });
          }
          break;

        case 'get-connections':
          try {
            const connections = this.connectionManager?.getAllConnections() || [];
            safeResponse({
              success: true,
              data: connections
            });
          } catch (error) {
            console.error('[CtripIM Content] ❌ 获取连接失败:', error);
            safeResponse({
              success: false,
              error: `获取连接失败: ${error.message}`
            });
          }
          break;

        case 'get-active-connections':
          try {
            const activeConnections = this.connectionManager?.getActiveConnections() || [];
            safeResponse({
              success: true,
              data: activeConnections
            });
          } catch (error) {
            console.error('[CtripIM Content] ❌ 获取活动连接失败:', error);
            safeResponse({
              success: false,
              error: `获取活动连接失败: ${error.message}`
            });
          }
          break;

        case 'get-message-history':
          try {
            const history = this.connectionManager?.getMessageHistory(data?.connectionId, data?.limit) || [];
            safeResponse({
              success: true,
              data: history
            });
          } catch (error) {
            console.error('[CtripIM Content] ❌ 获取消息历史失败:', error);
            safeResponse({
              success: false,
              error: `获取消息历史失败: ${error.message}`
            });
          }
          break;

        case 'export-data':
          try {
            const exportedData = this.exportData();
            safeResponse({
              success: true,
              data: exportedData
            });
          } catch (error) {
            console.error('[CtripIM Content] ❌ 导出数据失败:', error);
            safeResponse({
              success: false,
              error: `导出数据失败: ${error.message}`
            });
          }
          break;

        case 'force-reload':
          // 异步操作，使用Promise处理
          console.log('[CtripIM Content] 🔄 开始强制重载...');
          this.forceReload().then(() => {
            console.log('[CtripIM Content] ✅ 强制重载完成');
            safeResponse({ success: true });
          }).catch(error => {
            console.error('[CtripIM Content] ❌ 强制重载失败:', error);
            safeResponse({
              success: false,
              error: `强制重载失败: ${error.message}`
            });
          });
          break;

        case 'get-logs':
          try {
            // 获取日志数据
            const logs = this.getLogs();
            safeResponse({
              success: true,
              data: logs
            });
          } catch (error) {
            console.error('[CtripIM Content] ❌ 获取日志失败:', error);
            safeResponse({
              success: false,
              error: `获取日志失败: ${error.message}`
            });
          }
          break;

        case 'clear-logs':
          try {
            this.clearLogs();
            safeResponse({
              success: true,
              data: '日志已清空'
            });
          } catch (error) {
            console.error('[CtripIM Content] ❌ 清空日志失败:', error);
            safeResponse({
              success: false,
              error: `清空日志失败: ${error.message}`
            });
          }
          break;

        case 'get-settings':
          try {
            const settings = this.getSettings();
            safeResponse({
              success: true,
              data: settings
            });
          } catch (error) {
            console.error('[CtripIM Content] ❌ 获取设置失败:', error);
            safeResponse({
              success: false,
              error: `获取设置失败: ${error.message}`
            });
          }
          break;

        case 'update-setting':
          try {
            this.updateSetting(data?.key, data?.value);
            safeResponse({
              success: true,
              data: '设置已更新'
            });
          } catch (error) {
            console.error('[CtripIM Content] ❌ 更新设置失败:', error);
            safeResponse({
              success: false,
              error: `更新设置失败: ${error.message}`
            });
          }
          break;

        default:
          console.warn('[CtripIM Content] ⚠️ 未知操作:', action);
          safeResponse({
            success: false,
            error: `未知的操作: ${action}`
          });
      }
    } catch (error) {
      console.error('[CtripIM Content] ❌ 处理消息异常:', error);
      safeResponse({
        success: false,
        error: `处理消息失败: ${error.message}`
      });
    }
  }

  /**
   * 获取状态
   */
  getStatus() {
    const activeConnections = this.connectionManager?.getActiveConnections() || [];
    const stats = this.connectionManager?.getStats() || {};
    
    // 检查连接状态
    let status = 'disconnected';
    if (activeConnections.length > 0) {
      // 检查是否有任何连接处于错误状态
      const hasError = activeConnections.some(conn => conn.status === 'error');
      if (hasError) {
        status = 'error';
      } else {
        // 检查是否有正常连接的连接
        const hasConnected = activeConnections.some(conn => conn.status === 'connected');
        if (hasConnected) {
          status = 'connected';
        }
      }
    }
    
    return {
      initialized: this.initialized,
      webSocketIntercepted: !!window._ctripWebSocketInterceptorInstalled,
      activeConnections: activeConnections.length,
      totalConnections: stats.totalConnections || 0,
      messageCount: stats.totalMessages || 0,
      lastActivity: stats.lastActivity || null,
      status: status,
      interceptorStats: {
        installed: !!window._ctripWebSocketInterceptorInstalled,
        activeConnections: window._ctripWebSocketInterceptor?.getDebugInfo()?.activeConnections || 0,
        totalConnections: window._ctripWebSocketInterceptor?.getDebugInfo()?.totalConnections || 0,
        lastActivity: window._ctripWebSocketInterceptor?.getDebugInfo()?.lastActivity || null
      }
    };
  }

  /**
   * 导出数据
   */
  exportData() {
    return {
      connections: this.connectionManager?.exportData() || {},
      messageProcessor: {
        parsers: this.messageProcessor?.getParserTypes() || [],
        filters: this.messageProcessor?.getFilterNames() || []
      },
      timestamp: Date.now()
    };
  }

  /**
   * 获取日志
   */
  getLogs() {
    // 返回模拟的日志数据
    return [
      {
        level: 'info',
        message: 'Content Script已初始化',
        timestamp: Date.now() - 60000
      },
      {
        level: 'debug',
        message: 'WebSocket拦截器已安装',
        timestamp: Date.now() - 30000
      },
      {
        level: 'info',
        message: '连接管理器运行正常',
        timestamp: Date.now()
      }
    ];
  }

  /**
   * 清空日志
   */
  clearLogs() {
    console.log('[CtripIM Content] 📝 日志已清空');
    // 实际实现中可以清空日志存储
  }

  /**
   * 获取设置
   */
  getSettings() {
    return {
      enableInterceptor: true,
      enableAutoReply: false,
      logLevel: 'info',
      enableStorageLog: true
    };
  }

  /**
   * 更新设置
   */
  updateSetting(key, value) {
    console.log(`[CtripIM Content] ⚙️ 更新设置: ${key} = ${value}`);
    // 实际实现中可以保存到storage
    if (this.config) {
      this.config.set(key, value);
    }
  }

  /**
   * 强制重新加载
   */
  async forceReload() {
    try {
      this.logger.info('强制重新加载...');

      // 销毁现有组件
      this.destroy();

      // 重新初始化
      await this.initialize();

      this.logger.info('强制重新加载完成');
    } catch (error) {
      this.logger.exception(error, '强制重新加载失败');
    }
  }

  /**
   * 通知后台脚本
   */
  notifyBackgroundScript(action, data) {
    try {
      chrome.runtime.sendMessage({
        source: 'ctrip-im-content',
        action,
        data
      });
    } catch (error) {
      console.error('[CtripIM Content] 通知后台脚本失败:', error.message);
    }
  }

  /**
   * 通知popup
   */
  notifyPopup(action, data) {
    try {
      chrome.runtime.sendMessage({
        source: 'ctrip-im-content',
        action,
        data
      });
    } catch (error) {
      console.error('[CtripIM Content] 通知popup失败:', error.message);
    }
  }

  /**
   * 销毁内容脚本
   */
  destroy() {
    this.logger.info('销毁携程IM内容脚本');
    
    // 恢复原始WebSocket
    if (this.OriginalWebSocket) {
      window.WebSocket = this.OriginalWebSocket;
    }
    
    // 清理WebSocket相关数据
    this.proxyData.activeConnections.clear();
    this.proxyData.messageHandlers.clear();
    
    if (this.connectionManager) {
      this.connectionManager.destroy();
      this.connectionManager = null;
    }
    
    if (this.messageProcessor) {
      this.messageProcessor.destroy();
      this.messageProcessor = null;
    }
    
    // 移除标记
    delete window._ctripWebSocketInterceptorInstalled;
    delete window._ctripWebSocketInterceptor;
    
    this.initialized = false;
  }
}

// 创建全局实例
const ctripIMContentScript = new CtripIMContentScript();

// 初始化重试机制
let initRetries = 0;
const MAX_RETRIES = 3;
const RETRY_DELAY = 2000;

function initializeWithRetry() {
  console.log(`[CtripIM Content] 开始第 ${initRetries + 1} 次初始化尝试`);

  // 检查页面URL是否匹配（与manifest.json保持一致）
  const currentUrl = window.location.href;
  const supportedDomains = [
    'ctrip.com',
    'trip.com',
    'ctripbiz.com',
    'tripbiz.com'
  ];

  const isTargetSite = supportedDomains.some(domain => currentUrl.includes(domain));

  if (!isTargetSite) {
    console.log('[CtripIM Content] 当前页面不是目标网站，跳过初始化');
    console.log('[CtripIM Content] 当前URL:', currentUrl);
    console.log('[CtripIM Content] 支持的域名:', supportedDomains);
    return;
  }

  console.log('[CtripIM Content] ✅ 检测到目标网站，开始初始化...');

  ctripIMContentScript.initialize().catch(error => {
    console.error('[CtripIM Content] 初始化失败:', error);

    if (initRetries < MAX_RETRIES) {
      initRetries++;
      console.log(`[CtripIM Content] ${RETRY_DELAY/1000}秒后进行第${initRetries + 1}次重试...`);
      setTimeout(initializeWithRetry, RETRY_DELAY);
    } else {
      console.error('[CtripIM Content] 达到最大重试次数，初始化失败');
    }
  });
}

// 根据文档状态决定初始化时机
function startInitialization() {
  console.log('[CtripIM Content] 准备开始初始化，当前文档状态:', document.readyState);
  
  if (document.readyState === 'loading') {
    console.log('[CtripIM Content] 文档加载中，等待 DOMContentLoaded 事件...');
    document.addEventListener('DOMContentLoaded', () => {
      console.log('[CtripIM Content] DOMContentLoaded 事件触发，开始初始化...');
      setTimeout(initializeWithRetry, 1000);
    });
  } else {
    console.log('[CtripIM Content] 文档已加载完成，直接开始初始化...');
    initializeWithRetry();
  }
}

// 开始初始化流程
startInitialization();

// 导出全局对象供调试使用
window.ctripIMContentScript = ctripIMContentScript;

// 简化的调试接口
window.ctripIM = {
  getStatus: () => ctripIMContentScript.getStatus(),
  getConnections: () => ctripIMContentScript.connectionManager?.getActiveConnections() || [],
  exportData: () => ctripIMContentScript.exportData(),
  reload: () => ctripIMContentScript.forceReload(),
  
  // 检查WebSocket拦截器状态
  checkWS: () => {
    console.log('🔍 检查WebSocket拦截器状态...');
    
    const hasInterceptor = !!window._ctripWebSocketInterceptorInstalled;
    const interceptorApi = window._ctripWebSocketInterceptor;
    
    console.log('📊 WebSocket拦截器状态:', {
      installed: hasInterceptor,
      hasApi: !!interceptorApi
    });
    
    if (hasInterceptor && interceptorApi) {
      const debugInfo = interceptorApi.getDebugInfo();
      console.log('📋 WebSocket拦截器数据:', {
        activeConnections: debugInfo.activeConnections,
        totalConnections: debugInfo.totalConnections,
        attemptedUrls: debugInfo.attemptedUrls.length,
        installTime: new Date(debugInfo.installTime).toLocaleString()
      });
      
      if (debugInfo.attemptedUrls.length > 0) {
        console.log('🌐 尝试的WebSocket URL:');
        console.table(debugInfo.attemptedUrls.map(url => ({
          时间: new Date(url.timestamp).toLocaleTimeString(),
          URL: url.url,
          协议: url.protocols ? JSON.stringify(url.protocols) : '无',
          '是携程IM': url.url && (/ws-xmpp|im3\.ctrip|im\.ctrip/.test(url.url)) ? '是' : '否'
        })));
      }
      
             const activeConnections = interceptorApi.getActiveConnections();
       if (activeConnections.length > 0) {
         console.log('🎯 活跃的WebSocket连接:');
         console.table(activeConnections.map(conn => ({
          ID: conn.id,
          时间: new Date(conn.createdAt).toLocaleTimeString(),
          URL: conn.url,
          状态: conn.status
        })));
      }
    } else {
      console.log('❌ WebSocket拦截器未安装或无数据');
    }
    
         // 检查当前WebSocket状态
     console.log('🔍 当前WebSocket状态:', {
       hasWebSocket: !!window.WebSocket,
       isIntercepted: !!(window.WebSocket && window.WebSocket._ctripIntercepted),
       hasOriginal: !!(window.WebSocket && window.WebSocket._originalWebSocket)
     });
     
     return {
       hasInterceptor,
       interceptorApi,
       webSocketStatus: {
         hasWebSocket: !!window.WebSocket,
         isIntercepted: !!(window.WebSocket && window.WebSocket._ctripIntercepted)
       }
     };
  },
  
  // 测试拦截器
  test: () => {
    console.log('🧪 测试WebSocket拦截器...');
    try {
      const testWs = new WebSocket('wss://echo.websocket.org');
      testWs.onopen = () => {
        console.log('✅ 测试连接成功，拦截器工作正常');
        testWs.close();
      };
      testWs.onerror = (error) => {
        console.log('❌ 测试连接失败:', error);
      };
      return '测试中...';
    } catch (error) {
      console.error('❌ 创建测试WebSocket失败:', error);
      return '测试失败';
    }
  }
};

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
  ctripIMContentScript.destroy();
});

// 处理页面可见性变化
document.addEventListener('visibilitychange', () => {
  if (document.visibilityState === 'visible') {
    // 页面变为可见时，检查拦截器状态
    if (ctripIMContentScript.initialized && ctripIMContentScript.webSocketInterceptor) {
      ctripIMContentScript.logger.debug('页面变为可见，检查拦截器状态');
    }
  }
});

export default ctripIMContentScript; 