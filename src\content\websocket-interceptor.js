// 携程IM WebSocket代理 - 专门用于创建携程IM的WebSocket连接

(function () {
  'use strict';

  console.log('[CtripIM WebSocket] 🚀 携程IM WebSocket代理初始化...');

  // 检查是否已经安装
  if (window._ctripWebSocketInterceptorInstalled) {
    console.log('[CtripIM WebSocket] ⚠️ 携程IM WebSocket代理已安装，跳过');
    return;
  }

  // 保存原始WebSocket
  const OriginalWebSocket = window.WebSocket;
  if (!OriginalWebSocket) {
    console.log('[CtripIM WebSocket] ❌ WebSocket不可用');
    return;
  }

  console.log('[CtripIM WebSocket] 💾 原始WebSocket已保存:', OriginalWebSocket.name);

  // 代理数据存储
  const proxyData = {
    activeConnections: new Map(),
    connectionStats: {
      totalConnections: 0,
      activeConnections: 0,
      messagesReceived: 0,
      messagesSent: 0,
      lastActivity: null
    },
    messageHandlers: new Set(),
    installTime: Date.now()
  };

  // 生成连接ID
  function generateConnectionId() {
    return 'ws_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // 生成消息ID
  function generateMessageId() {
    return 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // 处理消息
  function handleMessage(connectionId, data, direction) {
    try {
      const connectionInfo = proxyData.activeConnections.get(connectionId);
      if (!connectionInfo) return;

      // 更新统计
      if (direction === 'received') {
        proxyData.connectionStats.messagesReceived++;
        
        // 检查是否是用户发来的消息
        if (typeof data === 'string' && data.includes('type="groupchat"')) {
          try {
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(data, 'text/xml');
            const messageElement = xmlDoc.querySelector('message');
            
            if (messageElement && 
                messageElement.getAttribute('type') === 'groupchat' && 
                messageElement.querySelector('body')) {
              
              // 获取必要的信息
              const threadId = messageElement.getAttribute('threadid');
              const toJid = messageElement.getAttribute('to').split('/')[0];
              const fromJid = messageElement.getAttribute('from').split('/')[0];
              
              // 构造回复消息
              const replyId = 'reply_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
              const replyMessage = `<message to="${fromJid}/csm0000001004960" type="send" id="${replyId}" xmlns="jabber:client"><body>{"chattype":"groupchat","biztype":"2107","localid":"${replyId}","msg":"你好！请问有什么需要帮助的？","msgtype":"0","autoextend":0,"threadid":"${threadId}"}</body></message>`;
              
              // 发送回复
              if (connectionInfo.websocket.readyState === WebSocket.OPEN) {
                connectionInfo.websocket.send(replyMessage);
                console.log('[CtripIM WebSocket] 🤖 已发送自动回复:', replyMessage);
              }
            }
          } catch (error) {
            console.error('[CtripIM WebSocket] ❌ 解析XML消息失败:', error);
          }
        }
      } else {
        proxyData.connectionStats.messagesSent++;
      }

      proxyData.connectionStats.lastActivity = Date.now();
      connectionInfo.lastActivity = Date.now();
      connectionInfo.messageCount++;

      console.log(`[CtripIM WebSocket] 📨 ${direction === 'sent' ? '发送' : '接收'}消息:`, {
        connectionId: connectionId,
        url: connectionInfo.url,
        dataLength: data?.length || 0,
        data: typeof data === 'string' ? data.slice(0, 100) : '非文本数据' // 只显示前100个字符
      });

      // 创建消息数据
      const messageData = {
        id: generateMessageId(),
        connectionId: connectionId,
        message: data,
        direction: direction,
        timestamp: Date.now(),
        isCtripProxy: true,
        source: 'ctrip_websocket_proxy'
      };

      // 触发自定义事件
      window.dispatchEvent(new CustomEvent('ctripWebSocketMessage', {
        detail: messageData
      }));

    } catch (error) {
      console.error('[CtripIM WebSocket] ❌ 处理消息失败:', error);
    }
  }

  // 更新连接状态
  function updateConnectionStatus(connectionId, status) {
    const connectionInfo = proxyData.activeConnections.get(connectionId);
    if (connectionInfo) {
      connectionInfo.status = status;
      connectionInfo.lastActivity = Date.now();

      console.log('[CtripIM WebSocket] 📊 连接状态更新:', {
        connectionId,
        status,
        url: connectionInfo.url,
        protocol: connectionInfo.protocols
      });

      // 触发状态变化事件
      window.dispatchEvent(new CustomEvent('ctripWebSocketStatusChange', {
        detail: {
          connectionId: connectionId,
          status: status,
          url: connectionInfo.url,
          timestamp: Date.now()
        }
      }));
    }
  }

  // 设置WebSocket事件监听器
  function setupWebSocketEventListeners(websocket, connectionId) {
    const connectionInfo = proxyData.activeConnections.get(connectionId);
    let heartbeatInterval = null;
    let lastPongTime = null;

    // 心跳检测函数
    function startHeartbeat() {
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
      }
      
      heartbeatInterval = setInterval(() => {
        try {
          if (websocket.readyState === WebSocket.OPEN) {
            websocket.send(JSON.stringify({
              type: 'ping',
              timestamp: Date.now()
            }));
            
            // 如果超过10秒没有收到pong响应，认为连接有问题
            if (lastPongTime && Date.now() - lastPongTime > 10000) {
              console.log('[CtripIM WebSocket] ⚠️ 心跳检测超时:', {
                connectionId,
                lastPongTime: new Date(lastPongTime).toISOString()
              });
              updateConnectionStatus(connectionId, 'error');
            }
          }
        } catch (error) {
          console.error('[CtripIM WebSocket] ❌ 发送心跳消息失败:', error);
          updateConnectionStatus(connectionId, 'error');
        }
      }, 30000); // 每30秒发送一次心跳
    }

    // 拦截发送方法
    const originalSend = websocket.send;
    websocket.send = function (data) {
      handleMessage(connectionId, data, 'sent');
      return originalSend.call(this, data);
    };

    // 拦截消息事件
    const originalAddEventListener = websocket.addEventListener;
    websocket.addEventListener = function (type, listener, options) {
      if (type === 'message') {
        const wrappedListener = function (event) {
          // 处理pong响应
          if (typeof event.data === 'string') {
            try {
              const data = JSON.parse(event.data);
              if (data.type === 'pong') {
                lastPongTime = Date.now();
                return;
              }
            } catch (e) {
              // 不是JSON或不是pong消息，当作普通消息处理
            }
          }
          
          handleMessage(connectionId, event.data, 'received');
          return listener.call(this, event);
        };
        return originalAddEventListener.call(this, type, wrappedListener, options);
      }
      return originalAddEventListener.call(this, type, listener, options);
    };

    // 拦截onmessage属性
    let originalOnMessage = websocket.onmessage;
    Object.defineProperty(websocket, 'onmessage', {
      get: () => originalOnMessage,
      set: (handler) => {
        originalOnMessage = handler;
        websocket.onmessage = (event) => {
          // 处理pong响应
          if (typeof event.data === 'string') {
            try {
              const data = JSON.parse(event.data);
              if (data.type === 'pong') {
                lastPongTime = Date.now();
                return;
              }
            } catch (e) {
              // 不是JSON或不是pong消息，当作普通消息处理
            }
          }
          
          handleMessage(connectionId, event.data, 'received');
          if (handler) handler(event);
        };
      }
    });

    // 监听连接状态
    websocket.addEventListener('open', function () {
      console.log('[CtripIM WebSocket] ✅ WebSocket连接已打开:', {
        url: connectionInfo?.url,
        protocol: websocket.protocol,
        extensions: websocket.extensions,
        readyState: websocket.readyState,
        connectionId: connectionId,
        timestamp: Date.now()
      });
      
      // 更新连接状态
      updateConnectionStatus(connectionId, 'connected');
      
      // 启动心跳检测
      startHeartbeat();
      
      // 立即发送一个测试消息来验证连接
      try {
        websocket.send(JSON.stringify({
          type: 'ping',
          timestamp: Date.now()
        }));
        console.log('[CtripIM WebSocket] 🏓 发送测试消息成功');
      } catch (error) {
        console.error('[CtripIM WebSocket] ❌ 发送测试消息失败:', error);
        updateConnectionStatus(connectionId, 'error');
      }
    });

    websocket.addEventListener('close', function (event) {
      console.log('[CtripIM WebSocket] ❌ WebSocket连接已关闭:', {
        url: connectionInfo?.url,
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean
      });
      
      // 清理心跳检测
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
        heartbeatInterval = null;
      }
      
      updateConnectionStatus(connectionId, 'closed');

      // 更新活跃连接数
      if (proxyData.connectionStats.activeConnections > 0) {
        proxyData.connectionStats.activeConnections--;
      }
    });

    websocket.addEventListener('error', function (error) {
      console.log('[CtripIM WebSocket] 🚫 WebSocket连接错误:', {
        url: connectionInfo?.url,
        error: error
      });
      
      // 清理心跳检测
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
        heartbeatInterval = null;
      }
      
      updateConnectionStatus(connectionId, 'error');
    });
  }

  // 创建WebSocket代理
  function createWebSocketProxy(url, protocols) {
    const connectionId = generateConnectionId();
    
    console.log('[CtripIM WebSocket] 🔍 创建新的WebSocket连接:', {
      url,
      protocols,
      connectionId,
      origin: window.location.origin,
      host: new URL(url).host
    });

    // 创建原始WebSocket连接
    const originalWebSocket = new OriginalWebSocket(url, protocols);

    // 更新统计信息
    proxyData.connectionStats.totalConnections++;
    proxyData.connectionStats.activeConnections++;
    proxyData.connectionStats.lastActivity = Date.now();

    // 存储连接信息
    const connectionInfo = {
      id: connectionId,
      url: url,
      protocols: protocols,
      status: 'connecting',
      createdAt: Date.now(),
      lastActivity: Date.now(),
      messageCount: 0,
      websocket: originalWebSocket,
      source: 'ctrip_proxy'
    };

    proxyData.activeConnections.set(connectionId, connectionInfo);

    // 设置事件监听器
    setupWebSocketEventListeners(originalWebSocket, connectionId);

    return originalWebSocket;
  }

  // 复制原始WebSocket的属性和方法
  try {
    Object.setPrototypeOf(createWebSocketProxy.prototype, OriginalWebSocket.prototype);
    Object.setPrototypeOf(createWebSocketProxy, OriginalWebSocket);

    // 复制静态属性（跳过只读常量）
    const readOnlyProperties = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];
    for (const key in OriginalWebSocket) {
      if (OriginalWebSocket.hasOwnProperty(key) && !readOnlyProperties.includes(key)) {
        try {
          const descriptor = Object.getOwnPropertyDescriptor(OriginalWebSocket, key);
          if (descriptor && descriptor.writable !== false) {
            createWebSocketProxy[key] = OriginalWebSocket[key];
          }
        } catch (e) {
          // 忽略只读属性错误
          console.debug('[CtripIM WebSocket] 跳过只读属性:', key, e.message);
        }
      }
    }

    // 使用Object.defineProperty安全地设置WebSocket常量
    try {
      Object.defineProperty(createWebSocketProxy, 'CONNECTING', {
        value: 0,
        writable: false,
        enumerable: true,
        configurable: false
      });
      Object.defineProperty(createWebSocketProxy, 'OPEN', {
        value: 1,
        writable: false,
        enumerable: true,
        configurable: false
      });
      Object.defineProperty(createWebSocketProxy, 'CLOSING', {
        value: 2,
        writable: false,
        enumerable: true,
        configurable: false
      });
      Object.defineProperty(createWebSocketProxy, 'CLOSED', {
        value: 3,
        writable: false,
        enumerable: true,
        configurable: false
      });
    } catch (e) {
      console.warn('[CtripIM WebSocket] ⚠️ 设置WebSocket常量时出错:', e.message);
      // 如果defineProperty失败，尝试直接赋值（可能在某些环境中有效）
      try {
        createWebSocketProxy.CONNECTING = 0;
        createWebSocketProxy.OPEN = 1;
        createWebSocketProxy.CLOSING = 2;
        createWebSocketProxy.CLOSED = 3;
      } catch (e2) {
        console.warn('[CtripIM WebSocket] ⚠️ 直接赋值WebSocket常量也失败:', e2.message);
      }
    }

  } catch (e) {
    console.error('[CtripIM WebSocket] ❌ 初始化WebSocket代理时出错:', e);
    // 如果代理初始化失败，确保原始WebSocket仍然可用
    if (!window.WebSocket) {
      window.WebSocket = OriginalWebSocket;
    }
    return false; // 表示初始化失败
  }

  // 替换全局WebSocket
  window.WebSocket = createWebSocketProxy;

  // 验证替换是否成功
  console.log('[CtripIM WebSocket] 🔄 WebSocket已替换为代理函数:', {
    isProxy: window.WebSocket === createWebSocketProxy,
    name: window.WebSocket.name,
    prototype: Object.getPrototypeOf(window.WebSocket)
  });

  // 标记已安装
  window._ctripWebSocketInterceptorInstalled = true;

  console.log('[CtripIM WebSocket] ✅ WebSocket代理初始化完成');
  return true; // 表示初始化成功

})(); 