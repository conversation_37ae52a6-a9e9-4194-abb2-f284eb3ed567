/**
 * 消息处理器
 * 负责解析和处理携程IM消息
 */
import { EventEmitter } from '../utils/event-emitter.js';
import { messageLogger } from '../utils/logger.js';

export class MessageProcessor extends EventEmitter {
  constructor() {
    super();
    this.logger = messageLogger;
    this.parsers = new Map();
    this.filters = new Set();
    this.initialized = false;
  }

  /**
   * 初始化消息处理器
   */
  async initialize() {
    if (this.initialized) {
      this.logger.warn('消息处理器已经初始化');
      return;
    }

    try {
      this.logger.info('初始化消息处理器...');
      
      // 注册内置解析器
      this.registerBuiltinParsers();
      
      // 注册内置过滤器
      this.registerBuiltinFilters();
      
      this.initialized = true;
      this.logger.info('消息处理器初始化完成');
    } catch (error) {
      this.logger.exception(error, '初始化失败');
      throw error;
    }
  }

  /**
   * 处理消息
   */
  async processMessage(messageData) {
    if (!this.initialized) {
      this.logger.warn('消息处理器未初始化');
      return messageData;
    }

    try {
      const { message, direction, connectionId } = messageData;
      
      // 应用过滤器
      const filtered = await this.applyFilters(messageData);
      if (!filtered) {
        this.logger.debug('消息被过滤器拒绝:', { connectionId, direction });
        return null;
      }

      // 解析消息
      const parsed = await this.parseMessage(message, direction);
      
      // 创建处理后的消息数据
      const processedData = {
        ...messageData,
        parsed,
        processed: true,
        processingTime: Date.now()
      };

      this.logger.debug('消息处理完成:', { connectionId, direction, hasContent: !!parsed.content });
      
      // 发射事件
      this.emit('message-processed', processedData);
      
      return processedData;
    } catch (error) {
      this.logger.exception(error, '消息处理失败');
      
      // 返回带错误信息的消息数据
      return {
        ...messageData,
        error: error.message,
        processed: false,
        processingTime: Date.now()
      };
    }
  }

  /**
   * 解析消息
   */
  async parseMessage(message, direction) {
    const result = {
      raw: message,
      direction,
      type: 'unknown',
      content: null,
      metadata: {},
      timestamp: Date.now()
    };

    try {
      // 尝试各个解析器
      for (const [type, parser] of this.parsers) {
        try {
          const parsed = await parser.parse(message, direction);
          if (parsed) {
            result.type = type;
            result.content = parsed.content;
            result.metadata = parsed.metadata || {};
            break;
          }
        } catch (error) {
          this.logger.debug(`${type}解析器失败:`, error.message);
        }
      }

      // 如果没有解析器成功，使用原始消息
      if (!result.content) {
        result.content = message;
        result.type = 'raw';
      }

      return result;
    } catch (error) {
      this.logger.exception(error, '解析消息失败');
      return result;
    }
  }

  /**
   * 应用过滤器
   */
  async applyFilters(messageData) {
    for (const filter of this.filters) {
      try {
        const passed = await filter.filter(messageData);
        if (!passed) {
          this.logger.debug('消息被过滤器拒绝:', filter.name);
          return false;
        }
      } catch (error) {
        this.logger.exception(error, '过滤器错误');
        // 过滤器错误时允许消息通过
      }
    }
    return true;
  }

  /**
   * 注册消息解析器
   */
  registerParser(type, parser) {
    if (typeof parser.parse !== 'function') {
      throw new Error('解析器必须有parse方法');
    }
    
    this.parsers.set(type, parser);
    this.logger.info('已注册解析器:', type);
  }

  /**
   * 注册消息过滤器
   */
  registerFilter(filter) {
    if (typeof filter.filter !== 'function') {
      throw new Error('过滤器必须有filter方法');
    }
    
    this.filters.add(filter);
    this.logger.info('已注册过滤器:', filter.name);
  }

  /**
   * 注册内置解析器
   */
  registerBuiltinParsers() {
    // XMPP消息解析器
    this.registerParser('xmpp', new XMPPParser());
    
    // JSON消息解析器
    this.registerParser('json', new JSONParser());
    
    // 纯文本解析器
    this.registerParser('text', new TextParser());
  }

  /**
   * 注册内置过滤器
   */
  registerBuiltinFilters() {
    // 长度过滤器
    this.registerFilter(new LengthFilter());
    
    // 内容过滤器
    this.registerFilter(new ContentFilter());
  }

  /**
   * 获取解析器列表
   */
  getParserTypes() {
    return Array.from(this.parsers.keys());
  }

  /**
   * 获取过滤器列表
   */
  getFilterNames() {
    return Array.from(this.filters).map(filter => filter.name);
  }

  /**
   * 移除解析器
   */
  removeParser(type) {
    this.parsers.delete(type);
    this.logger.info('已移除解析器:', type);
  }

  /**
   * 移除过滤器
   */
  removeFilter(filter) {
    this.filters.delete(filter);
    this.logger.info('已移除过滤器:', filter.name);
  }

  /**
   * 销毁消息处理器
   */
  destroy() {
    this.logger.info('销毁消息处理器');
    
    this.parsers.clear();
    this.filters.clear();
    this.removeAllListeners();
    
    this.initialized = false;
  }
}

/**
 * XMPP消息解析器
 */
class XMPPParser {
  constructor() {
    this.name = 'XMPP';
  }

  async parse(message, direction) {
    try {
      // 检查是否是XMPP消息
      if (!message.includes('<') || !message.includes('>')) {
        return null;
      }

      const parser = new DOMParser();
      const doc = parser.parseFromString(message, 'text/xml');
      
      if (doc.documentElement.tagName === 'parsererror') {
        return null;
      }

      const rootElement = doc.documentElement;
      const result = {
        content: this.extractContent(rootElement),
        metadata: {
          type: rootElement.tagName,
          attributes: this.extractAttributes(rootElement),
          namespace: rootElement.namespaceURI
        }
      };

      return result;
    } catch (error) {
      return null;
    }
  }

  extractContent(element) {
    if (element.tagName === 'message') {
      const bodyElement = element.querySelector('body');
      if (bodyElement) {
        return bodyElement.textContent || '';
      }
    }
    
    return element.textContent || '';
  }

  extractAttributes(element) {
    const attributes = {};
    for (const attr of element.attributes) {
      attributes[attr.name] = attr.value;
    }
    return attributes;
  }
}

/**
 * JSON消息解析器
 */
class JSONParser {
  constructor() {
    this.name = 'JSON';
  }

  async parse(message, direction) {
    try {
      const data = JSON.parse(message);
      return {
        content: this.extractContent(data),
        metadata: {
          type: 'json',
          keys: Object.keys(data),
          structure: this.analyzeStructure(data)
        }
      };
    } catch (error) {
      return null;
    }
  }

  extractContent(data) {
    // 尝试提取常见的内容字段
    const contentFields = ['content', 'message', 'text', 'body', 'data'];
    
    for (const field of contentFields) {
      if (data[field]) {
        return data[field];
      }
    }
    
    // 如果没有找到内容字段，返回整个对象的字符串表示
    return JSON.stringify(data, null, 2);
  }

  analyzeStructure(data) {
    const structure = {};
    
    for (const [key, value] of Object.entries(data)) {
      structure[key] = typeof value;
    }
    
    return structure;
  }
}

/**
 * 文本消息解析器
 */
class TextParser {
  constructor() {
    this.name = 'Text';
  }

  async parse(message, direction) {
    return {
      content: message,
      metadata: {
        type: 'text',
        length: message.length,
        encoding: this.detectEncoding(message)
      }
    };
  }

  detectEncoding(text) {
    // 简单的编码检测
    try {
      encodeURIComponent(text);
      return 'UTF-8';
    } catch (error) {
      return 'Unknown';
    }
  }
}

/**
 * 长度过滤器
 */
class LengthFilter {
  constructor() {
    this.name = 'Length';
    this.maxLength = 1000000; // 1MB
    this.minLength = 0;
  }

  async filter(messageData) {
    const { message } = messageData;
    const length = message.length;
    
    return length >= this.minLength && length <= this.maxLength;
  }
}

/**
 * 内容过滤器
 */
class ContentFilter {
  constructor() {
    this.name = 'Content';
    this.blockedPatterns = [
      /^ping$/i,
      /^pong$/i,
      /^heartbeat$/i
    ];
  }

  async filter(messageData) {
    const { message } = messageData;
    
    // 检查是否匹配阻止模式
    for (const pattern of this.blockedPatterns) {
      if (pattern.test(message)) {
        return false;
      }
    }
    
    return true;
  }
} 