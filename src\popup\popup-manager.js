console.log('==== popup-manager 入口文件已加载 ====');
/**
 * 弹窗管理器
 * 负责管理弹窗界面的所有逻辑
 */
import { EventEmitter } from '../utils/event-emitter.js';
import { popupLogger } from '../utils/logger.js';
import PerformanceUtils from '../utils/performance-utils.js';

export class PopupManager extends EventEmitter {
  constructor() {
    super();
    this.logger = popupLogger;
    this.currentTab = null;
    this.data = {
      connections: [],
      messageHistory: [],
      stats: {},
      status: 'disconnected'
    };
    this.refreshInterval = null;
    this.initialized = false;
    
    // 性能优化
    this.cache = new PerformanceUtils.Cache(50, 30000); // 30秒缓存
    this.performanceMonitor = new PerformanceUtils.PerformanceMonitor('PopupManager');
    
    // 节流优化的方法
    this.throttledUpdateUI = PerformanceUtils.throttle(this.updateUI.bind(this), 100);
    this.debouncedRefresh = PerformanceUtils.debounce(this.refreshData.bind(this), 500);
  }

  /**
   * 初始化弹窗管理器
   */
  async initialize() {
    if (this.initialized) {
      this.logger.warn('弹窗管理器已经初始化');
      return;
    }

    try {
      this.logger.info('初始化弹窗管理器...');
      
      // 获取当前标签页
      await this.getCurrentTab();
      
      // 设置事件监听器
      this.setupEventListeners();
      
      // 初始化UI
      await this.initializeUI();
      
      // 加载数据
      await this.loadData();
      
      // 启动定时刷新
      this.startRefreshTimer();
      
      this.initialized = true;
      this.logger.info('弹窗管理器初始化完成');
    } catch (error) {
      this.logger.error('初始化失败:', error);
      throw error;
    }
  }

  /**
   * 获取当前标签页
   */
  async getCurrentTab() {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      this.currentTab = tabs[0];
      this.logger.debug('当前标签页:', this.currentTab?.url);
    } catch (error) {
      this.logger.error('获取当前标签页失败:', error);
    }
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 监听DOM加载完成
    document.addEventListener('DOMContentLoaded', () => {
      this.setupUIEventListeners();
    });

    // 监听来自content script的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.source === 'ctrip-im-content') {
        this.handleContentMessage(request, sendResponse);
      }
    });
  }

  /**
   * 设置UI事件监听器
   */
  setupUIEventListeners() {
    // 刷新按钮
    const refreshBtn = document.getElementById('refreshBtn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => this.refreshData());
    }

    // 清理历史按钮
    const clearHistoryBtn = document.getElementById('clearHistoryBtn');
    if (clearHistoryBtn) {
      clearHistoryBtn.addEventListener('click', () => this.clearHistory());
    }

    // 导出数据按钮
    const exportBtn = document.getElementById('exportBtn');
    if (exportBtn) {
      exportBtn.addEventListener('click', () => this.exportData());
    }

    // 设置按钮
    const settingsBtn = document.getElementById('settingsBtn');
    if (settingsBtn) {
      settingsBtn.addEventListener('click', () => this.openSettings());
    }

    // 连接列表项点击
    const connectionList = document.getElementById('connectionList');
    if (connectionList) {
      connectionList.addEventListener('click', (e) => {
        if (e.target.classList.contains('connection-item')) {
          this.selectConnection(e.target.dataset.connectionId);
        }
      });
    }

    // 消息发送
    const sendBtn = document.getElementById('sendBtn');
    const messageInput = document.getElementById('messageInput');
    if (sendBtn && messageInput) {
      sendBtn.addEventListener('click', () => this.sendMessage());
      messageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          this.sendMessage();
        }
      });
    }

    // 标签页切换
    const tabs = document.querySelectorAll('.tab-button');
    tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        this.switchTab(tab.dataset.tab);
      });
    });
  }

  /**
   * 初始化UI
   */
  async initializeUI() {
    try {
      // 设置版本信息
      const versionElement = document.getElementById('version');
      if (versionElement) {
        versionElement.textContent = chrome.runtime.getManifest().version;
      }

      // 设置初始状态
      this.updateStatus('loading');
      
      // 显示当前URL
      const urlElement = document.getElementById('currentUrl');
      if (urlElement && this.currentTab) {
        urlElement.textContent = this.currentTab.url;
      }

      // 初始化图表
      this.initializeCharts();
      
    } catch (error) {
      this.logger.error('初始化UI失败:', error);
    }
  }

  /**
   * 加载数据
   */
  async loadData() {
    try {
      console.log('[CtripIM Popup] 🔄 开始加载数据...');
      
      // 检查是否是携程网站
      if (!this.isCtripSite()) {
        this.updateStatus('not-ctrip-site');
        this.showNotification('请在携程网站页面使用此扩展', 'warning');
        return;
      }

      // 先设置为加载状态
      this.updateStatus('loading');

      // 从content script获取数据
      const result = await this.sendMessageToContent('get-status');
      
      console.log('[CtripIM Popup] 📥 获取状态结果:', result);

      if (!result?.success) {
        this.handleError(result?.error || '获取状态失败');
        return;
      }

      // 更新状态
      if (result.data) {
        const { status, activeConnections, totalConnections, messageCount, lastActivity } = result.data;
        
        // 更新连接状态
        this.updateStatus(status);
        
        // 更新统计信息
        this.updateStats({
          activeConnections,
          totalConnections,
          messageCount,
          lastActivity
        });
      }

    } catch (error) {
      console.error('[CtripIM Popup] ❌ 加载数据失败:', error);
      this.handleError(error);
    }
  }

  /**
   * 处理连接错误
   */
  handleConnectionError(result) {
    switch (result.code) {
      case 'NOT_CTRIP_SITE':
        this.updateStatus('not-ctrip-site');
        this.showNotification('请在携程网站页面使用此扩展', 'warning');
        break;
      case 'CONTENT_SCRIPT_NOT_LOADED':
        this.updateStatus('error');
        this.showNotification('扩展未完全加载，请刷新页面重试', 'error');
        break;
      case 'CONNECTION_ERROR':
        this.updateStatus('error');
        this.showNotification('连接失败: ' + result.error, 'error');
        break;
      default:
        this.updateStatus('error');
        this.showNotification('连接错误: ' + result.error, 'error');
    }
  }

  /**
   * 加载连接信息
   */
  async loadConnections() {
    try {
      const result = await this.sendMessageToContent('get-connections');
      if (result.success) {
        this.data.connections = result.data || [];
        this.updateConnectionList();
      } else {
        this.logger.debug('加载连接信息失败:', result.error);
        this.data.connections = [];
      }
    } catch (error) {
      this.logger.error('加载连接信息失败:', error);
      this.data.connections = [];
    }
  }

  /**
   * 加载消息历史
   */
  async loadMessageHistory() {
    try {
      const result = await this.sendMessageToContent('get-message-history', { limit: 50 });
      if (result.success) {
        this.data.messageHistory = result.data || [];
        this.updateMessageHistory();
      } else {
        this.logger.debug('加载消息历史失败:', result.error);
        this.data.messageHistory = [];
      }
    } catch (error) {
      this.logger.error('加载消息历史失败:', error);
      this.data.messageHistory = [];
    }
  }

  /**
   * 加载统计信息
   */
  async loadStats() {
    try {
      const result = await this.sendMessageToContent('get-stats');
      if (result.success) {
        this.data.stats = result.data || {};
        this.updateStats();
      } else {
        this.logger.debug('加载统计信息失败:', result.error);
        this.data.stats = {};
      }
    } catch (error) {
      this.logger.error('加载统计信息失败:', error);
      this.data.stats = {};
    }
  }

  /**
   * 更新UI（新增的节流方法）
   */
  updateUI() {
    this.updateConnectionList();
    this.updateMessageHistory();
    this.updateStats();
    this.updateCharts();
  }

  /**
   * 更新连接列表
   */
  updateConnectionList() {
    const connectionList = document.getElementById('connectionList');
    if (!connectionList) return;

    connectionList.innerHTML = '';
    
    this.data.connections.forEach(connection => {
      const item = document.createElement('div');
      item.className = 'connection-item';
      item.dataset.connectionId = connection.id;
      
      const statusColor = this.getStatusColor(connection.status);
      
      // 创建连接头部
      const header = document.createElement('div');
      header.className = 'connection-header';
      
      const statusSpan = document.createElement('span');
      statusSpan.className = 'connection-status';
      statusSpan.style.color = statusColor;
      statusSpan.textContent = '●';
      
      const urlSpan = document.createElement('span');
      urlSpan.className = 'connection-url';
      urlSpan.textContent = connection.url;
      
      const timeSpan = document.createElement('span');
      timeSpan.className = 'connection-time';
      timeSpan.textContent = new Date(connection.createdAt).toLocaleTimeString();
      
      header.appendChild(statusSpan);
      header.appendChild(urlSpan);
      header.appendChild(timeSpan);
      
      // 创建连接统计
      const stats = document.createElement('div');
      stats.className = 'connection-stats';
      
      const messageSpan = document.createElement('span');
      messageSpan.textContent = `消息: ${connection.messageCount}`;
      
      const statusStatSpan = document.createElement('span');
      statusStatSpan.textContent = `状态: ${connection.status}`;
      
      stats.appendChild(messageSpan);
      stats.appendChild(statusStatSpan);
      
      item.appendChild(header);
      item.appendChild(stats);
      
      connectionList.appendChild(item);
    });
  }

  /**
   * 更新消息历史
   */
  updateMessageHistory() {
    const messageHistory = document.getElementById('messageHistory');
    if (!messageHistory) return;

    messageHistory.innerHTML = '';
    
    this.data.messageHistory.forEach(message => {
      const item = document.createElement('div');
      item.className = `message-item ${message.direction}`;
      
      const time = new Date(message.timestamp).toLocaleTimeString();
      const content = message.parsed?.content || message.message;
      
      // 创建消息头部
      const messageHeader = document.createElement('div');
      messageHeader.className = 'message-header';
      
      const directionSpan = document.createElement('span');
      directionSpan.className = 'message-direction';
      directionSpan.textContent = message.direction === 'received' ? '接收' : '发送';
      
      const timeSpan = document.createElement('span');
      timeSpan.className = 'message-time';
      timeSpan.textContent = time;
      
      messageHeader.appendChild(directionSpan);
      messageHeader.appendChild(timeSpan);
      
      // 创建消息内容
      const messageContent = document.createElement('div');
      messageContent.className = 'message-content';
      messageContent.textContent = content; // 直接使用textContent，无需转义
      
      item.appendChild(messageHeader);
      item.appendChild(messageContent);
      
      messageHistory.appendChild(item);
    });
    
    // 滚动到底部
    messageHistory.scrollTop = messageHistory.scrollHeight;
  }

  /**
   * 更新统计信息
   */
  updateStats(stats) {
    const {
      activeConnections = 0,
      totalConnections = 0,
      messageCount = 0,
      lastActivity = null
    } = stats;

    // 更新活跃连接数
    const activeConnectionsElement = document.getElementById('activeConnections');
    if (activeConnectionsElement) {
      activeConnectionsElement.textContent = activeConnections;
    }

    // 更新总连接数
    const totalConnectionsElement = document.getElementById('totalConnections');
    if (totalConnectionsElement) {
      totalConnectionsElement.textContent = totalConnections;
    }

    // 更新消息数量
    const messageCountElement = document.getElementById('messageCount');
    if (messageCountElement) {
      messageCountElement.textContent = messageCount;
    }

    // 更新最后活动时间
    const lastActivityElement = document.getElementById('lastActivity');
    if (lastActivityElement && lastActivity) {
      const timeAgo = Math.floor((Date.now() - lastActivity) / 1000);
      lastActivityElement.textContent = `${timeAgo}秒前`;
    }
  }

  /**
   * 更新统计元素
   */
  updateStatElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
      element.textContent = value;
    }
  }

  /**
   * 更新状态
   */
  updateStatus(status) {
    console.log('[CtripIM Popup] 📊 状态更新:', {
      newStatus: status,
      currentStatus: this.data.status,
      timestamp: Date.now()
    });

    // 更新内部状态
    this.data.status = status;

    // 更新UI元素
    const statusElement = document.getElementById('extensionStatus');
    const statusIndicator = document.getElementById('statusIndicator');
    const statusText = document.querySelector('.status-text');

    if (statusElement) {
      let statusClass = '';
      let statusMessage = '';

      switch (status) {
        case 'connected':
          statusClass = 'connected';
          statusMessage = '已连接';
          break;
        case 'disconnected':
          statusClass = 'disconnected';
          statusMessage = '未连接';
          break;
        case 'loading':
          statusClass = 'loading';
          statusMessage = '检测中...';
          break;
        case 'error':
          statusClass = 'error';
          statusMessage = '连接错误';
          break;
        case 'not-ctrip-site':
          statusClass = 'not-ctrip-site';
          statusMessage = '非携程网站';
          break;
        default:
          statusClass = 'disconnected';
          statusMessage = '未连接';
      }

      // 更新状态显示
      statusElement.textContent = statusMessage;
      statusElement.className = `value ${statusClass}`;
      
      // 更新状态指示器
      if (statusIndicator) {
        statusIndicator.className = `status-indicator ${statusClass}`;
      }
      
      // 更新状态文本
      if (statusText) {
        statusText.textContent = statusMessage;
      }
    }
  }

  /**
   * 处理错误
   */
  handleError(error) {
    console.error('[CtripIM Popup] ❌ 错误:', error);
    this.updateStatus('error');
    this.showNotification(error.message || '发生错误', 'error');
  }

  /**
   * 切换标签页
   */
  switchTab(tabName) {
    // 更新标签按钮状态
    const tabs = document.querySelectorAll('.tab-button');
    tabs.forEach(tab => {
      if (tab.dataset.tab === tabName) {
        tab.classList.add('active');
      } else {
        tab.classList.remove('active');
      }
    });

    // 更新内容显示
    const contents = document.querySelectorAll('.tab-content');
    contents.forEach(content => {
      if (content.id === `${tabName}Tab`) {
        content.classList.add('active');
      } else {
        content.classList.remove('active');
      }
    });
  }

  /**
   * 刷新数据
   */
  async refreshData() {
    try {
      const refreshBtn = document.getElementById('refreshBtn');
      if (refreshBtn) {
        refreshBtn.disabled = true;
        refreshBtn.textContent = '刷新中...';
      }

      await this.loadData();
      
      if (refreshBtn) {
        refreshBtn.disabled = false;
        refreshBtn.textContent = '刷新';
      }
    } catch (error) {
      this.logger.error('刷新数据失败:', error);
    }
  }

  /**
   * 清理历史
   */
  async clearHistory() {
    try {
      const result = await this.sendMessageToContent('clear-history');
      if (result.success) {
        this.data.messageHistory = [];
        this.updateMessageHistory();
        this.showNotification('历史记录已清理');
      }
    } catch (error) {
      this.logger.error('清理历史失败:', error);
    }
  }

  /**
   * 导出数据
   */
  async exportData() {
    try {
      const result = await this.sendMessageToContent('export-data');
      if (result.success) {
        const dataStr = JSON.stringify(result.data, null, 2);
        const blob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `ctrip-im-data-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
        this.showNotification('数据导出成功');
      }
    } catch (error) {
      this.logger.error('导出数据失败:', error);
    }
  }

  /**
   * 发送消息
   */
  async sendMessage() {
    try {
      const messageInput = document.getElementById('messageInput');
      const connectionSelect = document.getElementById('connectionSelect');
      
      if (!messageInput || !connectionSelect) return;
      
      const message = messageInput.value.trim();
      const connectionId = connectionSelect.value;
      
      if (!message || !connectionId) {
        this.showNotification('请输入消息内容并选择连接');
        return;
      }
      
      const result = await this.sendMessageToContent('send-message', {
        connectionId,
        message
      });
      
      if (result.success) {
        messageInput.value = '';
        this.showNotification('消息发送成功');
        await this.loadMessageHistory();
      } else {
        this.showNotification('消息发送失败: ' + result.message);
      }
    } catch (error) {
      this.logger.error('发送消息失败:', error);
    }
  }

  /**
   * 发送消息到content script
   */
  async sendMessageToContent(action, data = {}, retries = 3) {
    // 检查是否是携程网站
    if (!this.isCtripSite()) {
      return {
        success: false,
        error: '当前页面不是携程网站',
        code: 'NOT_CTRIP_SITE'
      };
    }

    // 增加超时和重试机制
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        const response = await this.sendMessageWithTimeout(action, data, 5000);
        return response;
      } catch (error) {
        this.logger.debug(`第${attempt}次尝试失败:`, error.message);
        
        if (attempt === retries) {
          // 最后一次尝试失败，返回错误
          if (error.message.includes('Receiving end does not exist')) {
            return {
              success: false,
              error: 'Content script未加载或未响应，请刷新页面重试',
              code: 'CONTENT_SCRIPT_NOT_LOADED'
            };
          } else {
            return {
              success: false,
              error: error.message,
              code: 'CONNECTION_ERROR'
            };
          }
        }
        
        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }
  }

  /**
   * 带超时的消息发送
   */
  sendMessageWithTimeout(action, data, timeout = 5000) {
    return new Promise((resolve, reject) => {
      // 设置超时
      const timeoutId = setTimeout(() => {
        reject(new Error('消息发送超时'));
      }, timeout);

      chrome.tabs.sendMessage(
        this.currentTab.id,
        {
          source: 'ctrip-im-popup',
          action,
          data,
          timestamp: Date.now()
        },
        (response) => {
          clearTimeout(timeoutId);
          
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(response || { success: false, error: 'No response' });
          }
        }
      );
    });
  }

  /**
   * 处理content script消息
   */
  handleContentMessage(request, sendResponse) {
    const { action, data } = request;
    
    console.log('[CtripIM Popup] 📨 收到content script消息:', {
      action,
      data,
      timestamp: Date.now()
    });
    
    switch (action) {
      case 'connection-status-changed':
        this.updateStatus(data.status === 'connected' ? 'connected' : 'disconnected');
        break;
      case 'status-update':
        this.updateStatus(data.status);
        break;
      case 'message-received':
        this.data.messageHistory.push(data);
        this.updateMessageHistory();
        break;
      case 'stats-updated':
        this.data.stats = data;
        this.updateStats();
        break;
    }
    
    if (sendResponse) {
      sendResponse({ success: true });
    }
  }

  /**
   * 检查是否是携程网站
   */
  isCtripSite() {
    return this.currentTab && /^https?:\/\/.*\.ctrip\.com/.test(this.currentTab.url);
  }

  /**
   * 获取状态颜色
   */
  getStatusColor(status) {
    const colors = {
      connected: '#4CAF50',
      connecting: '#FF9800',
      disconnected: '#F44336',
      error: '#F44336',
      closed: '#757575'
    };
    return colors[status] || '#757575';
  }

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    if (!timestamp) return '从未';
    return new Date(timestamp).toLocaleString();
  }

  /**
   * 转义HTML
   */
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * 显示通知
   */
  showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 自动移除
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }

  /**
   * 初始化图表
   */
  initializeCharts() {
    // 这里可以添加图表初始化代码
    // 例如使用Chart.js或其他图表库
  }

  /**
   * 更新图表
   */
  updateCharts() {
    // 这里可以添加图表更新代码
  }

  /**
   * 启动定时刷新
   */
  startRefreshTimer() {
    // 每30秒刷新一次数据，使用防抖优化
    this.refreshInterval = setInterval(() => {
      this.debouncedRefresh();
    }, 30000);
  }

  /**
   * 停止定时刷新
   */
  stopRefreshTimer() {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
    }
  }

  /**
   * 打开设置页面
   */
  openSettings() {
    chrome.runtime.openOptionsPage();
  }

  /**
   * 销毁弹窗管理器
   */
  destroy() {
    this.logger.info('销毁弹窗管理器');
    
    this.stopRefreshTimer();
    this.removeAllListeners();
    
    this.initialized = false;
  }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', async () => {
  const popupManager = new PopupManager();
  await popupManager.initialize();
  
  // 导出全局对象供调试使用
  window.popupManager = popupManager;
});

export default PopupManager; 