/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  color: #333;
  background: #f8fafc;
  width: 380px;
  min-height: 500px;
}

.container {
  background: #fff;
  min-height: 500px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 标题栏 */
.header {
  padding: 16px;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header h1 {
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 状态指示器样式 */
.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 600;
  transition: all 0.3s ease;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.15);
}

.status-indicator::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
  background-color: #ffffff;
  animation: pulse 2s infinite;
}

/* 检测中状态 */
.status-indicator.loading {
  background: rgba(99, 102, 241, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
  color: #ffffff;
  font-weight: 600;
}

.status-indicator.loading::before {
  background-color: #ffffff;
  animation: fastPulse 1s infinite;
}

/* 成功状态 */
.status-indicator.connected {
  background: rgba(34, 197, 94, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.2);
}

.status-indicator.connected::before {
  background-color: #ffffff;
}

/* 失败状态（包括断开连接和错误） */
.status-indicator.disconnected,
.status-indicator.error {
  background: rgba(239, 68, 68, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.2);
}

.status-indicator.disconnected::before,
.status-indicator.error::before {
  background-color: #ffffff;
}

/* 非携程网站状态 */
.status-indicator.not-ctrip-site {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #ffffff;
  font-weight: 500;
}

.status-indicator.not-ctrip-site::before {
  background-color: #ffffff;
}

/* 状态指示器动画 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(255, 255, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

/* 快速脉冲动画 */
@keyframes fastPulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 状态值样式 */
.value {
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 16px;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
}

.value::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.value.connected {
  color: #16a34a;
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.value.connected::before {
  background-color: #22c55e;
}

.value.disconnected {
  color: #dc2626;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.value.disconnected::before {
  background-color: #ef4444;
}

.value.loading {
  color: #2563eb;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.value.loading::before {
  background-color: #3b82f6;
  animation: pulse 1s infinite;
}

.value.error {
  color: #dc2626;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.value.error::before {
  background-color: #ef4444;
  animation: pulse 0.5s infinite;
}

.value.not-ctrip-site {
  color: #475569;
  background: rgba(100, 116, 139, 0.1);
  border: 1px solid rgba(100, 116, 139, 0.2);
}

.value.not-ctrip-site::before {
  background-color: #64748b;
}

/* 添加动画效果 */
@keyframes pulse {
  0% { 
    opacity: 1;
    transform: scale(1);
  }
  50% { 
    opacity: 0.6;
    transform: scale(1.1);
  }
  100% { 
    opacity: 1;
    transform: scale(1);
  }
}

/* 导航标签样式 */
.tabs {
  display: flex;
  background: #fff;
  border-bottom: 1px solid #e2e8f0;
  padding: 0 16px;
}

.tab-button {
  padding: 12px 16px;
  border: none;
  background: none;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

.tab-button:hover {
  color: #4f46e5;
}

.tab-button.active {
  color: #4f46e5;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: #4f46e5;
  border-radius: 2px;
}

/* 内容卡片样式 */
.info-card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.info-card h3 {
  color: #1e293b;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.info-item .label {
  color: #64748b;
  font-size: 13px;
  font-weight: 500;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  background: #667eea;
  color: white;
}

.btn:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
}

.btn-small {
  padding: 6px 12px;
  font-size: 12px;
}

/* 控制按钮组 */
.debug-controls,
.message-controls {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.log-filter {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
}

/* 日志列表 */
.logs-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: #f8f9fa;
}

.log-item {
  padding: 8px 12px;
  border-bottom: 1px solid #e9ecef;
  font-family: 'Courier New', monospace;
  font-size: 11px;
  line-height: 1.3;
}

.log-item:last-child {
  border-bottom: none;
}

.log-item.debug {
  background: #f8f9fa;
  color: #666;
}

.log-item.info {
  background: #e3f2fd;
  color: #1976d2;
}

.log-item.warn {
  background: #fff3e0;
  color: #f57c00;
}

.log-item.error {
  background: #ffebee;
  color: #d32f2f;
}

.log-timestamp {
  color: #888;
  font-size: 10px;
}

.log-module {
  font-weight: bold;
  color: #333;
}

.log-message {
  margin-top: 2px;
  word-wrap: break-word;
}

/* 连接列表 */
.connections-list,
.messages-list {
  max-height: 200px;
  overflow-y: auto;
}

.connection-item,
.message-item {
  padding: 8px 12px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
  margin-bottom: 4px;
  border-radius: 4px;
}

.connection-url {
  font-weight: 500;
  color: #667eea;
  word-break: break-all;
  font-size: 12px;
}

.connection-status {
  font-size: 11px;
  color: #666;
  margin-top: 4px;
}

.message-direction {
  font-weight: bold;
  margin-right: 8px;
}

.message-direction.sent {
  color: #4caf50;
}

.message-direction.received {
  color: #2196f3;
}

.message-content {
  font-family: 'Courier New', monospace;
  font-size: 11px;
  color: #333;
  margin-top: 4px;
  word-break: break-all;
}

/* 快速操作 */
.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

/* 设置项 */
.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
}

.setting-item input[type="checkbox"] {
  margin: 0;
}

.setting-item select {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 32px 16px;
  color: #666;
  font-style: italic;
}

/* 底部栏 */
.footer {
  padding: 12px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.version {
  font-size: 11px;
  color: #888;
}

/* 滚动条样式 */
.logs-list::-webkit-scrollbar,
.connections-list::-webkit-scrollbar,
.messages-list::-webkit-scrollbar {
  width: 6px;
}

.logs-list::-webkit-scrollbar-track,
.connections-list::-webkit-scrollbar-track,
.messages-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.logs-list::-webkit-scrollbar-thumb,
.connections-list::-webkit-scrollbar-thumb,
.messages-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.logs-list::-webkit-scrollbar-thumb:hover,
.connections-list::-webkit-scrollbar-thumb:hover,
.messages-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式调整 */
@media (max-width: 400px) {
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions {
    grid-template-columns: 1fr;
  }
} 