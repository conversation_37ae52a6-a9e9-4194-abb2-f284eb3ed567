<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>携程IM拦截器</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="container">
    <!-- 标题栏 -->
    <header class="header">
      <h1>携程IM拦截器</h1>
      <div class="status-indicator" id="statusIndicator">
        <span class="status-text">未连接</span>
      </div>
    </header>

    <!-- 导航标签 -->
    <nav class="tabs">
      <button class="tab-button active" data-tab="status">状态</button>
      <button class="tab-button" data-tab="connections">连接</button>
      <button class="tab-button" data-tab="messages">消息</button>
      <button class="tab-button" data-tab="debug">调试</button>
      <button class="tab-button" data-tab="settings">设置</button>
    </nav>

    <!-- 状态页面 -->
    <div class="tab-content active" id="statusTab">
      <div class="info-card">
        <h3>扩展状态</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">状态:</span>
            <span class="value" id="extensionStatus">检测中...</span>
          </div>
          <div class="info-item">
            <span class="label">活跃连接:</span>
            <span class="value" id="activeConnections">0</span>
          </div>
          <div class="info-item">
            <span class="label">总连接数:</span>
            <span class="value" id="totalConnections">0</span>
          </div>
          <div class="info-item">
            <span class="label">消息数量:</span>
            <span class="value" id="messageCount">0</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 连接页面 -->
    <div class="tab-content" id="connectionsTab">
      <div class="info-card">
        <h3>WebSocket连接</h3>
        <div class="connections-list" id="connectionsList">
          <div class="empty-state">暂无连接</div>
        </div>
      </div>
    </div>

    <!-- 消息页面 -->
    <div class="tab-content" id="messagesTab">
      <div class="info-card">
        <h3>消息历史</h3>
        <div class="message-controls">
          <button class="btn-small" id="clearMessages">清空</button>
          <button class="btn-small" id="exportMessages">导出</button>
        </div>
        <div class="messages-list" id="messagesList">
          <div class="empty-state">暂无消息</div>
        </div>
      </div>
    </div>

    <!-- 调试页面 -->
    <div class="tab-content" id="debugTab">
      <div class="info-card">
        <h3>调试日志</h3>
        <div class="debug-controls">
          <button class="btn-small" id="refreshLogs">刷新</button>
          <button class="btn-small" id="clearLogs">清空</button>
          <button class="btn-small" id="exportLogs">导出</button>
          <select id="logLevelFilter" class="log-filter">
            <option value="">所有级别</option>
            <option value="debug">DEBUG</option>
            <option value="info">INFO</option>
            <option value="warn">WARN</option>
            <option value="error">ERROR</option>
          </select>
        </div>
        <div class="logs-list" id="logsList">
          <div class="empty-state">暂无日志</div>
        </div>
      </div>
      
      <div class="info-card">
        <h3>快速操作</h3>
        <div class="quick-actions">
          <button class="btn" id="testInterceptor">测试拦截器</button>
          <button class="btn" id="forceReload">强制重载</button>
          <button class="btn" id="exportAll">导出所有数据</button>
        </div>
      </div>
    </div>

    <!-- 设置页面 -->
    <div class="tab-content" id="settingsTab">
      <div class="info-card">
        <h3>拦截设置</h3>
        <div class="setting-item">
          <label>
            <input type="checkbox" id="enableInterceptor" checked>
            启用WebSocket拦截
          </label>
        </div>
        <div class="setting-item">
          <label>
            <input type="checkbox" id="enableAutoReply">
            启用自动回复
          </label>
        </div>
      </div>
      
      <div class="info-card">
        <h3>日志设置</h3>
        <div class="setting-item">
          <label for="logLevel">日志级别:</label>
          <select id="logLevel">
            <option value="debug">DEBUG</option>
            <option value="info">INFO</option>
            <option value="warn">WARN</option>
            <option value="error">ERROR</option>
          </select>
        </div>
        <div class="setting-item">
          <label>
            <input type="checkbox" id="enableStorageLog" checked>
            启用存储日志
          </label>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <footer class="footer">
      <button class="btn-small" id="refreshData">刷新数据</button>
      <span class="version">v1.0.3</span>
    </footer>
  </div>

  <script src="popup-manager.js"></script>
</body>
</html> 