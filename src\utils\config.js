/**
 * @fileoverview 配置管理器
 * <AUTHOR> Extension Developer
 */

import { DEFAULT_CONFIG, ERROR_CODES, STORAGE_KEYS } from './constants.js';
import { storageLogger } from './logger.js';

/**
 * 配置管理器类
 */
class ConfigManager {
  /**
   * 构造函数
   */
  constructor() {
    this.logger = storageLogger;
    this.cache = new Map();
    this.initialized = false;
  }

  /**
   * 初始化配置管理器
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      if (this.initialized) {
        return;
      }

      this.logger.info('正在初始化配置管理器...');
      
      // 检查是否已有配置
      const existingData = await this.getStorageData(Object.keys(DEFAULT_CONFIG));
      
      // 合并默认配置和现有配置
      const mergedData = this.mergeConfigs(DEFAULT_CONFIG, existingData);
      
      // 保存合并后的配置
      await this.setStorageData(mergedData);
      
      // 更新缓存
      this.updateCache(mergedData);
      
      this.initialized = true;
      this.logger.info('配置管理器初始化完成');
      
    } catch (error) {
      this.logger.exception(error, '配置管理器初始化失败');
      throw new Error(`${ERROR_CODES.INITIALIZATION_FAILED}: ${error.message}`);
    }
  }

  /**
   * 获取存储数据
   * @param {Array<string>} keys - 要获取的键名数组
   * @returns {Promise<Object>} 存储的数据
   */
  async getStorageData(keys) {
    try {
      const data = await chrome.storage.local.get(keys);
      this.logger.debug('获取存储数据:', Object.keys(data));
      return data;
    } catch (error) {
      this.logger.exception(error, '获取存储数据失败');
      throw new Error(`${ERROR_CODES.STORAGE_ERROR}: ${error.message}`);
    }
  }

  /**
   * 设置存储数据
   * @param {Object} data - 要设置的数据
   * @returns {Promise<void>}
   */
  async setStorageData(data) {
    try {
      await chrome.storage.local.set(data);
      this.logger.debug('设置存储数据:', Object.keys(data));
    } catch (error) {
      this.logger.exception(error, '设置存储数据失败');
      throw new Error(`${ERROR_CODES.STORAGE_ERROR}: ${error.message}`);
    }
  }

  /**
   * 合并配置
   * @param {Object} defaultConfig - 默认配置
   * @param {Object} existingConfig - 现有配置
   * @returns {Object} 合并后的配置
   */
  mergeConfigs(defaultConfig, existingConfig) {
    const mergedData = {};
    
    for (const [key, defaultValue] of Object.entries(defaultConfig)) {
      if (existingConfig[key]) {
        // 深度合并对象
        mergedData[key] = this.deepMerge(defaultValue, existingConfig[key]);
      } else {
        mergedData[key] = defaultValue;
      }
    }
    
    return mergedData;
  }

  /**
   * 深度合并对象
   * @param {Object} target - 目标对象
   * @param {Object} source - 源对象
   * @returns {Object} 合并后的对象
   */
  deepMerge(target, source) {
    const result = { ...target };
    
    for (const [key, value] of Object.entries(source)) {
      if (value !== null && typeof value === 'object' && !Array.isArray(value)) {
        result[key] = this.deepMerge(result[key] || {}, value);
      } else {
        result[key] = value;
      }
    }
    
    return result;
  }

  /**
   * 更新缓存
   * @param {Object} data - 要缓存的数据
   */
  updateCache(data) {
    for (const [key, value] of Object.entries(data)) {
      this.cache.set(key, value);
    }
  }

  /**
   * 获取配置
   * @param {string} key - 配置键名
   * @returns {Promise<any>} 配置值
   */
  async getConfig(key) {
    try {
      await this.ensureInitialized();
      
      // 先检查缓存
      if (this.cache.has(key)) {
        return this.cache.get(key);
      }
      
      // 从存储中获取
      const data = await this.getStorageData([key]);
      const value = data[key];
      
      // 更新缓存
      if (value !== undefined) {
        this.cache.set(key, value);
      }
      
      return value;
    } catch (error) {
      this.logger.exception(error, `获取配置失败: ${key}`);
      throw error;
    }
  }

  /**
   * 设置配置
   * @param {string} key - 配置键名
   * @param {any} value - 配置值
   * @returns {Promise<void>}
   */
  async setConfig(key, value) {
    try {
      await this.ensureInitialized();
      
      // 验证配置
      this.validateConfig(key, value);
      
      // 设置存储
      await this.setStorageData({ [key]: value });
      
      // 更新缓存
      this.cache.set(key, value);
      
      this.logger.info(`配置已更新: ${key}`);
      
    } catch (error) {
      this.logger.exception(error, `设置配置失败: ${key}`);
      throw error;
    }
  }

  /**
   * 更新配置
   * @param {Object} updates - 要更新的配置
   * @returns {Promise<void>}
   */
  async updateConfig(updates) {
    try {
      await this.ensureInitialized();
      
      // 验证所有更新
      for (const [key, value] of Object.entries(updates)) {
        this.validateConfig(key, value);
      }
      
      // 批量设置
      await this.setStorageData(updates);
      
      // 更新缓存
      this.updateCache(updates);
      
      this.logger.info('配置已批量更新:', Object.keys(updates));
      
    } catch (error) {
      this.logger.exception(error, '批量更新配置失败');
      throw error;
    }
  }

  /**
   * 获取所有配置
   * @returns {Promise<Object>} 所有配置
   */
  async getAllConfigs() {
    try {
      await this.ensureInitialized();
      
      const configKeys = Object.keys(DEFAULT_CONFIG);
      const data = await this.getStorageData(configKeys);
      
      // 更新缓存
      this.updateCache(data);
      
      return data;
    } catch (error) {
      this.logger.exception(error, '获取所有配置失败');
      throw error;
    }
  }

  /**
   * 重置配置为默认值
   * @param {string} key - 配置键名，如果不提供则重置所有
   * @returns {Promise<void>}
   */
  async resetConfig(key = null) {
    try {
      await this.ensureInitialized();
      
      if (key) {
        // 重置单个配置
        const defaultValue = DEFAULT_CONFIG[key];
        if (defaultValue !== undefined) {
          await this.setConfig(key, defaultValue);
          this.logger.info(`配置已重置: ${key}`);
        } else {
          throw new Error(`未找到配置: ${key}`);
        }
      } else {
        // 重置所有配置
        await this.setStorageData(DEFAULT_CONFIG);
        this.updateCache(DEFAULT_CONFIG);
        this.logger.info('所有配置已重置为默认值');
      }
    } catch (error) {
      this.logger.exception(error, '重置配置失败');
      throw error;
    }
  }

  /**
   * 验证配置
   * @param {string} key - 配置键名
   * @param {any} value - 配置值
   * @throws {Error} 如果配置无效
   */
  validateConfig(key, value) {
    // 检查配置键是否存在
    if (!DEFAULT_CONFIG.hasOwnProperty(key)) {
      throw new Error(`无效的配置键: ${key}`);
    }

    // 基本类型检查
    if (value === null || value === undefined) {
      throw new Error(`配置值不能为空: ${key}`);
    }

    // 特定配置的验证
    switch (key) {
      case STORAGE_KEYS.AUTO_REPLY_CONFIG:
        this.validateAutoReplyConfig(value);
        break;
      case STORAGE_KEYS.INTERCEPTOR_CONFIG:
        this.validateInterceptorConfig(value);
        break;
      case STORAGE_KEYS.USER_PREFERENCES:
        this.validateUserPreferences(value);
        break;
      case STORAGE_KEYS.STATISTICS:
        this.validateStatistics(value);
        break;
    }
  }

  /**
   * 验证自动回复配置
   * @param {Object} config - 自动回复配置
   */
  validateAutoReplyConfig(config) {
    if (typeof config !== 'object') {
      throw new Error('自动回复配置必须是对象');
    }

    // 验证必需字段
    const requiredFields = ['enabled', 'delay', 'mode'];
    for (const field of requiredFields) {
      if (!(field in config)) {
        throw new Error(`缺少必需的字段: ${field}`);
      }
    }

    // 验证延迟设置
    if (config.delay && typeof config.delay === 'object') {
      if (config.delay.min < 0 || config.delay.max < 0) {
        throw new Error('延迟时间不能为负数');
      }
      if (config.delay.min > config.delay.max) {
        throw new Error('最小延迟不能大于最大延迟');
      }
    }

    // 验证回复率
    if (config.replyRate !== undefined) {
      if (config.replyRate < 0 || config.replyRate > 1) {
        throw new Error('回复率必须在0到1之间');
      }
    }
  }

  /**
   * 验证拦截器配置
   * @param {Object} config - 拦截器配置
   */
  validateInterceptorConfig(config) {
    if (typeof config !== 'object') {
      throw new Error('拦截器配置必须是对象');
    }

    // 验证日志级别
    if (config.logLevel) {
      const validLevels = ['debug', 'info', 'warn', 'error'];
      if (!validLevels.includes(config.logLevel)) {
        throw new Error(`无效的日志级别: ${config.logLevel}`);
      }
    }

    // 验证历史记录大小
    if (config.maxHistorySize !== undefined) {
      if (config.maxHistorySize < 0) {
        throw new Error('历史记录大小不能为负数');
      }
    }
  }

  /**
   * 验证用户偏好设置
   * @param {Object} config - 用户偏好设置
   */
  validateUserPreferences(config) {
    if (typeof config !== 'object') {
      throw new Error('用户偏好设置必须是对象');
    }

    // 验证主题
    if (config.theme) {
      const validThemes = ['light', 'dark', 'system'];
      if (!validThemes.includes(config.theme)) {
        throw new Error(`无效的主题: ${config.theme}`);
      }
    }

    // 验证语言
    if (config.language) {
      const validLanguages = ['zh-CN', 'en-US'];
      if (!validLanguages.includes(config.language)) {
        throw new Error(`无效的语言: ${config.language}`);
      }
    }
  }

  /**
   * 验证统计信息
   * @param {Object} config - 统计信息
   */
  validateStatistics(config) {
    if (typeof config !== 'object') {
      throw new Error('统计信息必须是对象');
    }

    // 验证数值字段
    const numericFields = ['totalMessages', 'totalReplies', 'successRate'];
    for (const field of numericFields) {
      if (config[field] !== undefined && (typeof config[field] !== 'number' || config[field] < 0)) {
        throw new Error(`${field} 必须是非负数`);
      }
    }
  }

  /**
   * 确保已初始化
   * @returns {Promise<void>}
   */
  async ensureInitialized() {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  /**
   * 监听配置变化
   * @param {Function} callback - 回调函数
   */
  onConfigChanged(callback) {
    chrome.storage.onChanged.addListener((changes, namespace) => {
      if (namespace === 'local') {
        for (const [key, change] of Object.entries(changes)) {
          if (DEFAULT_CONFIG.hasOwnProperty(key)) {
            // 更新缓存
            this.cache.set(key, change.newValue);
            
            // 调用回调
            callback(key, change.newValue, change.oldValue);
          }
        }
      }
    });
  }

  /**
   * 导出配置
   * @returns {Promise<Object>} 配置数据
   */
  async exportConfig() {
    try {
      const allConfigs = await this.getAllConfigs();
      return {
        timestamp: Date.now(),
        version: '1.0.0',
        configs: allConfigs
      };
    } catch (error) {
      this.logger.exception(error, '导出配置失败');
      throw error;
    }
  }

  /**
   * 导入配置
   * @param {Object} configData - 配置数据
   * @returns {Promise<void>}
   */
  async importConfig(configData) {
    try {
      if (!configData || !configData.configs) {
        throw new Error('无效的配置数据');
      }

      // 验证所有配置
      for (const [key, value] of Object.entries(configData.configs)) {
        this.validateConfig(key, value);
      }

      // 导入配置
      await this.updateConfig(configData.configs);
      
      this.logger.info('配置导入完成');
    } catch (error) {
      this.logger.exception(error, '导入配置失败');
      throw error;
    }
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.cache.clear();
    this.logger.debug('配置缓存已清理');
  }
}

/**
 * 配置管理器实例
 */
export const configManager = new ConfigManager();

/**
 * 导出配置管理器类
 */
export default ConfigManager; 