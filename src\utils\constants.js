/**
 * @fileoverview 应用常量定义
 * <AUTHOR> Extension Developer
 */

/**
 * 应用基础配置
 */
export const APP_CONFIG = {
  NAME: 'CtripIM-Interceptor',
  VERSION: '1.0.0',
  DESCRIPTION: '携程IM拦截器',
  AUTHOR: 'Extension Developer'
};

/**
 * 存储键名常量
 */
export const STORAGE_KEYS = {
  AUTO_REPLY_CONFIG: 'autoReplyConfig',
  INTERCEPTOR_CONFIG: 'interceptorConfig',
  USER_PREFERENCES: 'userPreferences',
  STATISTICS: 'statistics',
  MESSAGE_HISTORY: 'messageHistory',
  AUTO_REPLY_STATS: 'autoReplyStats',
  CTRIP_ACTIVITY: 'ctripActivity'
};

/**
 * 消息类型常量
 */
export const MESSAGE_TYPES = {
  // 拦截相关
  INTERCEPTED_MESSAGE: 'intercepted_message',
  WEBSOCKET_DETECTED: 'websocket-detected',
  EARLY_WEBSOCKET_DETECTED: 'early-websocket-detected',
  INTERCEPTOR_INSTALLED: 'interceptor-installed',
  CONNECTION_STATUS_UPDATE: 'connection-status-update',
  
  // 配置相关
  GET_CONFIG: 'get_config',
  UPDATE_CONFIG: 'update_config',
  CONFIG_CHANGED: 'configChanged',
  
  // 状态相关
  GET_STATUS: 'get_status',
  RESET_STATS: 'reset_stats',
  UPDATE_STATS: 'updateStats',
  GET_BACKGROUND_STATS: 'getBackgroundStats',
  
  // 标签页管理
  REGISTER_TAB: 'registerTab',
  UNREGISTER_TAB: 'unregisterTab',
  
  // 通知相关
  SHOW_NOTIFICATION: 'showNotification',
  
  // 导出相关
  EXPORT_LOGS: 'export_logs',
  AUTO_EXPORT: 'autoExport',
  EXPORT_HISTORY: 'exportHistory',
  
  // 自动回复相关
  UPDATE_AUTO_REPLY_CONFIG: 'updateAutoReplyConfig',
  TOGGLE_AUTO_REPLY: 'toggleAutoReply'
};

/**
 * 定时任务名称
 */
export const ALARM_NAMES = {
  CLEANUP_EXPIRED_DATA: 'cleanupExpiredData',
  SYNC_STATISTICS: 'syncStatistics',
  AUTO_EXPORT: 'autoExport'
};

/**
 * 上下文菜单ID
 */
export const CONTEXT_MENU_IDS = {
  TOGGLE_AUTO_REPLY: 'ctrip-im-toggle-auto-reply',
  EXPORT_HISTORY: 'ctrip-im-export-history',
  OPEN_SETTINGS: 'ctrip-im-open-settings'
};

/**
 * URL模式
 */
export const URL_PATTERNS = {
  CTRIP_SITES: ['*://*.ctrip.com/*'],
  CTRIP_DOMAIN: 'ctrip.com'
};

/**
 * 时间常量 (毫秒)
 */
export const TIME_CONSTANTS = {
  SECOND: 1000,
  MINUTE: 60 * 1000,
  HOUR: 60 * 60 * 1000,
  DAY: 24 * 60 * 60 * 1000,
  WEEK: 7 * 24 * 60 * 60 * 1000,
  
  // 定时任务间隔
  CLEANUP_INTERVAL: 60, // 分钟
  SYNC_INTERVAL: 30, // 分钟
  EXPORT_INTERVAL: 24 * 60, // 分钟
  
  // 图标更新间隔
  ICON_UPDATE_INTERVAL: 5 * 1000, // 5秒
  
  // 自动回复默认延迟
  AUTO_REPLY_MIN_DELAY: 2000,
  AUTO_REPLY_MAX_DELAY: 8000,
  
  // 上下文窗口
  CONTEXT_WINDOW: 300000 // 5分钟
};

/**
 * 数据限制常量
 */
export const DATA_LIMITS = {
  MAX_MESSAGE_HISTORY: 1000,
  MAX_ACTIVITY_RECORDS: 100,
  MAX_DAILY_STATS: 30,
  MAX_REPLIES_PER_USER: 10,
  MAX_REPLIES_PER_HOUR: 50,
  MAX_HISTORY_SIZE: 1000
};

/**
 * 默认配置
 */
export const DEFAULT_CONFIG = {
  autoReplyConfig: {
    enabled: false,
    delay: { 
      min: TIME_CONSTANTS.AUTO_REPLY_MIN_DELAY, 
      max: TIME_CONSTANTS.AUTO_REPLY_MAX_DELAY 
    },
    mode: 'smart',
    maxRepliesPerUser: DATA_LIMITS.MAX_REPLIES_PER_USER,
    maxRepliesPerHour: DATA_LIMITS.MAX_REPLIES_PER_HOUR,
    workingHours: { enabled: false, start: 9, end: 18 },
    replyRate: 0.8,
    avoidDetection: true,
    personalization: true,
    duplicateCheck: true,
    contextWindow: TIME_CONSTANTS.CONTEXT_WINDOW,
    keywords: { exclude: ['测试', 'test'], required: [] },
    blacklist: [],
    whitelist: []
  },
  interceptorConfig: {
    enabled: true,
    logLevel: 'info',
    maxHistorySize: DATA_LIMITS.MAX_HISTORY_SIZE,
    autoExport: false,
    exportInterval: TIME_CONSTANTS.DAY
  },
  userPreferences: {
    theme: 'light',
    language: 'zh-CN',
    notifications: true,
    sounds: false,
    autoUpdate: true
  },
  statistics: {
    totalMessages: 0,
    totalReplies: 0,
    successRate: 0,
    lastReset: Date.now()
  }
};

/**
 * 日志级别
 */
export const LOG_LEVELS = {
  DEBUG: 'debug',
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error'
};

/**
 * 通知类型
 */
export const NOTIFICATION_TYPES = {
  BASIC: 'basic',
  IMAGE: 'image',
  LIST: 'list',
  PROGRESS: 'progress'
};

/**
 * 活动类型
 */
export const ACTIVITY_TYPES = {
  WEBSOCKET_DETECTED: 'websocket_detected',
  INTERCEPTOR_INSTALLED: 'interceptor_installed',
  CONNECTION_STATUS: 'connection_status',
  AUTO_REPLY_SENT: 'auto_reply_sent',
  MESSAGE_INTERCEPTED: 'message_intercepted'
};

/**
 * 扩展状态
 */
export const EXTENSION_STATUS = {
  INITIALIZING: 'initializing',
  RUNNING: 'running',
  STOPPED: 'stopped',
  ERROR: 'error'
};

/**
 * 图标状态
 */
export const ICON_STATUS = {
  ENABLED: 'ON',
  DISABLED: '',
  ENABLED_COLOR: '#4CAF50',
  DISABLED_COLOR: '#F44336'
};

/**
 * 导出格式
 */
export const EXPORT_FORMATS = {
  JSON: 'json',
  CSV: 'csv',
  TXT: 'txt'
};

/**
 * API端点（如果需要）
 */
export const API_ENDPOINTS = {
  BASE_URL: 'https://api.ctrip.com',
  // 其他API端点...
};

/**
 * 错误代码
 */
export const ERROR_CODES = {
  INITIALIZATION_FAILED: 'INIT_FAILED',
  STORAGE_ERROR: 'STORAGE_ERROR',
  MESSAGE_HANDLER_ERROR: 'MESSAGE_ERROR',
  ALARM_ERROR: 'ALARM_ERROR',
  NOTIFICATION_ERROR: 'NOTIFICATION_ERROR',
  TAB_COMMUNICATION_ERROR: 'TAB_COMM_ERROR'
};

/**
 * 成功消息
 */
export const SUCCESS_MESSAGES = {
  EXTENSION_INITIALIZED: '扩展初始化完成',
  CONFIG_UPDATED: '配置已更新',
  AUTO_REPLY_ENABLED: '自动回复已启用',
  AUTO_REPLY_DISABLED: '自动回复已禁用',
  HISTORY_EXPORTED: '历史记录已导出',
  STATS_RESET: '统计信息已重置'
};

/**
 * 错误消息
 */
export const ERROR_MESSAGES = {
  INITIALIZATION_FAILED: '扩展初始化失败',
  CONFIG_UPDATE_FAILED: '配置更新失败',
  STORAGE_ACCESS_FAILED: '存储访问失败',
  MESSAGE_HANDLING_FAILED: '消息处理失败',
  EXPORT_FAILED: '导出失败',
  UNKNOWN_MESSAGE_TYPE: '未知消息类型'
}; 