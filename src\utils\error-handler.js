/**
 * 错误处理模块
 * 提供统一的错误处理、报告和恢复机制
 */
import { EventEmitter } from './event-emitter.js';
import { createLogger } from './logger.js';

/**
 * 错误类型定义
 */
export const ErrorTypes = {
  NETWORK: 'NETWORK',
  WEBSOCKET: 'WEBSOCKET',
  PERMISSION: 'PERMISSION',
  STORAGE: 'STORAGE',
  CONFIGURATION: 'CONFIGURATION',
  PARSING: 'PARSING',
  VALIDATION: 'VALIDATION',
  TIMEOUT: 'TIMEOUT',
  MEMORY: 'MEMORY',
  UNKNOWN: 'UNKNOWN'
};

/**
 * 错误严重程度
 */
export const ErrorSeverity = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  CRITICAL: 'CRITICAL'
};

/**
 * 自定义错误类
 */
export class CtripIMError extends Error {
  constructor(message, type = ErrorTypes.UNKNOWN, severity = ErrorSeverity.MEDIUM, context = {}) {
    super(message);
    this.name = 'CtripIMError';
    this.type = type;
    this.severity = severity;
    this.context = context;
    this.timestamp = Date.now();
    this.id = this.generateErrorId();
  }

  generateErrorId() {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  toJSON() {
    return {
      id: this.id,
      name: this.name,
      message: this.message,
      type: this.type,
      severity: this.severity,
      context: this.context,
      timestamp: this.timestamp,
      stack: this.stack
    };
  }
}

/**
 * 错误处理器类
 */
export class ErrorHandler extends EventEmitter {
  constructor() {
    super();
    this.logger = createLogger('ErrorHandler');
    this.errorHistory = [];
    this.maxHistorySize = 100;
    this.retryStrategies = new Map();
    this.recoveryHandlers = new Map();
    this.errorCounts = new Map();
    this.circuitBreakers = new Map();
    
    this.setupGlobalErrorHandlers();
  }

  /**
   * 设置全局错误处理器
   */
  setupGlobalErrorHandlers() {
    // 处理未捕获的错误
    if (typeof window !== 'undefined') {
      window.addEventListener('error', (event) => {
        this.handleError(new CtripIMError(
          event.message,
          ErrorTypes.UNKNOWN,
          ErrorSeverity.HIGH,
          {
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            stack: event.error?.stack
          }
        ));
      });

      // 处理未处理的Promise拒绝
      window.addEventListener('unhandledrejection', (event) => {
        this.handleError(new CtripIMError(
          `Unhandled Promise Rejection: ${event.reason}`,
          ErrorTypes.UNKNOWN,
          ErrorSeverity.HIGH,
          { reason: event.reason }
        ));
      });
    }

    // Chrome扩展特定的错误处理
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        if (request.type === 'error-report') {
          this.handleError(new CtripIMError(
            request.message,
            request.errorType || ErrorTypes.UNKNOWN,
            request.severity || ErrorSeverity.MEDIUM,
            request.context || {}
          ));
        }
      });
    }
  }

  /**
   * 处理错误
   */
  async handleError(error, options = {}) {
    try {
      // 确保是CtripIMError实例
      const ctripError = error instanceof CtripIMError ? 
        error : 
        new CtripIMError(error.message || String(error), ErrorTypes.UNKNOWN, ErrorSeverity.MEDIUM, { originalError: error });

      // 记录错误
      this.recordError(ctripError);

      // 检查重复错误
      if (this.isDuplicateError(ctripError)) {
        this.logger.debug('跳过重复错误:', ctripError.message);
        return;
      }

      // 更新错误计数
      this.updateErrorCount(ctripError);

      // 检查熔断器
      if (this.shouldCircuitBreak(ctripError)) {
        this.logger.warn('错误过多，触发熔断器:', ctripError.type);
        this.emit('circuit-breaker-triggered', { errorType: ctripError.type, error: ctripError });
        return;
      }

      // 记录日志
      this.logError(ctripError);

      // 尝试恢复
      const recovered = await this.attemptRecovery(ctripError);
      
      if (!recovered && options.retry !== false) {
        // 尝试重试
        await this.attemptRetry(ctripError, options);
      }

      // 发送错误事件
      this.emit('error-handled', ctripError);

      // 根据严重程度决定是否向用户报告
      if (ctripError.severity === ErrorSeverity.HIGH || ctripError.severity === ErrorSeverity.CRITICAL) {
        this.notifyUser(ctripError);
      }

    } catch (handlingError) {
      this.logger.exception(handlingError, '错误处理器本身发生错误');
      throw handlingError;
    }
  }

  /**
   * 记录错误到历史
   */
  recordError(error) {
    this.errorHistory.push(error);
    
    // 保持历史记录大小
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory.shift();
    }
  }

  /**
   * 检查是否是重复错误
   */
  isDuplicateError(error) {
    const recentErrors = this.errorHistory.slice(-5); // 检查最近5个错误
    return recentErrors.some(recent => 
      recent.message === error.message && 
      recent.type === error.type &&
      (Date.now() - recent.timestamp) < 5000 // 5秒内
    );
  }

  /**
   * 更新错误计数
   */
  updateErrorCount(error) {
    const key = `${error.type}_${error.message}`;
    const current = this.errorCounts.get(key) || 0;
    this.errorCounts.set(key, current + 1);
  }

  /**
   * 检查是否应该触发熔断器
   */
  shouldCircuitBreak(error) {
    const key = error.type;
    const count = Array.from(this.errorCounts.entries())
      .filter(([errorKey]) => errorKey.startsWith(key))
      .reduce((sum, [, count]) => sum + count, 0);
    
    // 同类型错误超过10次触发熔断器
    return count > 10;
  }

  /**
   * 记录错误日志
   */
  logError(error) {
    const logMethod = this.getLogMethod(error.severity);
    logMethod.call(this.logger, `[${error.type}] ${error.message}`, {
      id: error.id,
      severity: error.severity,
      context: error.context,
      timestamp: new Date(error.timestamp).toISOString()
    });
  }

  /**
   * 获取日志方法
   */
  getLogMethod(severity) {
    switch (severity) {
      case ErrorSeverity.LOW:
        return this.logger.debug;
      case ErrorSeverity.MEDIUM:
        return this.logger.warn;
      case ErrorSeverity.HIGH:
        return this.logger.error;
      case ErrorSeverity.CRITICAL:
        return this.logger.error;
      default:
        return this.logger.warn;
    }
  }

  /**
   * 尝试恢复
   */
  async attemptRecovery(error) {
    const recoveryHandler = this.recoveryHandlers.get(error.type);
    if (recoveryHandler) {
      try {
        this.logger.info(`尝试从错误恢复: ${error.type}`);
        const result = await recoveryHandler(error);
        if (result) {
          this.logger.info(`错误恢复成功: ${error.type}`);
          this.emit('error-recovered', error);
          return true;
        }
      } catch (recoveryError) {
        this.logger.exception(recoveryError, '错误恢复失败');
        throw recoveryError;
      }
    }
    return false;
  }

  /**
   * 尝试重试
   */
  async attemptRetry(error, options) {
    const retryStrategy = this.retryStrategies.get(error.type);
    if (retryStrategy && options.retryContext) {
      try {
        this.logger.info(`尝试重试操作: ${error.type}`);
        await retryStrategy(error, options.retryContext);
        this.emit('error-retried', error);
      } catch (retryError) {
        this.logger.exception(retryError, '重试失败');
        throw retryError;
      }
    }
  }

  /**
   * 通知用户
   */
  notifyUser(error) {
    const userMessage = this.getUserFriendlyMessage(error);
    
    // 发送通知事件
    this.emit('user-notification-required', {
      message: userMessage,
      type: 'error',
      severity: error.severity
    });

    // 如果是Chrome扩展环境，显示通知
    if (typeof chrome !== 'undefined' && chrome.notifications) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: '携程IM拦截器错误',
        message: userMessage
      });
    }
  }

  /**
   * 获取用户友好的错误消息
   */
  getUserFriendlyMessage(error) {
    const errorMessages = {
      [ErrorTypes.NETWORK]: '网络连接异常，请检查网络设置',
      [ErrorTypes.WEBSOCKET]: 'WebSocket连接失败，正在尝试重连',
      [ErrorTypes.PERMISSION]: '权限不足，请检查扩展权限设置',
      [ErrorTypes.STORAGE]: '数据存储失败，请检查存储空间',
      [ErrorTypes.CONFIGURATION]: '配置错误，请检查设置',
      [ErrorTypes.PARSING]: '数据解析失败，可能是格式错误',
      [ErrorTypes.VALIDATION]: '数据验证失败，请检查输入',
      [ErrorTypes.TIMEOUT]: '操作超时，请稍后重试',
      [ErrorTypes.MEMORY]: '内存不足，建议关闭其他标签页',
      [ErrorTypes.UNKNOWN]: '发生未知错误，请查看详细日志'
    };

    return errorMessages[error.type] || errorMessages[ErrorTypes.UNKNOWN];
  }

  /**
   * 注册重试策略
   */
  registerRetryStrategy(errorType, strategyFunction) {
    this.retryStrategies.set(errorType, strategyFunction);
  }

  /**
   * 注册恢复处理器
   */
  registerRecoveryHandler(errorType, handlerFunction) {
    this.recoveryHandlers.set(errorType, handlerFunction);
  }

  /**
   * 获取错误统计
   */
  getErrorStats() {
    const stats = {
      totalErrors: this.errorHistory.length,
      errorsByType: {},
      errorsBySeverity: {},
      recentErrors: this.errorHistory.slice(-10),
      topErrors: this.getTopErrors()
    };

    // 按类型统计
    for (const error of this.errorHistory) {
      stats.errorsByType[error.type] = (stats.errorsByType[error.type] || 0) + 1;
      stats.errorsBySeverity[error.severity] = (stats.errorsBySeverity[error.severity] || 0) + 1;
    }

    return stats;
  }

  /**
   * 获取最常见的错误
   */
  getTopErrors() {
    const errorCounts = new Map();
    
    for (const error of this.errorHistory) {
      const key = `${error.type}: ${error.message}`;
      errorCounts.set(key, (errorCounts.get(key) || 0) + 1);
    }

    return Array.from(errorCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([error, count]) => ({ error, count }));
  }

  /**
   * 清理错误历史
   */
  clearErrorHistory() {
    this.errorHistory = [];
    this.errorCounts.clear();
    this.logger.info('错误历史已清理');
  }

  /**
   * 导出错误报告
   */
  exportErrorReport() {
    return {
      timestamp: Date.now(),
      version: '1.0.0',
      stats: this.getErrorStats(),
      errors: this.errorHistory.map(error => error.toJSON())
    };
  }
}

// 创建全局错误处理器实例
export const globalErrorHandler = new ErrorHandler();

export default ErrorHandler; 