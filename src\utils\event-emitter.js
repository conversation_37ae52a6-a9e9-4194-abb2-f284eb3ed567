/**
 * 事件发射器
 * 提供事件发射和监听功能
 */
export class EventEmitter {
  constructor() {
    this.events = new Map();
  }

  /**
   * 监听事件
   */
  on(event, listener) {
    if (!this.events.has(event)) {
      this.events.set(event, new Set());
    }
    this.events.get(event).add(listener);
  }

  /**
   * 监听事件（一次性）
   */
  once(event, listener) {
    const onceListener = (...args) => {
      listener(...args);
      this.off(event, onceListener);
    };
    this.on(event, onceListener);
  }

  /**
   * 移除事件监听器
   */
  off(event, listener) {
    if (this.events.has(event)) {
      this.events.get(event).delete(listener);
      if (this.events.get(event).size === 0) {
        this.events.delete(event);
      }
    }
  }

  /**
   * 发射事件
   */
  emit(event, ...args) {
    if (this.events.has(event)) {
      this.events.get(event).forEach(listener => {
        try {
          listener(...args);
        } catch (error) {
          console.error('事件监听器执行错误:', error);
        }
      });
    }
  }

  /**
   * 移除所有事件监听器
   */
  removeAllListeners(event = null) {
    if (event) {
      this.events.delete(event);
    } else {
      this.events.clear();
    }
  }

  /**
   * 获取事件监听器数量
   */
  listenerCount(event) {
    return this.events.has(event) ? this.events.get(event).size : 0;
  }

  /**
   * 获取所有事件名
   */
  eventNames() {
    return Array.from(this.events.keys());
  }
} 