/**
 * @fileoverview 日志记录工具
 * <AUTHOR> Extension Developer
 */

import { APP_CONFIG, LOG_LEVELS } from './constants.js';

/**
 * 日志记录器类
 */
class Logger {
  /**
   * 构造函数
   * @param {string} module - 模块名称
   * @param {string} level - 日志级别
   */
  constructor(module, level = LOG_LEVELS.INFO) {
    this.module = module;
    this.level = level;
    this.logLevels = {
      [LOG_LEVELS.DEBUG]: 0,
      [LOG_LEVELS.INFO]: 1,
      [LOG_LEVELS.WARN]: 2,
      [LOG_LEVELS.ERROR]: 3
    };
  }

  /**
   * 格式化日志消息
   * @param {string} level - 日志级别
   * @param {Array} args - 参数列表
   * @returns {Array} 格式化后的参数
   */
  formatMessage(level, args) {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${APP_CONFIG.NAME}] [${this.module}] [${level.toUpperCase()}]`;
    return [prefix, ...args];
  }

  /**
   * 检查是否应该记录该级别的日志
   * @param {string} level - 日志级别
   * @returns {boolean}
   */
  shouldLog(level) {
    return this.logLevels[level] >= this.logLevels[this.level];
  }

  /**
   * 记录调试信息
   * @param {...any} args - 参数列表
   */
  debug(...args) {
    if (this.shouldLog(LOG_LEVELS.DEBUG)) {
      console.debug(...this.formatMessage(LOG_LEVELS.DEBUG, args));
    }
  }

  /**
   * 记录信息
   * @param {...any} args - 参数列表
   */
  info(...args) {
    if (this.shouldLog(LOG_LEVELS.INFO)) {
      console.info(...this.formatMessage(LOG_LEVELS.INFO, args));
    }
  }

  /**
   * 记录警告
   * @param {...any} args - 参数列表
   */
  warn(...args) {
    if (this.shouldLog(LOG_LEVELS.WARN)) {
      console.warn(...this.formatMessage(LOG_LEVELS.WARN, args));
    }
  }

  /**
   * 记录错误
   * @param {...any} args - 参数列表
   */
  error(...args) {
    if (this.shouldLog(LOG_LEVELS.ERROR)) {
      console.error(...this.formatMessage(LOG_LEVELS.ERROR, args));
    }
  }

  /**
   * 记录性能信息
   * @param {string} operation - 操作名称
   * @param {number} startTime - 开始时间
   * @param {number} endTime - 结束时间
   */
  performance(operation, startTime, endTime) {
    const duration = endTime - startTime;
    this.info(`性能监控: ${operation} 耗时 ${duration}ms`);
  }

  /**
   * 记录带有错误堆栈的错误
   * @param {Error} error - 错误对象
   * @param {string} context - 上下文信息
   */
  exception(error, context = '') {
    this.error(`异常${context ? ` (${context})` : ''}:`, error.message);
    if (error.stack) {
      this.error('堆栈信息:', error.stack);
    }
  }

  /**
   * 创建性能计时器
   * @param {string} operation - 操作名称
   * @returns {Function} 结束计时的函数
   */
  createTimer(operation) {
    const startTime = performance.now();
    this.debug(`开始计时: ${operation}`);
    
    return () => {
      const endTime = performance.now();
      this.performance(operation, startTime, endTime);
    };
  }

  /**
   * 记录函数调用
   * @param {string} functionName - 函数名称
   * @param {Array} args - 参数列表
   */
  trace(functionName, args = []) {
    this.debug(`函数调用: ${functionName}`, args.length > 0 ? args : '');
  }

  /**
   * 设置日志级别
   * @param {string} level - 新的日志级别
   */
  setLevel(level) {
    if (this.logLevels.hasOwnProperty(level)) {
      this.level = level;
      this.info(`日志级别已设置为: ${level}`);
    } else {
      this.warn(`无效的日志级别: ${level}`);
    }
  }

  /**
   * 获取当前日志级别
   * @returns {string} 当前日志级别
   */
  getLevel() {
    return this.level;
  }
}

/**
 * 创建日志记录器实例
 * @param {string} module - 模块名称
 * @param {string} level - 日志级别
 * @returns {Logger} 日志记录器实例
 */
export function createLogger(module, level = LOG_LEVELS.INFO) {
  return new Logger(module, level);
}

/**
 * 默认后台日志记录器
 */
export const backgroundLogger = createLogger('Background');

/**
 * 内容脚本日志记录器
 */
export const contentLogger = createLogger('Content');

/**
 * 弹窗日志记录器
 */
export const popupLogger = createLogger('Popup');

/**
 * 消息处理器日志记录器
 */
export const messageLogger = createLogger('MessageHandler');

/**
 * 存储管理器日志记录器
 */
export const storageLogger = createLogger('StorageManager');

/**
 * 定时任务日志记录器
 */
export const alarmLogger = createLogger('AlarmManager');

/**
 * 通知管理器日志记录器
 */
export const notificationLogger = createLogger('NotificationManager');

/**
 * 上下文菜单日志记录器
 */
export const contextMenuLogger = createLogger('ContextMenu');

/**
 * 统计管理器日志记录器
 */
export const statsLogger = createLogger('StatsManager');

/**
 * 日志记录器工厂
 */
export class LoggerFactory {
  /**
   * 创建模块特定的日志记录器
   * @param {string} module - 模块名称
   * @param {string} level - 日志级别
   * @returns {Logger} 日志记录器实例
   */
  static create(module, level = LOG_LEVELS.INFO) {
    return new Logger(module, level);
  }

  /**
   * 获取所有可用的日志级别
   * @returns {Array<string>} 日志级别数组
   */
  static getAvailableLevels() {
    return Object.values(LOG_LEVELS);
  }

  /**
   * 验证日志级别是否有效
   * @param {string} level - 日志级别
   * @returns {boolean} 是否有效
   */
  static isValidLevel(level) {
    return Object.values(LOG_LEVELS).includes(level);
  }
}

/**
 * 全局错误处理器
 */
export class GlobalErrorHandler {
  /**
   * 构造函数
   * @param {Logger} logger - 日志记录器
   */
  constructor(logger) {
    this.logger = logger;
    this.setupErrorListeners();
  }

  /**
   * 设置全局错误监听器
   */
  setupErrorListeners() {
    // 监听未捕获的错误
    if (typeof self !== 'undefined') {
      self.addEventListener('error', (event) => {
        this.logger.exception(event.error, '全局错误');
      });

      // 监听未处理的Promise拒绝
      self.addEventListener('unhandledrejection', (event) => {
        this.logger.exception(event.reason, '未处理的Promise拒绝');
      });
    }
  }

  /**
   * 手动记录错误
   * @param {Error} error - 错误对象
   * @param {string} context - 上下文信息
   */
  logError(error, context = '') {
    this.logger.exception(error, context);
  }
}

/**
 * 导出全局错误处理器实例
 */
export const globalErrorHandler = new GlobalErrorHandler(backgroundLogger);

export default Logger; 