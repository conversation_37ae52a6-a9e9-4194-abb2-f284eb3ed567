/**
 * 性能优化工具模块
 * 提供缓存、节流、防抖等性能优化功能
 */
import { createLogger } from './logger.js';

export class PerformanceUtils {
  static logger = createLogger('PerformanceUtils');

  /**
   * 创建节流函数
   * @param {Function} func - 要节流的函数
   * @param {number} wait - 等待时间(毫秒)
   * @returns {Function} 节流后的函数
   */
  static throttle(func, wait) {
    let timeout;
    let previous = 0;
    
    return function throttled(...args) {
      const now = Date.now();
      const remaining = wait - (now - previous);
      
      if (remaining <= 0 || remaining > wait) {
        if (timeout) {
          clearTimeout(timeout);
          timeout = null;
        }
        previous = now;
        return func.apply(this, args);
      } else if (!timeout) {
        timeout = setTimeout(() => {
          previous = Date.now();
          timeout = null;
          func.apply(this, args);
        }, remaining);
      }
    };
  }

  /**
   * 创建防抖函数
   * @param {Function} func - 要防抖的函数
   * @param {number} wait - 等待时间(毫秒)
   * @param {boolean} immediate - 是否立即执行
   * @returns {Function} 防抖后的函数
   */
  static debounce(func, wait, immediate = false) {
    let timeout;
    
    return function debounced(...args) {
      const later = () => {
        timeout = null;
        if (!immediate) func.apply(this, args);
      };
      
      const callNow = immediate && !timeout;
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
      
      if (callNow) func.apply(this, args);
    };
  }

  /**
   * 内存缓存类
   */
  static Cache = class {
    constructor(maxSize = 100, ttl = 300000) { // 默认5分钟TTL
      this.cache = new Map();
      this.timers = new Map();
      this.maxSize = maxSize;
      this.ttl = ttl;
    }

    set(key, value, customTTL) {
      // 清理超出大小限制的缓存
      if (this.cache.size >= this.maxSize) {
        const firstKey = this.cache.keys().next().value;
        this.delete(firstKey);
      }

      // 设置新值
      this.cache.set(key, {
        value,
        timestamp: Date.now()
      });

      // 设置过期清理
      const expiry = customTTL || this.ttl;
      if (this.timers.has(key)) {
        clearTimeout(this.timers.get(key));
      }

      this.timers.set(key, setTimeout(() => {
        this.delete(key);
      }, expiry));

      return this;
    }

    get(key) {
      const item = this.cache.get(key);
      if (!item) return undefined;

      // 检查是否过期
      if (Date.now() - item.timestamp > this.ttl) {
        this.delete(key);
        return undefined;
      }

      return item.value;
    }

    has(key) {
      return this.cache.has(key) && this.get(key) !== undefined;
    }

    delete(key) {
      if (this.timers.has(key)) {
        clearTimeout(this.timers.get(key));
        this.timers.delete(key);
      }
      return this.cache.delete(key);
    }

    clear() {
      for (const timer of this.timers.values()) {
        clearTimeout(timer);
      }
      this.timers.clear();
      this.cache.clear();
    }

    size() {
      return this.cache.size;
    }

    keys() {
      return this.cache.keys();
    }

    values() {
      return Array.from(this.cache.values()).map(item => item.value);
    }
  };

  /**
   * 批处理工具
   */
  static BatchProcessor = class {
    constructor(processor, batchSize = 10, delay = 100) {
      this.processor = processor;
      this.batchSize = batchSize;
      this.delay = delay;
      this.queue = [];
      this.timer = null;
    }

    add(item) {
      this.queue.push(item);
      this.schedule();
    }

    schedule() {
      if (this.timer) return;

      if (this.queue.length >= this.batchSize) {
        this.process();
      } else {
        this.timer = setTimeout(() => {
          this.process();
        }, this.delay);
      }
    }

    async process() {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }

      if (this.queue.length === 0) return;

      const batch = this.queue.splice(0, this.batchSize);
      
      try {
        await this.processor(batch);
      } catch (error) {
        PerformanceUtils.logger.error('批处理失败:', error);
      }

      // 如果还有待处理的项目，继续处理
      if (this.queue.length > 0) {
        this.schedule();
      }
    }

    clear() {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      this.queue = [];
    }
  };

  /**
   * 性能监控器
   */
  static PerformanceMonitor = class {
    constructor(name) {
      this.name = name;
      this.marks = new Map();
      this.measures = new Map();
    }

    mark(label) {
      const timestamp = performance.now();
      this.marks.set(label, timestamp);
      return timestamp;
    }

    measure(name, startMark, endMark) {
      const start = this.marks.get(startMark);
      const end = this.marks.get(endMark) || performance.now();
      
      if (start === undefined) {
        PerformanceUtils.logger.warn(`开始标记不存在: ${startMark}`);
        return null;
      }

      const duration = end - start;
      this.measures.set(name, duration);
      
      PerformanceUtils.logger.debug(`性能测量 [${this.name}] ${name}: ${duration.toFixed(2)}ms`);
      return duration;
    }

    getMeasure(name) {
      return this.measures.get(name);
    }

    getAllMeasures() {
      return Object.fromEntries(this.measures);
    }

    clear() {
      this.marks.clear();
      this.measures.clear();
    }
  };

  /**
   * 内存使用监控
   */
  static MemoryMonitor = class {
    static getMemoryUsage() {
      if (performance.memory) {
        return {
          used: performance.memory.usedJSHeapSize,
          total: performance.memory.totalJSHeapSize,
          limit: performance.memory.jsHeapSizeLimit,
          timestamp: Date.now()
        };
      }
      return null;
    }

    static logMemoryUsage(context = '') {
      const usage = this.getMemoryUsage();
      if (usage) {
        const usedMB = (usage.used / 1024 / 1024).toFixed(2);
        const totalMB = (usage.total / 1024 / 1024).toFixed(2);
        const limitMB = (usage.limit / 1024 / 1024).toFixed(2);
        
        PerformanceUtils.logger.debug(
          `内存使用 ${context}: ${usedMB}MB / ${totalMB}MB (限制: ${limitMB}MB)`
        );
      }
    }

    static isMemoryPressure() {
      const usage = this.getMemoryUsage();
      if (!usage) return false;
      
      const usageRatio = usage.used / usage.limit;
      return usageRatio > 0.8; // 超过80%认为内存压力较大
    }
  };

  /**
   * 函数执行时间测量装饰器
   */
  static measureTime(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = function(...args) {
      const startTime = performance.now();
      const result = originalMethod.apply(this, args);
      const endTime = performance.now();
      
      PerformanceUtils.logger.debug(
        `方法执行时间 [${target.constructor.name}.${propertyKey}]: ${(endTime - startTime).toFixed(2)}ms`
      );
      
      return result;
    };
    
    return descriptor;
  }

  /**
   * 异步函数执行时间测量装饰器
   */
  static measureAsyncTime(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function(...args) {
      const startTime = performance.now();
      const result = await originalMethod.apply(this, args);
      const endTime = performance.now();
      
      PerformanceUtils.logger.debug(
        `异步方法执行时间 [${target.constructor.name}.${propertyKey}]: ${(endTime - startTime).toFixed(2)}ms`
      );
      
      return result;
    };
    
    return descriptor;
  }

  /**
   * 通用优化工具
   */
  static optimizeForPerformance() {
    // 启用被动事件监听器优化
    const supportsPassive = (() => {
      let supportsPassive = false;
      try {
        const opts = Object.defineProperty({}, 'passive', {
          get() {
            supportsPassive = true;
          }
        });
        window.addEventListener('testPassive', null, opts);
        window.removeEventListener('testPassive', null, opts);
      } catch (e) {}
      return supportsPassive;
    })();

    return {
      supportsPassive,
      addPassiveEventListener: (element, event, handler) => {
        element.addEventListener(event, handler, supportsPassive ? { passive: true } : false);
      }
    };
  }
}

export default PerformanceUtils; 