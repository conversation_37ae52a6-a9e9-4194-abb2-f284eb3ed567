<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>携程IM拦截器 - 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 携程IM拦截器测试页面</h1>
            <p>用于测试Chrome扩展的popup功能和tab切换</p>
        </div>

        <div class="test-section">
            <h3>📋 测试说明</h3>
            <p>1. 确保已安装并启用携程IM拦截器扩展</p>
            <p>2. 点击浏览器工具栏中的扩展图标打开popup</p>
            <p>3. 测试各个tab页面的切换功能</p>
            <p>4. 检查状态显示是否正常</p>
        </div>

        <div class="test-section">
            <h3>🔧 模拟携程网站环境</h3>
            <p>当前页面URL: <code id="currentUrl"></code></p>
            <button onclick="simulateCtripSite()">模拟携程网站</button>
            <button onclick="createWebSocket()">创建WebSocket连接</button>
            <button onclick="sendTestMessage()">发送测试消息</button>
            <div id="websocketStatus" class="status warning">WebSocket状态: 未连接</div>
        </div>

        <div class="test-section">
            <h3>📊 扩展状态检查</h3>
            <button onclick="checkExtensionStatus()">检查扩展状态</button>
            <button onclick="testContentScript()">测试Content Script</button>
            <div id="extensionStatus" class="status warning">点击按钮检查扩展状态</div>
        </div>

        <div class="test-section">
            <h3>📝 测试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="testLog" class="log">等待测试操作...</div>
        </div>
    </div>

    <script>
        let testWebSocket = null;
        
        // 显示当前URL
        document.getElementById('currentUrl').textContent = window.location.href;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[Test Page] ${message}`);
        }
        
        function clearLog() {
            document.getElementById('testLog').textContent = '';
        }
        
        function simulateCtripSite() {
            // 修改页面标题和一些元素来模拟携程网站
            document.title = '携程旅行网 - Ctrip.com';
            log('✅ 已模拟携程网站环境');
            
            // 添加一些携程相关的元素
            if (!document.querySelector('.ctrip-header')) {
                const header = document.createElement('div');
                header.className = 'ctrip-header';
                header.style.display = 'none';
                document.body.appendChild(header);
            }
        }
        
        function createWebSocket() {
            try {
                // 创建一个测试WebSocket连接
                testWebSocket = new WebSocket('wss://echo.websocket.org/');
                
                testWebSocket.onopen = function(event) {
                    log('✅ WebSocket连接已建立');
                    updateWebSocketStatus('已连接', 'success');
                };
                
                testWebSocket.onmessage = function(event) {
                    log(`📨 收到WebSocket消息: ${event.data}`);
                };
                
                testWebSocket.onclose = function(event) {
                    log('❌ WebSocket连接已关闭');
                    updateWebSocketStatus('已断开', 'error');
                };
                
                testWebSocket.onerror = function(error) {
                    log('❌ WebSocket连接错误: ' + error);
                    updateWebSocketStatus('连接错误', 'error');
                };
                
                log('🔄 正在创建WebSocket连接...');
                updateWebSocketStatus('连接中...', 'warning');
                
            } catch (error) {
                log('❌ 创建WebSocket失败: ' + error.message);
                updateWebSocketStatus('创建失败', 'error');
            }
        }
        
        function sendTestMessage() {
            if (testWebSocket && testWebSocket.readyState === WebSocket.OPEN) {
                const message = `测试消息 - ${new Date().toLocaleTimeString()}`;
                testWebSocket.send(message);
                log(`📤 发送测试消息: ${message}`);
            } else {
                log('❌ WebSocket未连接，无法发送消息');
            }
        }
        
        function updateWebSocketStatus(status, type) {
            const statusDiv = document.getElementById('websocketStatus');
            statusDiv.textContent = `WebSocket状态: ${status}`;
            statusDiv.className = `status ${type}`;
        }
        
        function checkExtensionStatus() {
            log('🔍 检查扩展状态...');
            
            // 检查是否有扩展注入的脚本
            const hasContentScript = !!window.ctripIMContentScript;
            const hasInterceptor = !!window._ctripWebSocketInterceptorInstalled;
            
            log(`Content Script: ${hasContentScript ? '✅ 已加载' : '❌ 未加载'}`);
            log(`WebSocket拦截器: ${hasInterceptor ? '✅ 已安装' : '❌ 未安装'}`);
            
            const statusDiv = document.getElementById('extensionStatus');
            if (hasContentScript && hasInterceptor) {
                statusDiv.textContent = '扩展状态: 正常运行';
                statusDiv.className = 'status success';
            } else {
                statusDiv.textContent = '扩展状态: 未完全加载';
                statusDiv.className = 'status error';
            }
        }
        
        function testContentScript() {
            log('🧪 测试Content Script通信...');
            
            if (window.ctripIMContentScript) {
                try {
                    // 尝试获取状态
                    const status = window.ctripIMContentScript.getStatus();
                    log(`📊 Content Script状态: ${JSON.stringify(status)}`);
                } catch (error) {
                    log(`❌ Content Script测试失败: ${error.message}`);
                }
            } else {
                log('❌ Content Script未加载');
            }
        }
        
        // 页面加载完成后自动检查
        window.addEventListener('load', function() {
            log('🚀 测试页面加载完成');
            setTimeout(checkExtensionStatus, 1000);
        });
    </script>
</body>
</html>
