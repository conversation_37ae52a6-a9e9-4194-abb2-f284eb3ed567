const path = require('path');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');

module.exports = (env, argv) => {
  const isDev = argv.mode === 'development';
  
  return {
    mode: isDev ? 'development' : 'production',
    devtool: isDev ? 'cheap-module-source-map' : false,
    
    entry: {
      // WebSocket拦截器 - 独立早期执行，不需要打包依赖
      'content/websocket-interceptor': './src/content/websocket-interceptor.js',
      
      // Content Script 主文件 - 合并相关模块
      'content/content-script': [
        './src/content/content-script.js',
        './src/content/connection-manager.js',
        './src/message-parser/message-processor.js',
        './src/utils/config.js',
        './src/utils/constants.js',
        './src/utils/error-handler.js',
        './src/utils/event-emitter.js',
        './src/utils/logger.js',
        './src/utils/performance-utils.js'
      ],
      
      // Background 模块
      'background/service-worker': './src/background/service-worker.js',
      'background/alarm-manager': './src/background/alarm-manager.js',
      'background/context-menu': './src/background/context-menu.js',
      'background/message-handler': './src/background/message-handler.js',
      'background/notification-manager': './src/background/notification-manager.js',
      
      // Popup 模块
      'popup/popup-manager': './src/popup/popup-manager.js',
      
      // 独立模块
      'auto-reply/auto-reply-engine': './src/auto-reply/auto-reply-engine.js',
      'message-parser/message-processor': './src/message-parser/message-processor.js',
      
      // Utils 模块
      'utils/config': './src/utils/config.js',
      'utils/constants': './src/utils/constants.js',
      'utils/error-handler': './src/utils/error-handler.js',
      'utils/event-emitter': './src/utils/event-emitter.js',
      'utils/logger': './src/utils/logger.js',
      'utils/performance-utils': './src/utils/performance-utils.js'
    },
    
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: '[name]-bundle.js',
      clean: true,
    },
    
    module: {
      rules: [
        {
          test: /\.js$/,
          exclude: /node_modules/,
          oneOf: [
            {
              // WebSocket拦截器 - 立即执行函数，不使用ES模块
              include: [
                path.resolve(__dirname, 'src/content/websocket-interceptor.js')
              ],
              use: {
                loader: 'babel-loader',
                options: {
                  presets: [
                    ['@babel/preset-env', {
                      targets: {
                        chrome: '88'
                      },
                      modules: false, // 不转换模块，保持原样
                      useBuiltIns: false, // 不添加polyfill
                      corejs: false
                    }]
                  ]
                }
              }
            },
            {
              // Content Scripts 使用 CommonJS
              include: [
                path.resolve(__dirname, 'src/content')
              ],
              use: {
                loader: 'babel-loader',
                options: {
                  presets: [
                    ['@babel/preset-env', {
                      targets: {
                        chrome: '88'
                      },
                      modules: false, // 改为false，使用ES modules
                      useBuiltIns: 'usage',
                      corejs: 3
                    }]
                  ]
                }
              }
            },
            {
              // 其他模块使用 ES modules
              use: {
                loader: 'babel-loader',
                options: {
                  presets: [
                    ['@babel/preset-env', {
                      targets: {
                        chrome: '88'
                      },
                      modules: false,
                      useBuiltIns: 'usage',
                      corejs: 3
                    }]
                  ]
                }
              }
            }
          ]
        },
        {
          test: /\.css$/,
          use: ['style-loader', 'css-loader']
        }
      ]
    },
    
    plugins: [
      new CleanWebpackPlugin(),
      new CopyWebpackPlugin({
        patterns: [
          // 复制图标
          { from: 'src/assets/icons', to: 'assets/icons' },
          // 复制 popup 相关文件
          { 
            from: 'src/popup/popup.html', 
            to: 'popup/popup.html',
            transform(content) {
              return content
                .toString()
                .replace(
                  /popup-manager\.js/,
                  'popup-manager-bundle.js'
                );
            }
          },
          { from: 'src/popup/popup.css', to: 'popup/popup.css' },
          // 复制文档和其他静态文件
          { from: 'src/docs', to: 'docs', noErrorOnMissing: true },
          // 复制并修改 manifest.json
          { 
            from: 'src/manifest.json', 
            to: 'manifest.json',
            transform(content) {
              const manifest = JSON.parse(content.toString());
              // 修改 service worker 路径
              manifest.background.service_worker = manifest.background.service_worker.replace('.js', '-bundle.js');
              // 修改 content scripts 路径
              if (manifest.content_scripts && manifest.content_scripts.length >= 2) {
                // 第一个是websocket-interceptor
                manifest.content_scripts[0].js = ['content/websocket-interceptor-bundle.js'];
                // 第二个是主content-script
                manifest.content_scripts[1].js = ['content/content-script-bundle.js'];
              }
              // 保持 background 的 module 类型
              manifest.background.type = 'module';
              return JSON.stringify(manifest, null, 2);
            }
          }
        ]
      })
    ],
    
    resolve: {
      extensions: ['.js', '.json'],
      alias: {
        '@': path.resolve(__dirname, 'src'),
        '@utils': path.resolve(__dirname, 'src/utils'),
        '@background': path.resolve(__dirname, 'src/background'),
        '@content': path.resolve(__dirname, 'src/content'),
        '@auto-reply': path.resolve(__dirname, 'src/auto-reply'),
        '@message-parser': path.resolve(__dirname, 'src/message-parser'),
        '@popup': path.resolve(__dirname, 'src/popup')
      }
    },
    
    optimization: {
      minimize: !isDev,
      splitChunks: {
        cacheGroups: {
          defaultVendors: false,
          default: false
        }
      },
      runtimeChunk: false
    },
    
    experiments: {
      outputModule: true
    },
    
    ...(isDev && {
      cache: {
        type: 'filesystem'
      },
      stats: {
        modules: false,
        chunks: false,
        chunkModules: false,
        colors: true,
        builtAt: true
      }
    })
  };
}; 